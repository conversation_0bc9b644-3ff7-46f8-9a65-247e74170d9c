# 微信结算补偿信息同步任务

## 任务概述
创建新的同步类，用于同步Oracle数据库中的`WECHATAPI.WECHAT_SETTLED_COMPENSATION`表到MySQL数据库，参考现有的合同信息同步逻辑。

## 上下文信息
- 原始表：Oracle `WECHATAPI.WECHAT_SETTLED_COMPENSATION`
- 目标表：MySQL `CX_GQ_WECHAT_SETTLED_COMPENSATION`
- 同步字段：通过`ETL_DATE`字段进行时间基准的增量同步
- 主键：`CONTRACT_NUMBER`

## 执行计划

### 1. 常量定义更新
- 文件：`src/com/yunqu/cc/mixgw/base/Constants.java`
- 添加：`SYNC_COMMAND_06`、`IS_SYN_WECHAT_SETTLED_COMPENSATION`

### 2. 创建同步工具类
- 文件：`src/com/yunqu/cc/mixgw/inf/thread/SynWechatSettledCompensationUtil.java`
- 参考：`SynRetailInfoUtil.java`的完整实现模式

### 3. 创建临时表SQL
- 文件：`WebContent/META-INF/script/script_mysql_20250702.sql`
- 添加：`CX_GQ_WECHAT_SETTLED_COMPENSATION_TMP`表

### 4. 更新字典初始化
- 文件：`src/com/yunqu/cc/mixgw/inf/RegInitDataService.java`
- 添加新的同步类型到字典

## 实现状态
- [ ] 常量定义更新
- [ ] 同步工具类创建
- [ ] 临时表SQL添加
- [ ] 字典初始化更新

## 开始时间
2025-07-03
