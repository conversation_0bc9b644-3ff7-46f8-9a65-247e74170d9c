<?xml version="1.0" encoding="UTF-8"?>
<resources>
	<resource id="cc-km" name="智能知识" url="" type="2" portal=""  icon="" state="0" order="4" entId = "0" index = "50">
		<resource id="cc-km-query-list" name="知识查询" url="/cc-km/pages/km/query/km-info-list.jsp" type="2" icon="" portal="" state="0" entId ="0"  index="1"/>
		<resource id="cc-km-manage" name="知识管理"  type="2" icon="" portal="" state="0" entId ="0"  index="2">
			<resource id="cc-km-manage-edit" name="资料库维护" url="/cc-km/pages/km/info/km-info-repair-list.jsp" type="2" icon="" portal="" state="0" entId ="0"  index="1"/>
			<!-- <resource id="cc-km-manage-question-scene" name="场景库维护" url="/cc-km/pages/km/question/scene/sceneList.jsp" type="2" icon="" portal="" state="0" entId ="0"  index="2"/> -->
			<resource id="cc-km-manage-question-faq" name="FAQ库维护" url="/cc-km/pages/km/question/faq/faqList.jsp" type="2" icon="" portal="" state="0" entId ="0"  index="3"/>
			<!-- <resource id="cc-km-manage-question-greeting" name="寒暄库维护" url="/cc-km/pages/km/question/greeting/greetingList.jsp" type="2" icon="" portal="" state="0" entId ="0"  index="4"/> -->
			<resource id="cc-km-manage-channel" name="知识渠道" url="/cc-km/pages/km/info/channel/km-info-channel.jsp" type="2" icon="" portal="" state="0" entId ="0"  index="5"/>
			<!-- <resource id="cc-km-manage-classify" name="业务分类" url="/cc-km/pages/km/classify/kmClassify-list.jsp" type="2" icon="" portal="" state="0" entId ="0"  index="6"/> -->
		</resource>
		<resource id="cc-km-optimize" name="知识优化" url=""  type="2" icon="" portal="" state="0" entId ="0"  index="3">
			<resource id="cc-km-optimize-noun" name="行业名词" url="/cc-km/pages/km/optimize/km-info-noun.jsp" type="2" icon="" portal="" state="0" entId ="0"  index="12"/>
			<resource id="cc-km-optimize-query-no-stat" name="查无结果优化" url="/cc-km/pages/km/optimize/km-info-query-no-list.jsp" type="2" icon="" portal="" state="0" entId ="0"  index="1"/>
			<resource id="cc-km-optimize-faq-record" name="解决情况优化" url="/cc-km/pages/km/optimize/km-info-solution-record.jsp" type="2" icon="" portal="" state="0" entId ="0"  index="2"/>
			<resource id="cc-km-optimize-feedback-parent" name="知识反馈" url="/cc-portal/pages/config/sys-config.jsp?resId=cc-km-optimize-feedback-parent"  type="2" icon="" portal="" state="0" entId ="0"  index="6">
				<resource id="cc-km-optimize-feedback" name="知识反馈" url="/cc-km/servlet/kmFeedbackInfo?action=agent" type="2" icon="" portal="" state="0" entId ="0"  index="6"/>
				<resource id="cc-km-optimize-feedback-manage" name="反馈管理" url="/cc-km/servlet/kmFeedbackInfo?action=admin" type="2" icon="" portal="" state="0" entId ="0"  index="7"/>
				<resource id="cc-km-optimize-feedback-deal" name="反馈处理" url="/cc-km/servlet/kmFeedbackInfo?action=dept" type="2" icon="" portal="" state="0" entId ="0"  index="8"/>
				<resource id="cc-km-optimize-feedback-info" name="知识反馈" url="/cc-km/pages/km/feedbackSimplify/km-feedback-info-list.jsp" type="2" icon="" portal="" state="0" entId ="0"  index="9"/>
			</resource>
		</resource>
		<resource id="cc-km-access" name="知识权限" url="/cc-portal/pages/config/sys-config.jsp?resId=cc-km-access"  type="2" icon="" portal="" state="0" entId ="0"  index="3">
			<resource id="cc-km-access-audit" name="知识审核" url="/cc-km/pages/km/access/km-info-audit-list.jsp" type="2" icon="" portal="" state="0" entId ="0"  index="1"/>
			<resource id="cc-km-access-role-config" name="角色权限配置" url="/cc-km/pages/km/access/role/km-role-list.jsp" type="2" icon="" portal="" state="0" entId ="0"  index="2"/>
			<resource id="cc-km-access-dept-config" name="部门权限配置" url="/cc-km/pages/km/access/dept/km-access-dept-list.jsp" type="2" icon="" portal="" state="0" entId ="0"  index="2"/>
		</resource>
		<resource id="cc-km-analyze" name="统计分析"  type="2" icon="" portal="" state="0" entId ="0"  index="4">
			<resource id="cc-km-analyze-query-stat" name="知识查询统计" url="/cc-km/pages/km/analyze/km-info-search-log.jsp" type="2" icon="" portal="" state="0" entId ="0"  index="3"/>
			<resource id="cc-km-analyze-oper-log" name="知识操作日志" url="/cc-km/pages/km/analyze/km-analyze-oper-log.jsp" type="2" icon="" portal="" state="0" entId ="0"  index="3"/>
			<resource id="cc-km-analyze-visit-stat" name="知识访问统计" url="/cc-km/pages/km/analyze/km-info-visit-count-list.jsp" type="2" icon="" portal="" state="0" entId ="0"  index="4"/>
			<resource id="cc-km-analyze-channel-stat" name="渠道访问统计" url="/cc-km/pages/km/analyze/km-view-log.jsp" type="2" icon="" portal="" state="0" entId ="0"  index="5"/>
			<resource id="cc-km-data-overview" name="知识整体分析" url="/cc-km/pages/km/analyze/dataOverview.html" type="2" icon="" portal="" state="0" entId ="0"  index="6"/>
		</resource>
		<resource id="cc-km-button-access" name="按钮权限"  type="2" icon="" portal="" state="0" entId ="0"  index="4">
			<resource id="cc-km-manage-edit-aysncAll" name="全量同步" url="" type="3" icon="" portal="" state="0" entId ="0"  index="1" />
			<resource id="cc-km-manage-edit-infoSuspend" name="暂停知识" url="" type="3" icon="" portal="" state="0" entId ="0"  index="2" />
			<resource id="cc-km-manage-edit-moveRecycle" name="移入回收站" url="" type="3" icon="" portal="" state="0" entId ="0"  index="3" />
			<resource id="cc-km-manage-moveInfo" name="移动知识" url="" type="3" icon="" portal="" state="0" entId ="0"  index="4" />
			<resource id="cc-km-manage-dirEdit" name="目录维护" url="" type="3" icon="" portal="" state="0" entId ="0"  index="4" />
			<resource id="cc-km-manage-removeInfo" name="删除知识" url="" type="3" icon="" portal="" state="0" entId ="0"  index="5" />
			<resource id="cc-km-manage-exportInfo" name="导出知识" url="" type="3" icon="" portal="" state="0" entId ="0"  index="6" />
			<resource id="cc-km-manage-importInfo" name="导入知识" url="" type="3" icon="" portal="" state="0" entId ="0"  index="7" />
			<resource id="cc-km-manage-removeTemplate" name="删除资料库模板" url="" type="3" icon="" portal="" state="0" entId ="0"  index="8" />
			<resource id="cc-km-manage-removeChannel" name="删除知识渠道" url="" type="3" icon="" portal="" state="0" entId ="0"  index="9" />
			<resource id="cc-km-manage-removeNoun" name="删除行业名词" url="" type="3" icon="" portal="" state="0" entId ="0"  index="10" />
			<resource id="cc-km-manage-importNoun" name="导入行业名词" url="" type="3" icon="" portal="" state="0" entId ="0"  index="11" />
			<resource id="cc-km-manage-exportNoun" name="导出行业名词" url="" type="3" icon="" portal="" state="0" entId ="0"  index="12" />
			<resource id="cc-km-manage-removeDeptAccess" name="删除部门权限" url="" type="3" icon="" portal="" state="0" entId ="0"  index="13" />
			<resource id="cc-km-manage-removeRoleAccess" name="删除角色权限" url="" type="3" icon="" portal="" state="0" entId ="0"  index="14" />
			<resource id="cc-km-manage-exportAnalyze" name="导出分析结果" url="" type="3" icon="" portal="" state="0" entId ="0"  index="15" />
			<resource id="cc-km-manage-nlpButton" name="NLP相关按钮" url="" type="3" icon="" portal="" state="0" entId ="0"  index="15" />
			<resource id="cc-km-manage-send-sms" name="知识分享" url="" type="3" icon="" portal="" state="0" entId ="0"  index="1" />
		</resource>
	</resource>
	<resource id="cc-base-system" name="系统管理" url="/cc-portal/pages/config/sys-config.jsp?resId=cc-base-system" type="2" portal="" state="0" order="4" icon="iconfont icon-setting" entId ="0" index = "80">
		<resource id="cc-base-system-xtpz" name="系统配置"  type="2" icon="" portal="" state="0" entId ="0"  index="5">
			<resource id="cc-base-system-xtpz-cspz" name="参数配置" url="/cc-portal/pages/config/sys-config.jsp?resId=cc-base-system-xtpz-cspz" type="2" icon="" portal="" state="0" entId ="0"  index="6">
				<resource id="cc-base-system-xtpz-cspz-km-info" name="智能知识参数配置" url="/cc-km/pages/param/param-info.jsp" type="2" icon="" portal="" state="0" entId ="0"  index="9"/>
			</resource>
		</resource>
	</resource>
</resources>
