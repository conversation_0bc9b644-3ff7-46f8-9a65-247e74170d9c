-- BI合同数据表建表语句
-- 创建时间: 2025-01-03
-- 说明: 根据BI字段规范创建，所有金额字段支持4位以上小数，时间字段使用VARCHAR

DROP TABLE IF EXISTS `bi_contract_data`;

CREATE TABLE `bi_contract_data` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `contract_number` VARCHAR(24) NOT NULL COMMENT '合同号',
  `rtl_tl_flag` VARCHAR(1) DEFAULT NULL COMMENT '是否TL合同',
  `request_status_dsc` VARCHAR(200) DEFAULT NULL COMMENT '合同状态',
  `is_hold_dd` VARCHAR(1) DEFAULT NULL COMMENT '是否hold DD',
  `unallocated_amt` DECIMAL(20,6) DEFAULT NULL COMMENT '未分配金额',
  `ttl_rental_overdue` DECIMAL(20,6) DEFAULT NULL COMMENT '逾期款',
  `interest_rate` DECIMAL(20,6) DEFAULT NULL COMMENT '利率',
  `distributor_name_chs` VARCHAR(200) DEFAULT NULL COMMENT '主机厂中文名称',
  `structured_rental_ind` VARCHAR(1) DEFAULT NULL COMMENT '是否灵活还款',
  `asset_condition_code` VARCHAR(10) DEFAULT NULL COMMENT '车辆类型',
  `settled_trm` DECIMAL(20,6) DEFAULT NULL COMMENT '已还期数',
  `asset_type` VARCHAR(100) DEFAULT NULL COMMENT '汽车标识',
  `lease_type` VARCHAR(10) DEFAULT NULL COMMENT '是否售后回租合同',
  `contract_activation_date` VARCHAR(50) DEFAULT NULL COMMENT '合同激活日期',
  `evnt_dte_time` VARCHAR(50) DEFAULT NULL COMMENT '申请通过日期',
  `ls_principal_outstanding_amt` DECIMAL(20,6) DEFAULT NULL COMMENT '下一期的剩余本金余额',
  `interest_amt` DECIMAL(20,6) DEFAULT NULL COMMENT '下一期利息',
  `principal_outstanding_amt` DECIMAL(20,6) DEFAULT NULL COMMENT '当期剩余本金',
  `source_code` VARCHAR(10) DEFAULT NULL COMMENT '数据来源',
  `etl_date` VARCHAR(50) DEFAULT NULL COMMENT '跑数时间',
  `financial_product_name` VARCHAR(255) DEFAULT NULL COMMENT '金融产品名称',
  `contract_trm` DECIMAL(20,6) DEFAULT NULL COMMENT '总期数',
  `due_day` VARCHAR(10) DEFAULT NULL COMMENT '还款日',
  `create_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_contract_number` (`contract_number`),
  KEY `idx_contract_activation_date` (`contract_activation_date`),
  KEY `idx_etl_date` (`etl_date`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='BI合同数据表';
