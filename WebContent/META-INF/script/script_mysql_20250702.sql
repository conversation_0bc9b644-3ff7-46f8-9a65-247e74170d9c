-- <PERSON><PERSON><PERSON><PERSON>
create table CX_GQ_WECHAT_SETTLED_COMPENSATION(
    CONTRACT_NUMBER VARCHAR(24) NOT NULL COMMENT '合同号',
    RTL_TL_FLAG VARCHAR(1) DEFAULT NULL COMMENT '是否TL合同',
    REQUEST_STATUS_DSC VARCHAR(200) DEFAULT NULL COMMENT '合同状态',
    IS_HOLD_DD VARCHAR(1) DEFAULT NULL COMMENT '是否HOLD DD',
    UNALLOCATED_AMT DECIMAL(20,6) DEFAULT NULL COMMENT '未分配金额',
    TTL_RENTAL_OVERDUE DECIMAL(20,6) DEFAULT NULL COMMENT '逾期款',
    INTEREST_RATE DECIMAL(20,6) DEFAULT NULL COMMENT '利率',
    DISTRIBUTOR_NAME_CHS VARCHAR(200) DEFAULT NULL COMMENT '主机厂中文名称',
    STRUCTURED_RENTAL_IND VARCHAR(1) DEFAULT NULL COMMENT '是否灵活还款',
    ASSET_CONDITION_CODE VARCHAR(10) DEFAULT NULL COMMENT '车辆类型',
    SETTLED_TRM DECIMAL(20,6) DEFAULT NULL COMMENT '已还期数',
    ASSET_TYPE VARCHAR(100) DEFAULT NULL COMMENT '汽车标识',
    LEASE_TYPE VARCHAR(10) DEFAULT NULL COMMENT '是否售后回租合同',
    CONTRACT_ACTIVATION_DATE VARCHAR(50) DEFAULT NULL COMMENT '合同激活日期',
    EVNT_DTE_TIME VARCHAR(50) DEFAULT NULL COMMENT '申请通过日期',
    LS_PRINCIPAL_OUTSTANDING_AMT DECIMAL(20,6) DEFAULT NULL COMMENT '下一期的剩余本金余额',
    INTEREST_AMT DECIMAL(20,6) DEFAULT NULL COMMENT '下一期利息',
    PRINCIPAL_OUTSTANDING_AMT DECIMAL(20,6) DEFAULT NULL COMMENT '当期剩余本金',
    SOURCE_CODE VARCHAR(10) DEFAULT NULL COMMENT '数据来源',
    ETL_DATE VARCHAR(50) DEFAULT NULL COMMENT '跑数时间',
    FINANCIAL_PRODUCT_NAME VARCHAR(255) DEFAULT NULL COMMENT '金融产品名称',
    CONTRACT_TRM DECIMAL(20,6) DEFAULT NULL COMMENT '总期数',
    DUE_DAY VARCHAR(10) DEFAULT NULL COMMENT '还款日',
    PRIMARY KEY (CONTRACT_NUMBER),
    KEY IDX_ETL_DATE(ETL_DATE)
);

-- 微信结算补偿信息临时表
create table CX_GQ_WECHAT_SETTLED_COMPENSATION_TMP(
    CONTRACT_NUMBER VARCHAR(24) NOT NULL COMMENT '合同号',
    RTL_TL_FLAG VARCHAR(1) DEFAULT NULL COMMENT '是否TL合同',
    REQUEST_STATUS_DSC VARCHAR(200) DEFAULT NULL COMMENT '合同状态',
    IS_HOLD_DD VARCHAR(1) DEFAULT NULL COMMENT '是否HOLD DD',
    UNALLOCATED_AMT DECIMAL(20,6) DEFAULT NULL COMMENT '未分配金额',
    TTL_RENTAL_OVERDUE DECIMAL(20,6) DEFAULT NULL COMMENT '逾期款',
    INTEREST_RATE DECIMAL(20,6) DEFAULT NULL COMMENT '利率',
    DISTRIBUTOR_NAME_CHS VARCHAR(200) DEFAULT NULL COMMENT '主机厂中文名称',
    STRUCTURED_RENTAL_IND VARCHAR(1) DEFAULT NULL COMMENT '是否灵活还款',
    ASSET_CONDITION_CODE VARCHAR(10) DEFAULT NULL COMMENT '车辆类型',
    SETTLED_TRM DECIMAL(20,6) DEFAULT NULL COMMENT '已还期数',
    ASSET_TYPE VARCHAR(100) DEFAULT NULL COMMENT '汽车标识',
    LEASE_TYPE VARCHAR(10) DEFAULT NULL COMMENT '是否售后回租合同',
    CONTRACT_ACTIVATION_DATE VARCHAR(50) DEFAULT NULL COMMENT '合同激活日期',
    EVNT_DTE_TIME VARCHAR(50) DEFAULT NULL COMMENT '申请通过日期',
    LS_PRINCIPAL_OUTSTANDING_AMT DECIMAL(20,6) DEFAULT NULL COMMENT '下一期的剩余本金余额',
    INTEREST_AMT DECIMAL(20,6) DEFAULT NULL COMMENT '下一期利息',
    PRINCIPAL_OUTSTANDING_AMT DECIMAL(20,6) DEFAULT NULL COMMENT '当期剩余本金',
    SOURCE_CODE VARCHAR(10) DEFAULT NULL COMMENT '数据来源',
    ETL_DATE VARCHAR(50) DEFAULT NULL COMMENT '跑数时间',
    FINANCIAL_PRODUCT_NAME VARCHAR(255) DEFAULT NULL COMMENT '金融产品名称',
    CONTRACT_TRM DECIMAL(20,6) DEFAULT NULL COMMENT '总期数',
    DUE_DAY VARCHAR(10) DEFAULT NULL COMMENT '还款日',
    PRIMARY KEY (CONTRACT_NUMBER),
    KEY IDX_ETL_DATE_TMP(ETL_DATE)
);

-- 微信结算补偿信息备份表
create table CX_GQ_WECHAT_SETTLED_COMPENSATION2(
    CONTRACT_NUMBER VARCHAR(24) NOT NULL COMMENT '合同号',
    RTL_TL_FLAG VARCHAR(1) DEFAULT NULL COMMENT '是否TL合同',
    REQUEST_STATUS_DSC VARCHAR(200) DEFAULT NULL COMMENT '合同状态',
    IS_HOLD_DD VARCHAR(1) DEFAULT NULL COMMENT '是否HOLD DD',
    UNALLOCATED_AMT DECIMAL(20,6) DEFAULT NULL COMMENT '未分配金额',
    TTL_RENTAL_OVERDUE DECIMAL(20,6) DEFAULT NULL COMMENT '逾期款',
    INTEREST_RATE DECIMAL(20,6) DEFAULT NULL COMMENT '利率',
    DISTRIBUTOR_NAME_CHS VARCHAR(200) DEFAULT NULL COMMENT '主机厂中文名称',
    STRUCTURED_RENTAL_IND VARCHAR(1) DEFAULT NULL COMMENT '是否灵活还款',
    ASSET_CONDITION_CODE VARCHAR(10) DEFAULT NULL COMMENT '车辆类型',
    SETTLED_TRM DECIMAL(20,6) DEFAULT NULL COMMENT '已还期数',
    ASSET_TYPE VARCHAR(100) DEFAULT NULL COMMENT '汽车标识',
    LEASE_TYPE VARCHAR(10) DEFAULT NULL COMMENT '是否售后回租合同',
    CONTRACT_ACTIVATION_DATE VARCHAR(50) DEFAULT NULL COMMENT '合同激活日期',
    EVNT_DTE_TIME VARCHAR(50) DEFAULT NULL COMMENT '申请通过日期',
    LS_PRINCIPAL_OUTSTANDING_AMT DECIMAL(20,6) DEFAULT NULL COMMENT '下一期的剩余本金余额',
    INTEREST_AMT DECIMAL(20,6) DEFAULT NULL COMMENT '下一期利息',
    PRINCIPAL_OUTSTANDING_AMT DECIMAL(20,6) DEFAULT NULL COMMENT '当期剩余本金',
    SOURCE_CODE VARCHAR(10) DEFAULT NULL COMMENT '数据来源',
    ETL_DATE VARCHAR(50) DEFAULT NULL COMMENT '跑数时间',
    FINANCIAL_PRODUCT_NAME VARCHAR(255) DEFAULT NULL COMMENT '金融产品名称',
    CONTRACT_TRM DECIMAL(20,6) DEFAULT NULL COMMENT '总期数',
    DUE_DAY VARCHAR(10) DEFAULT NULL COMMENT '还款日',
    PRIMARY KEY (CONTRACT_NUMBER),
    KEY IDX_ETL_DATE_2(ETL_DATE)
);

