<?xml version="1.0" encoding="UTF-8"?>
<config>
	<param key="DEALERS_ENT_ID" name="经销商信息同步企业ID" type="String" description="经销商信息同步企业ID" value="" ></param>
	<param key="RETAIL_ENT_ID" name="零售客服信息同步企业ID" type="String" description="零售客服信息同步企业ID" value="" ></param>
	<param key="COLLECTION_ENT_ID" name="催收信息同步企业ID" type="String" description="催收信息同步企业ID" value="" ></param>
	<param key="IAM_INTERFACE_URL" name="IAM接口地址" type="String" description="IAM接口地址" value="https://apigateway-test.gsafc.com:8443/rest_ad" ></param>
	<param key="IAM_RSA_PUBLIC_KEY" name="IAM的RSA公钥" type="String" description="IAM的RSA公钥" value="" ></param>
	<param key="SEND_SMS_URL" name="短信服务接口url" type="String"  description="短信服务接口url" value="http://************:7003/NewSms/ProxyService/SendSmsMgrNewImplProxyService?wsdl"></param>
	<param key="MEDIA_URL" name="全媒体客服在线地址" type="String"  description="全媒体客服在线地址" value=""></param>
	<param key="FES_START_TIME" name="FES同步开始时间" type="String"  description="FES同步开始时间" value=""></param>
	<param key="FES_END_TIME" name="FES同步结束时间" type="String"  description="FES同步结束时间" value=""></param>
	<param key="UNION_ID_PRIVATE_KEY" name="UNIONID加密密钥" type="String"  description="UNIONID加密密钥" value="MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAKBBhyArPzdsh3cb8fbfMi8AVEpWf+MZfoXsDdOlyeNntDBRIPUceKertXIzE588VIB8yixjEdIBg0UDSlikRhxYBpkSnpXBJ9/250onwiE3wRlnNIiS7eioCkBR11ksEcPbdNZs1S9MyNG3L5l4NQ+apgOwCLb4fG7EIj/YQrevAgMBAAECgYBDXWhM6IEJblC/i/q5mPdCdLP6D8RwlX3vE9x4FJ/6PNSs3t2oy5AYg4ChgcEE1dp7rHlcEUUjb0lcEFcHTf6+LVmUrtkv9r8XZfNvux7568v7/HjyoXz6mSS2Gq2cH/83gdBBj8ZBc+Rqb70/Gy7zPHl5RcUoe+n5yiGPpHEeGQJBAOop06uLpjJt0j/s0oV2NI2clJ3EMPM6sI7lit5PwKf0d1foooskBfFBqunhQzMSSdhHvzCC/9NZ9uNJdS4KkI0CQQCvM0/pw+6xpckDwTzQ/CgvdqhMdtPXpQ53bo3jrLPi4GQUVjpnmWE9z8dppYL9tB9V1+shK9XuOIWvnvwjajArAkBSQQEOmE54FmWteNX2dbA+MktuI9WCCxKsD2u5bdBh7xjg82HteGjx9zw/TZaaYQk5hPJe6xVMO6Ti8BHOa32dAkBDdqrLBVugpIonG/3jK3X98N8VspwYacKMUtHoXdRXBLkE679JrVI4jw3mrIkoUHfQgnhUKaETPkicO8gziuxJAkBDTOD07zRgzkJtQcSvDgHCAwM6vJHgjh2wW55gAVes5RttLqBNga8VZizgdBrM8WsCu8v+XKd9dwP+Gs8xeFLg"></param>
	<param key="PROMPT_INPUT_WORDS" name="提示输入手机号话数" type="String"  description="提示输入手机号话数" value=""></param>
	<param key="PHONE_VERIFY_FAIL" name="手机号校验失败话数" type="String"  description="手机号校验失败话数" value=""></param>
	<param key="WECHAT_VERIFY_FAIL" name="微信校验失败话数" type="String"  description="微信校验失败话数" value=""></param>
	<param key="WECHAT_VERIFY_FAIL2" name="微信小程序校验失败话数" type="String"  description="微信小程序校验失败话数" value=""></param>
	<param key="CLEAR_FILE_PATH" name="清理文件路径" type="String"  description="清理文件路径" value=""></param>
	<param key="CLEAR_FILE_SUFFIX" name="清理文件后缀" type="String"  description="清理文件后缀" value=""></param>
	<param key="WECHAT_MINIPROGRAM_URL" name="微信虚拟账户请求地址" type="String"  description="微信虚拟账户请求地址" value=""></param>
	<param key="WECHAT_MINIPROGRAM_APPID" name="微信虚拟账户appId" type="String"  description="微信虚拟账户appId" value=""></param>
	<param key="SYSTEM_DOCTOR_ENT_ID" name="系统医生企业ID" type="String"  description="系统医生企业ID" value=""></param>
	<param key="SYSTEM_DOCTOR_URL" name="系统医生接口地址" type="String"  description="系统医生接口地址" value=""></param>
	<param key="SYSTEM_DOCTOR_APPID" name="系统医生接口appId" type="String"  description="系统医生接口appId" value=""></param>
	
	<param key="RELAY_CLOUD_ENT_ID" name="对接云中继企业ID" type="String"  description="对接云中继企业ID,多个使用逗号分隔" value=""></param>


	<param key="BIND_CONTRACT_URL" name="小程序绑定合同地址" type="String"  description="微信合同号绑定界面地址" value="" index="90"></param>
	<param key="BIND_CONTRACT_MAP" name="绑定合同地址映射" type="String"  description="绑定合同地址映射(sourceType:绑定页地址（地址需要使用URL编码）),多个使用逗号拼接" value="APP:https%3A%2F%2Fwww.baidu.com" index="90"></param>
	<param key="MEDIA_SIGN_KEY" name="全媒体加密密钥" type="String"  description="全媒体加密密钥" value="ASDASDASD" index="91"></param>
	<param key="AGENT_SPECIAL_PHONE" name="客服专项接入号" type="String"  description="客服专项接入号" value="" index="92"></param>
	<param key="AGENT_SPECIAL_PHONE_JGTS" name="客服专项接入号(监管投诉)" type="String"  description="客服专项接入号(监管投诉)" value="" index="93"></param>

	<param key="MEDIA_IN_METHOD" name="全媒体接入模式" type="String"  description="全媒体接入模式 1.原模式 2.新模式" value="1" index="93"></param>
	<param key="IP_WHITE_LIST" name="Ip白名单列表" type="String"  description="Ip白名单列表（仅针对WCS接口）" value="" index="94"></param>


</config>
