<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@page import="com.yunqu.cc.km.base.Constants"%>
<%@page import="org.easitline.common.core.context.AppContext"%>
<%@ include file="/pages/common/global.jsp" %>
<%
	AppContext appContext = AppContext.getContext(Constants.APP_NAME);
	request.setAttribute("feedbackType", appContext.getProperty("feedbackType", "1"));
%>
<EasyTag:override name="head">
    <title i18n-content="知识详情"></title>
    <link rel="stylesheet" href="/easitline-static/lib/layui/css/layui.css">
   	<link href="/easitline-static/lib/bootstrap/css/bootstrap.min.css" rel="stylesheet">
	<link href="/easitline-static/lib/font-awesome/css/font-awesome.min.css" rel="stylesheet">
	<link href="/easitline-static/css/easitline.ui.css?v=20180107" rel="stylesheet">
    <style>
		body{color:#333}
    	.km-box{
    		background: #fff;
    		border: 1px solid #eee;
    		margin: 20px auto 20px;
    		padding-bottom: 20px;
			padding:10px 15px;
			padding-right: 200px;
			margin-left: 15px;
			margin-right: 15px;	
    	}
    	.km-title{
		    font-size: 34px;
		    line-height: 1.15;
		    font-weight: 400;
		    vertical-align: sub;
		    padding: 10px 0;
    	}
    	.km-baseinfo-col{border-bottom: 1px dashed #ddd;padding: 10px 0}
    	.baseinfo-name{
    		width: 100px;float: left; text-align: right;padding: 3px 10px;
		    font-weight: 700;
		    overflow: hidden;
		    text-overflow: ellipsis;
		    white-space: nowrap;
		    word-wrap: normal;
		    color: #999;
    	}
    	.baseinfo-value{
    		float: left;padding: 3px 5px;
    		position: relative;
    		word-break: break-all;
    	}
    	.km-list-item-title{
		    display: block;
		    clear: both;
		    zoom: 1;
		    overflow: hidden;
		    font-size: 20px;
		    border-left: 12px solid #4F9CEE;
		    line-height: 24px;
		    font-size: 22px;
		    font-weight: 400;
		    font-family: Microsoft YaHei,SimHei,Verdana;
		    margin: 35px 0 15px -15px;
		    padding-left: 20px;
		    position: relative;
    	}
    	.km-list-item-title .title{
    		background: #fff;
		    z-index: 2;
		    position: relative;
		    padding-right: 10px;
    	}
    	.km-list-item-title:after{
    		content: '';
    		position: absolute;
    		width: 90%;
    		height: 1px;
    		background: #eee;
    		top:50%;
    		right: 0;

    	}
    	.km-list-item-content{
		    font-size: 14px;
		    word-wrap: break-word;
		    color: #333;
		    margin-bottom: 15px;
		    text-indent: 2em;
		    line-height: 24px;
		    zoom: 1;
    	}
    	
    	.km-list-item-content table{
		    text-indent: 0em;
    	}
    	
		.km-relate{margin-bottom: 20px}
    	.km-relate-title{
    		font-size: 19px;
		    line-height: 45px;
		    font-weight: 200;
		    font-family: 'Microsoft YaHei',sans-serif;
		    border-bottom: 1px solid #eee;
		    padding: 0!important;
		    margin: 0!important;
    	}
    	.extend-list li{padding: 5px 0;}

    	.sitenav{
    		position: fixed;
    		top: 40%;
    		right: 45px;
    		z-index: 3;
    		max-height: 70%;
    		overflow: auto;
    		transform: translateY(-50%);
		}
    	.sitenav li{
    		position: relative;
    		padding: 3px 10px 3px 16px;
		    line-height: 24px;
		    border-left: 2px solid #e8e8e8;
    	}
    	.sitenav li.current:after{
    		content: '';
    		position: absolute;
    		width: 2px;
    		height: 100%;
    		
    		left: -2px;
    		top: 0;
    		background: #4f9cee;
    	}
    	.sitenav .current,.sitenav .current>a{color: #4f9cee}
    	/*.sitenav .current:after{background: #4f9cee}*/
    	
    	
    	*::-webkit-scrollbar-thumb {
	    background: #ddd;
	    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .2) 50%, rgba(255, 255, 255, .2) 75%, transparent 75%, transparent);
		}
		*::-webkit-scrollbar {
		    width: 6px;
		    overflow-y: scroll;
		}
		*::-webkit-scrollbar-track {
		    background-color: #eee;
		}
		*::-webkit-scrollbar-track-piece {
		    background-color: #eee;
		}
		a{color:#333;text-decoration:none;}
		
		.shadow{
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-moz-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
		}
		.layui-badge-rim{
			padding: 10px 6px;
			line-height: 0px;
		}
		
		::-webkit-scrollbar {
			/*滚动条整体样式*/
			width : 12px;  /*高宽分别对应横竖滚动条的尺寸*/
		}
		::-webkit-scrollbar-thumb{
			border-radius: 7px;
			-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);
			background-color: #E8E8E8;
		}
    </style>

</EasyTag:override>
<EasyTag:override name="content">

	<div id="sidebox" class="site-nav-box">
        <a href="#reg" class="site-top" i18n-title="返回头部"></a>
        <ul id="sitenav" class="sitenav" style="overflow: auto;width: 10%;" >
          <li class="baseItem"><a href="#baseinfo" i18n-title="基础信息" i18n-content="基础信息"></a></li>
          <li><a href="#extend" i18n-title="附件" i18n-content="附件"></a></li>
          <li><a href="#relative" i18n-title="关联知识" i18n-content="关联知识"></a></li>
        </ul>
        <a href="#footer" class="site-footer" i18n-title="返回底部"></a>
    </div>

 	<div class="layui-fluid km-box shadow" id="baseinfo" style="padding-right: 16%;"> 
 		<input type="hidden" id="DIR_ID"/> 
	  	<h3 class="km-title" id="INFO_TOPIC"></h3>
	  	<div class="km-baseinfo">
			<div class="layui-row">
				<div class="km-baseinfo-col layui-col-xs6 layui-col-sm6 layui-col-md4">
			  		<div class="baseinfo-name" i18n-content="别名"></div>
			  		<div class="baseinfo-value" id="SEARCH_CODE" data-fn="getSearchCode" >
			  			<span class="layui-badge-rim"></span>
			  		</div>
				</div>
		  		<div class="km-baseinfo-col layui-col-xs6 layui-col-sm6 layui-col-md4">
			  		<div class="baseinfo-name" i18n-content="是否新知识"></div>
			  		<div class="baseinfo-value" id="IS_NEW" data-fn="getIsNew" ></div>
			  	</div>
	
		  		<div class="km-baseinfo-col layui-col-xs6 layui-col-sm6 layui-col-md4">
			  		<div class="baseinfo-name" i18n-content="创建人"></div>
			  		<div class="baseinfo-value" id="CREATE_USERNAME" ></div>
			  	</div>
			</div>
	  		<div class="layui-row">
				<div class="km-baseinfo-col layui-col-xs6 layui-col-sm6 layui-col-md4">
					<div class="baseinfo-name" i18n-content="创建日期"></div>
					<div class="baseinfo-value" id="CREATE_DATE" ></div>
				</div>

				<div class="km-baseinfo-col layui-col-xs6 layui-col-sm6 layui-col-md4">
					<div class="baseinfo-name" i18n-content="生效日期"></div>
					<div class="baseinfo-value" id="VALID_DATE"></div>
				</div>

				<div class="km-baseinfo-col layui-col-xs6 layui-col-sm6 layui-col-md4">
					<div class="baseinfo-name" i18n-content="失效日期"></div>
					<div class="baseinfo-value" id="EXPIRE_DATE"></div>
				</div>
			</div>
			<div class="layui-row">
				<div class="km-baseinfo-col layui-col-xs6 layui-col-sm6 layui-col-md4">
					<div class="baseinfo-name" i18n-content="业务分类"></div>
					<div class="baseinfo-value" id="CLASSIFY_NAME" data-fn="getClassifyName" ></div>
				</div>
				<div class="km-baseinfo-col layui-col-xs6 layui-col-sm6 layui-col-md4">
					<div class="baseinfo-name" i18n-content="知识提供人"></div>
					<div class="baseinfo-value" id="KM_PROVIDER" ></div>
				</div>
				<div class="km-baseinfo-col layui-col-xs6 layui-col-sm6 layui-col-md4">
					<div class="baseinfo-name" i18n-content="保密等级"></div>
					<div class="baseinfo-value" id="SECRET_LEVEL" ></div>
				</div>
			</div>
	  	</div>
	  	<!-- 知识列表项 -->
	  	<div class="km-list" id="km-list"></div>
	  	<script id="content-tpl" type="text/x-jsrender">
			 {{for  list}}
					<div id="item-{{:ID}}" class="km-list-item">
	  					<div class="km-list-item-title"><span class="title">{{:TITLE}}</span></div>
	  					<div class="km-list-item-content">
	  						<p style=""><div id="editor_{{:ID}}">{{:CONTENT}}</div></p>
	  					</div>
	  				</div>
					<div class="clearfix"></div>
			 {{/for}}	
		</script>
	  	<script id="nav-tpl" type="text/x-jsrender">
			 {{for  list}}
					<li><a id="nav-item-{{:ID}}" href="#item-{{:ID}}" i18n-title="{{:TITLE}}">{{:TITLE}}</a></li>
			 {{/for}}	
		</script>
		<script id="file-tpl" type="text/x-jsrender">
			 {{for  list}}
					<li><span>{{:#index+1}}. </span><a href="{{:FILE_URI}}?filename={{:FILE_NAME}}" target="_blank">{{:FILE_NAME}}</a></li>
			 {{/for}}	
		</script>
		<script id="linkInfo-tpl" type="text/x-jsrender">
			 {{for  list}}
					<li><span>{{:#index+1}}. </span><a onclick="KmCommon.showDetail('','{{:INFO_ID}}','','{{:INFO_TOPIC}}')" i18n-title="{{:INFO_TOPIC}}" target="_blank">{{:INFO_TOPIC}}</a></li>
			 {{/for}}	
		</script>
	  	<!-- 附件 -->
	  	<div id="extend" class="km-relate">
	  		<div class="km-relate-title" i18n-content="附件"></div>
	  		<div class="km-relate-content">
	  			<ul class="extend-list">
	  				
	  			</ul>
	  		</div>
	  	</div>
	  	<!-- 关联知识 -->
	  	<div id="relative" class="km-relate">
	  		<div class="km-relate-title" i18n-content="关联知识"></div>
	  		<div class="km-relate-content">
	  			<ul class="extend-list">
	  				
	  			</ul>
	  		</div>
	  	</div>
	</div>
</EasyTag:override>

<EasyTag:override name="script">
    <script src="/easitline-static/js/jquery.min.js"></script>
    <script type="text/javascript" src="/easitline-static/js/jsrender.min.js"></script>
    <script type="text/javascript" src="/easitline-static/lib/layer/layer.js"></script>
    <script src="/easitline-static/lib/layui/layui.js"></script>
    <script src="/easitline-static/js/easitline.core-2.0.0.js"></script>
	<script type="text/javascript" src="/easitline-static/js/requreLib.js"></script>
	<script type="text/javascript" src="/easitline-static/lib/bootstrap/js/bootstrap.min.js"></script>
	<script src="/cc-km/static/js/jquery.nav.js"></script>
	<script type="text/javascript" src="/cc-base/static/js/yq/extends.js"></script>
	<script src="/cc-km/static/js/km-common.js" type="text/javascript"></script>
	<script type="text/javascript" src="/cc-km/static/lib/ckeditor/ckeditor.js"></script>
	<script type="text/javascript" src="/cc-km/static/js/my_i18n.js"></script>
	<script type="text/javascript" src="/cc-base/static/js/i18n.js"></script>
	<script>
		//判断是否新知识
		function getIsNew(val) {
			return val == '0' ? getI18nValue('否') : getI18nValue('是');
		}
		function getSearchCode(val) {
			var array;
			if(typeof val == 'string') {
				array = val.split(";");
			} else {
				array = val;
			}
			var rt = '';
			for(var entityName in array) {
				if(array[entityName] != '') {
					rt += '<span class="layui-badge-rim">' + array[entityName] + '</span>&nbsp;';
				}
			}
			return  rt;
		}
		function getClassifyName(val) {
			var array;
			if(typeof val == 'string') {
				array = val.split(",");
			} else {
				array = val;
			}
			var rt = '';
			for(var entityName in array) {
				if(array[entityName] != '') {
					rt += '<span class="layui-badge-rim">' + array[entityName] + '</span>&nbsp;';
				}
			}
			return  rt;
		}
		var KmInfoDetail = {
			infoId: getUrlParam("infoId"),
			itemId: getUrlParam('itemId'),
			infoTitle:'',
			channelKey: getUrlParam('channelKey'),
			entId: getUrlParam('entId'),
			init: function() {
				var data={infoId:KmInfoDetail.infoId,itemId:KmInfoDetail.itemId,channelKey:KmInfoDetail.channelKey,entId:KmInfoDetail.entId};
				ajax.daoCall({"params":data,"controls":["KmPortalDao.getInfo","KmPortalDao.getItemList"]},function(result){
					var data = result["KmPortalDao.getInfo"].data;
					var data2 = result["KmPortalDao.getItemList"].data;
					if(data.state == '0'){
						layer.msg(data.msg); 
					}else{
						data.SECRET_LEVEL = getDictTextByCode('KM_SECRET_LEVEL',data.SECRET_LEVEL);
						
						fillRecord(data);
						KmInfoDetail.infoTitle = data.INFO_TOPIC;
						$("#secretLevel").text(getI18nValue(getDictTextByCode('KM_SECRET_LEVEL', data.SECRET_LEVEL)));
						
						var fileArray = [];
						for(var i in data2) {
							var file = data2[i].EX_FILE;
							if (file) {
								fileArray = fileArray.concat($.parseJSON(file));
							}
						}
						KmInfoDetail.getFileByIds(fileArray);
						
						KmInfoDetail.getLinkList();
						var template = $.templates("#content-tpl");
						var htmlOutput = template.render({list:data2});
						$(".km-list").html(htmlOutput);
						for(var i in data2) {
							var d = data2[i];
							$("#editor_" + d.ID).html(d.CONTENT);
							
						}
						
						var navTemplate = $.templates("#nav-tpl");
						var navHtmlOutput = navTemplate.render({list:data2});
						$(".baseItem").after(navHtmlOutput);
						
						$('#sitenav').onePageNav({scrollThreshold:0.02});
						if(KmInfoDetail.itemId) {
							$("#nav-item-" + KmInfoDetail.itemId).click();
						}
						execI18n();
					}
				});
				execI18n();
			},
			getFileByIds: function(fileArray){
				if (fileArray) {
					for (var i in fileArray) {
						var data = fileArray[i];
						$('#extend .extend-list').append(KmInfoDetail.getFileHtml(data.id, data.name, data.url, data.previewUrl, (parseInt(i) + 1)));
					}
				}
			}, 
			getFileHtml: function (id, name, url, previewUrl,index) {
				return '<li><span>' + index + '. </span><a href="' + url + '" target="_blank">' + name + '</a><a href="/cc-base/pages/fileview/fileview.jsp?fileSrc=' + encodeURIComponent(previewUrl) + '&fileType=' + name.substring(name.lastIndexOf('.') + 1) + '" target="_blank" class="ml-10" i18n-content="预览"></span></li>';
			}, 
			getLinkList: function(){
				ajax.daoCall({"params":{infoId:KmInfoDetail.infoId, entId:KmInfoDetail.entId},"controls":["KmPortalDao.linkInfoList"]},function(result){
					var linkInfolist = result['KmPortalDao.linkInfoList'].data;
					if(linkInfolist){
						var linkInfoTemplate = $.templates("#linkInfo-tpl");
						var linkInfoHtml = linkInfoTemplate.render({list:linkInfolist});
						$("#relative .extend-list").html(linkInfoHtml);
					}
				 });
			}
		}
		$(function(){
			KmInfoDetail.init();
		});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>