<!DOCTYPE html>
<html>

<head>
    <title>BI同步记录</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport"
        content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
    <!-- 基础的 css js 资源 -->
    <link rel="stylesheet" href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css" />
    <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.0" />
    <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/resetElement.css?v=1.0.0" />
    <script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
    <script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
    <script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
    <script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.0"></script>
    <style>
        .yq-table-page {
            background-color: #fff;
        }

        .title-box {
            padding: 16px;
            border-bottom: 1px solid #E8E8E8;
            display: flex;
            justify-content: space-between;
        }

        .search-box {
            padding: 10px 24px;
        }

        .el-form.el-form--inline .el-input__inner {
            height: auto !important;
            line-height: auto !important;
            background: #f2f4f7;
        }

        .table-box {
            padding: 24px;
            padding-top: 0;
        }
    </style>
</head>

<body class="yq-page-full vue-box">
    <div id="productPool" class="flex yq-table-page" element-loading-text="加载中..." v-cloak>
        <div class="flex">
            <div class="title-box">
                <div class="title">BI同步记录</div>

            </div>
            <div class="search-box">
                <el-form :inline="true" :model="formData" ref="form" inline :rules="rules">
                    <el-form-item label="执行日期" prop="DATE_ID">
                        <el-date-picker v-model="formData.DATE_ID" value-format="yyyy-MM-dd" type="date"
                            placeholder="选择日期">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="接口类型" prop="COMMAND">
                        <el-select v-model="formData.COMMAND" placeholder="请选择" filterable clearable>
                            <el-option v-for="(label, value) in BI_SYNC_COMMAND" :key="value" :label="label"
                                :value="value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="执行结果" prop="EXEC_RESULT">
                        <el-select v-model="formData.EXEC_RESULT" placeholder="请选择" filterable clearable>
                            <el-option v-for="(label, value) in BI_SYNC_RESULT" :key="value" :label="label"
                                :value="value">

                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item style="margin-left: 70px;">
                        <el-button type="primary" size="small" icon="el-icon-search" @click="doSearch">查询</el-button>
                        <el-button type="primary" size="small" icon="el-icon-refresh"
                            @click="dialogVisible=true">执行同步</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <div class="table-box yq-table">
                <el-table :data="tableData" stripe fit style="width: 100%" height="100%" v-loading="pageNav.loading"
                    :default-sort="{prop: 'CREATE_TIME', order: 'descending'}">
                    <el-table-column prop="DATE_ID" label="执行日期">

                    </el-table-column>
                    <el-table-column prop="COMMAND" label="接口类型">
                        <template slot-scope="scope">
                            {{BI_SYNC_COMMAND[scope.row.COMMAND]}}
                        </template>
                    </el-table-column>
                    <el-table-column prop="EXEC_START_TIME" label="开始时间">

                    </el-table-column>
                    <el-table-column prop="EXEC_END_TIME" label="结束时间" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="EXEC_TIMES" label="执行时长(s)">
                    </el-table-column>
                    <el-table-column prop="EXEC_RESULT" label="执行结果">
                        <template slot-scope="scope">
                            {{BI_SYNC_RESULT[scope.row.EXEC_RESULT]}}
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                    :current-page="pageNav.page" :page-sizes="[10, 15, 20, 30]" :page-size="pageNav.size"
                    layout="total, sizes, prev, pager, next, jumper" :total="pageNav.total"></el-pagination>
            </div>

        </div>
        <el-dialog title="同步" :visible.sync="dialogVisible" width="30%" :close-on-click-modal="false"
            @close="handleClose">
            <el-form :model="form" ref="form" :rules="rules" label-width="120px">
                <el-form-item prop="interfaceType" label="执行接口类型">
                    <el-select v-model="form.interfaceType" placeholder="请选择" clearable>
                        <el-option v-for="(label, value) in BI_SYNC_COMMAND_LIST" :key="value" :label="label"
                            :value="value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item prop="dateId" label="同步日期" v-if="form.interfaceType=='01' || form.interfaceType=='03' || form.interfaceType=='04' || form.interfaceType=='06'">
                    <el-date-picker type="date" placeholder="选择日期" v-model="form.dateId" format="yyyy-MM-dd"
                        value-format="yyyyMMdd"></el-date-picker>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="handleOk" :loading="isLoading">确 定</el-button>
            </span>
        </el-dialog>
    </div>
    <script>
        var orderFramePage = new Vue({
            components: {},
            el: '#productPool',
            data: function () {
                var checkDATE_ID = (rule, value, callback) => {
                    if (!value) {
                        return callback(new Error("不能为空"));
                    } else {
                        return callback();

                    }
                };
                return {
                    rules: {
                        DATE_ID: [
                            { validator: checkDATE_ID, trigger: "blur" }
                        ],
                        interfaceType: [
                            { required: true, message: '请选择', trigger: 'change' }
                        ],
                        dateId: [
                            { required: true, message: '请选择日期', trigger: 'change' }
                        ],
                    },
                    BI_SYNC_COMMAND: {},
                    BI_SYNC_RESULT: {},
                    dialogFormVisible: false,
                    showDetailValue: true,
                    dialogTitle: "添加",
                    formData: {
                        EXEC_RESULT: '',
                        COMMAND: '',
                        DATE_ID: new Date().toISOString().slice(0,10),
                    },
                    smallForm: {
                        PRODUCT_ID: "",
                        PRODUCT_NAME: ""
                    },
                    pageNav: {
                        size: 20,
                        page: 1,
                        total: 0,
                        loading: false
                    },
                    tableData: [],
                    show: false,
                    dialogVisible: false,
                    form: {
                        interfaceType: "",
                        dateId: "",
                    },
                    BI_SYNC_COMMAND_LIST: {},
                    isLoading:false,

                }
            },
            computed: {

            },
            beforeCreate() {
            },
            created() {

            },
            watch: {},
            methods: {
                doSearch() {
                    let data = {
                        data: this.formData,
                        pageSize: this.pageNav.size,
                        pageIndex: this.pageNav.page,
                        pageType: '3'
                    }
                    let that = this
                    yq.tableCall('/cx-mix/webcall?action=syncLog.logList', data, function (res) {
                        if (res.state == 1) {
                            console.log(res.data)

                            for (var i = 0; i < res.data.length; i++) {
                                let time = res.data[i].EXEC_TIMES;
                                let ms = time * 1000; // 1485000毫秒
                                let date = new Date(ms);
                                // 注意这里是使用的getUTCHours()方法，转换成UTC(协调世界时)时间的小时
                                let hour = date.getUTCHours();
                                // let hour = date.getHours(); 如果直接使用getHours()方法，则得到的时分秒格式会多出来8个小时（在国内开发基本都是使用的是东八区时间），getHours()方法会把当前的时区给加上。
                                let minute = date.getMinutes();
                                let second = date.getSeconds();
                                let formatTime = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:${second.toString().padStart(2, '0')}`;
                                res.data[i].EXEC_TIMES = formatTime
                            }
                            that.tableData = res.data
                            that.pageNav.total = res.totalRow
                        }
                    }).finally(res => {
                        this.pageNav.loading = false
                    })
                },
                handleCurrentChange(val) {
                    this.pageNav.page = val
                    this.doSearch()
                },
                handleSizeChange(val) {
                    this.pageNav.size = val
                    this.doSearch()
                },
                getDict: function () {
                    var _this = this;
                    yq.daoCall({ "controls": ["common.getDict(BI_SYNC_COMMAND)", "common.getDict(BI_SYNC_RESULT)", "common.getDict(BI_SYNC_COMMAND)"], "params": {} }, function (data) {
                        _this.BI_SYNC_COMMAND = data["common.getDict(BI_SYNC_COMMAND)"].data;
                        _this.BI_SYNC_RESULT = data["common.getDict(BI_SYNC_RESULT)"].data;
                        _this.BI_SYNC_COMMAND_LIST = data["common.getDict(BI_SYNC_COMMAND)"].data;
                    }, { contextPath: 'cx-mix' })
                },
                handleOk() {
                    this.$refs['form'].validate((valid) => {
                        if (valid) {
                            var url = ""
                            switch (this.form.interfaceType) {
                                case '01':
                                    url = '/cx-mix/servlet/execSync?action=syncRetailInfo&dateId=' + this.form.dateId
                                    break;
                                case '02':
                                    url = '/cx-mix/servlet/execSync?action=synDealersInfo'
                                    break;
                                case '03':
                                    url = '/cx-mix/servlet/execSync?action=synOrderHistory&dateId=' + this.form.dateId
                                    break;
                                case '04':
                                    url = '/cx-mix/servlet/execSync?action=synFes&dateId=' + this.form.dateId
                                    break;
                                case '05':
                                    url = '/cx-mix/servlet/execSync?action=synRequestStatus'
                                    break;
                                case '06':
                                    url = '/cx-mix/servlet/execSync?action=synWechatSettledCompensationInfo'
                                    break;
                                default:
                                    break;
                            }
                            this.isLoading = true
                            yq.remoteCall(url, {}, res => {
                                if(res.respCode == '999'){
                                    this.$message.error(res.respDesc)
                                    this.isLoading = false
                                }else{
                                    this.$message.success(res.respDesc)
                                    this.handleClose()
                                    this.isLoading = false
                                }
                            })
                        }
                    })
                },
                handleClose() {
                    this.dialogFormVisible = false
                    this.$refs['form'].resetFields();
                }
            },
            mounted: function () {
                this.doSearch()
                this.getDict()
            }
        })
    </script>
</body>

</html>