<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title><span i18n-title="模板树管理" ></span></title>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="easyform" data-mars="" style="margin-top: 10px;"  autocomplete="off" data-mars-prefix="">
			<input type="hidden"  name="P_ID" id="pId" value="${param.id}">
			<input type="hidden"  name="TYPE" id="code" value="${param.code}">
		  <table class="table table-edit table-vzebra">
                 <tbody>
                     <tr>
                         <td class="required" width="60px" i18n-content="类型名称"></td>
                         <td width="100px"><input type="text" name="NAME"  data-rules="required" class="form-control input-sm"></td>
                     </tr>
					 <tr>
                         <td i18n-content="编号"></td>
                         <td><input type="text" name="CODE"  class="form-control input-sm" style="width: 155px" maxlength="20"></td>
                     </tr>  
                     <tr>
                         <td i18n-content="排序"></td>
                         <td><input type="number" name="SORT_NUM"  class="form-control input-sm" style="width: 155px" maxlength="5" value="1"></td>
                     </tr>                    
                      <tr>
                         <td i18n-content="备注"></td>
                         <td>
                            <textarea class="form-control input-sm" name="BAKUP" rows="4" cols="21"></textarea>
                         </td>
                     </tr>
                 </tbody>
	      </table>
		  <div class="layer-foot text-c">
		   		<button class="btn btn-sm btn-primary"  type="button" id="submit-form" onclick="userEdit.ajaxSubmitForm()" i18n-content="保存"></button>
		   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="layer.closeAll();" i18n-content="关闭"></button>
		  </div>
	</form>				
</EasyTag:override>

<EasyTag:override name="script">
	 <script src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
    <!-- <script src="https://cdn.bootcss.com/zTree.v3/3.5.29/js/jquery.ztree.all.min.js"></script> -->
	<script type="text/javascript">
	jQuery.namespace("userEdit");
	$(function(){
		console.info(dataJson);

	});
	userEdit.ajaxSubmitForm = function(){
		if(form.validate("easyform")){		
			userEdit.insertData(); 	
		};
	}
	userEdit.insertData = function() {
		var pId=$("#pId").val();
		var data = form.getJSONObject("easyform");
		ajax.remoteCall("/cc-base/servlet/commonTree?query=AddTree",data,function(result) { 
			console.info(result)
				if(result.state == 1){
					layer.alert(result.msg,{icon: 1,closeBtn:0},function(){
						//window.location.reload();
						reloadNode('${param.tid}');
						layer.closeAll();
						});
					}else{
					layer.alert(result.msg,{icon: 5});
				}
			}
		);
 }

	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>