<%@ page language="java" contentType="text/html;charset=UTF-8"%>
    <%@ include file="/pages/common/global.jsp"%>
        <EasyTag:override name="head">
            <title i18n-content="常用语配置"></title>
            <style type="text/css">
                div#rMenu {
                    position: absolute;
                    visibility: hidden;
                    top: 0;
                    text-align: left;
                    padding: 2px;
                }
                
                .dropdown-menu>li>a {
                    font-size: 13px;
                }
                
                a:link {
                    color: #00adff;
                }
                
                .gray-bg {
                    background-color: #fff
                }
                
                .relaType {
                	display:none;
                }
            </style>
        </EasyTag:override>
        <EasyTag:override name="content">
            <form action="javaScript:void(0)" id="searchForm" name="searchForm" class="form-inline">
                <input type="hidden" value="-1" name="id" id="id"> <input type="hidden" value="" name="text" id="text"><input type="hidden" value="${param.type }" name="type" id="type">
                <input type="hidden" id="code" name="code">
                <!-- 01公共 02个人  -->
                <div class="row">
                    <div style="background-color: #fff; margin-left: 15px; width: 18%; float: left; height: 700px">
                        <div style="border-bottom: 1px solid #eee; height: 52px; line-height: 52px; padding: 0px 15px">
                            <span i18n-content="常用语目录"></span>
                            <span class="f-12"><span class="label label-info" i18n-content="右键菜单操作"></span></span>
                            <button type="button" onclick="phrase.toAddPhraseDir('-1')" class="btn btn-sm btn-success btn-outline" style="float: right;margin-top: 11px;">
                            	<span class="glyphicon glyphicon-plus"></span>
                            </button>
                        </div>
                        <!-- <div class="ztree" data-mars="phrase.getZtree" id="ztree" data-setting="{callback: {onClick: zTreeOnClick,onRightClick: OnRightClick}}" style="max-height: 700px; overflow: auto; padding: 15px;"></div> -->
                        <div class="ztree" id="ztree" style="max-height: 700px; overflow: auto; padding: 15px;"></div>
                    </div>
                    <div style="height: 450px; width: 78%; float: left; margin-left: 15px;">
                        <div class="ibox ">
                            <div class="ibox-title clearfix" id="divId">
                                <div class="form-group">
                                    <h5 i18n-content="常用语"></h5>
                                    <c:if test="${param.type=='01' }">
                                        <div class="input-group input-group-sm pull-right">
                                            <button class="btn btn-sm btn-success btn-outline " type="button" title="将所有常用语同步到缓存,让客服可以使用" onclick="phrase.sync()" i18n-content="同步"></button>
                                        </div>
                                    </c:if>

                                    <div class="input-group input-group-sm pull-right ">
                                        <button id="black_min_delete" type="button" class="btn btn-sm btn-danger btn-outline" onclick="phrase.dels()" i18n-content="批量删除"></button>
                                    </div>
                                    <div class="input-group input-group-sm pull-right">
                                        <button id="min_upload" type="button" class="btn btn-sm btn-info btn-outline" onclick="phrase.exportData()" i18n-content="导出"></button>
                                    </div>
                                    <div class="input-group input-group-sm pull-right">
                                        <button id="min_upload" type="button" class="btn btn-sm btn-info btn-outline" onclick="phrase.importData()" i18n-content="导入"></button>
                                    </div>
                                    <div class="input-group input-group-sm pull-right">
                                        <button class="btn btn-sm btn-success btn-outline " type="button" onclick="phrase.addPhrase()" i18n-content="新增"></button>
                                    </div>


                                </div>
                                <div class="form-group">
                                    <div class="input-group input-group-sm ml-8">
                                        <span class="input-group-addon" i18n-content="标题"></span> <input type="text" name="title" class="form-control input-sm" style="width: 120px">
                                    </div>
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-addon" i18n-content="内容"></span> <input type="text" name="content" class="form-control input-sm" style="width: 160px">
                                    </div>
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-addon" i18n-content="关键字"></span> <input type="text" name="keyWord" class="form-control input-sm" style="width: 160px">
                                    </div>
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-addon" i18n-content="内容类型"></span> 
                                        <select class="form-control input-sm" name="contentType" data-mars="common.getDict(C_CF_PHRASE_CONTENT_TYPE)">
		              						<option value="" i18n-content="请选择"></option>
										</select>
                                    </div>
                                    <!-- <div class="input-group input-group-sm">
								<span class="input-group-addon" i18n-content="是否公开"></span> <select
									data-mars="common.getDict(SF_YN)" data-rules="required"
									class="form-control input-sm" name="is_public">
									<option value="" i18n-content="请选择"></option>
								</select>
							</div> -->
                                    <div class="input-group input-group-sm">
                                        <button class="btn btn-sm btn-default" type="button" onclick="searchResData('1')">
                                        	<span class="glyphicon glyphicon-search"></span>
											<span i18n-content="查询"></span>
										</button>
                                    </div>
                                    <div class="input-group ">
                                        <button type="button" class="btn btn-sm btn-default" onclick="phrase.reset()">
                                        	<span class="glyphicon glyphicon-repeat"></span>
											<span i18n-content="重置"></span>
										</button>
                                    </div>
                                    <c:if test="${param.type=='01' }">
	                                    <div class="input-group">
											<button type="button" class="btn btn-sm btn-default" onclick="phrase.moreQuery()" i18n-content="高级查询"></button>
										</div>
										<div class="form-group" id="more-query-content" style="display: none;">
		                                    <div class="input-group input-group-sm">
		                                    	<span class="input-group-addon" i18n-content="渠道"></span>
		                                    	<input readonly="readonly" id="channelList" autocomplete="off" class="form-control input-sm">
		                                    	<input type="hidden" name="channelList">
		                                    </div>
		                                    <div class="input-group input-group-sm">
		                                    	<span class="input-group-addon" i18n-content="技能组"></span>
		                                    	<input readonly="readonly" id="skillList"autocomplete="off" class="form-control input-sm">
	                                    		<input type="hidden" name="skillList">
		                                    </div>
										</div>
                                    </c:if>
                                </div>
                            </div>
                            <div class="ibox-content">
                                <table id="main"></table>
                            </div>
                        </div>
                    </div>
                </div>

            </form>
            <div id="rMenu">
                <ul class="dropdown-menu" role="menu">
                    <li>
                        <a href="javascript:void(0)" onclick="phrase.toAddPhraseDir();" i18n-content="新增分类"></a>
                    </li>
                    <li>
                        <a href="javascript:void(0)" onclick="phrase.toEditPhraseDir();" i18n-content="修改分类"></a>
                    </li>
                    <c:if test="${param.type=='01' }">
                    <li class="relaType">
                        <a href="javascript:void(0)" onclick="phrase.showSlider('01');" i18n-content="关联渠道"></a>
                    </li>
                    <li class="relaType">
                        <a href="javascript:void(0)" onclick="phrase.showSlider('02');" i18n-content="关联技能组"></a>
                    </li>
                    </c:if>
                    <li>
                        <a href="javascript:void(0)" onclick="phrase.delPhraseDir();" i18n-content="删除分类"></a>
                    </li>
                </ul>
            </div>
        </EasyTag:override>
        <EasyTag:override name="script">
            <script type="text/javascript">
                requreLib.setplugs("select2");

                jQuery.namespace("phrase");
                var zTree, rMenu;
                
                requreLib.setplugs("layui", function() {
	                layui.config({
	        			base: '${ctxPath}/static/js/'
	        		}).use(['tableSelect'], function(){
	        			var tableSelect = layui.tableSelect;
	        			//渠道
	           			tableSelect.render({
	           				elem: '#channelList',
	           				searchKey: 'CHANNEL_NAME',
	           				checkedKey: 'CHANNEL_KEY',
	           				searchPlaceholder: getI18nValue('请输入渠道名称或渠道key'),
	           				table: {
	           					mars: 'channel.getChannelIdNameList',
	           					cols: [[
	           						{ type: 'radio' },
	           						{ type: 'numbers',title: getI18nValue('序号')},
	           						{ field: 'CHANNEL_NAME', title: getI18nValue('渠道名称')},
	           						{ field: 'CHANNEL_KEY', title: getI18nValue('渠道Key')},
	           					]]
	           				},
	           				done: function(elem, data){
	           					//回显
	        					var names = [];
	        					var ids = [];
	        					layui.each(data.data, function (index, item) {
	        						names.push(item.CHANNEL_NAME);
	        						ids.push(item.CHANNEL_KEY);
	        					});
	        					elem.attr("ts-selected",ids.join(","));
	        					elem.val(names.join(","));
	        					$("input[name='channelList']").val(ids.join(","));
						    },
	        				clear:function(elem){
	        					 elem.prev().val("");
	        				}
	           			})
	           			//技能组
	           			tableSelect.render({
	           				elem: '#skillList',
	           				searchKey: 'NAME',
	           				checkedKey: 'CODE',
	           				searchPlaceholder: getI18nValue('请输入名称或编号'),
	           				table: {
	           					mars: 'phrase.skillGroupList',
	           					cols: [[
	           						{ type: 'radio' },
	           						{ type: 'numbers',title: getI18nValue('序号')},
	           						{ field: 'NAME', title: getI18nValue('技能组名称')},
	           						{ field: 'CODE', title: getI18nValue('技能组编号')},
	           					]]
	           				},
	           				done: function(elem, data){
	           					//回显
	        					var names = [];
	        					var ids = [];
	        					layui.each(data.data, function (index, item) {
	        						names.push(item.NAME);
	        						ids.push(item.CODE);
	        					});
	        					elem.attr("ts-selected",ids.join(","));
	        					elem.val(names.join(","));
	        					$("input[name='skillList']").val(ids.join(","));
						    },
	        				clear:function(elem){
	        					 elem.prev().val("");
	        				}
	           			})
	        		})
                })
        		
                $(function() {
                    $('#searchForm').render({
                        success: function() {
                           	phrase.load();
                        }
                    });

                    $("#searchForm").keydown(function(e) {
                        var ke = e.keyCode;
                        if (ke == 13) {
                            searchResData('');
                        }
                    });

                    requreLib.setplugs('layui', function() {
                        phrase.loadData();
                    });
                    $("input[name='checkAll']").click(function() {
                        var ifChecked = $(this).prop("checked");
                        $("#dataList input:checkbox").prop("checked", ifChecked);
                    })
                });
                
                phrase.importData = function() {
                    var DIR_ID = $("#id").val(); //目录id
                    if (DIR_ID == "" || DIR_ID == -1) {
                        layer.msg(getI18nValue("请选择分类"));
                        return;
                    }

                    var type = "${param.type}"; //01公共 02个人
                    if (type == '01') {
                        popup.layerShow({
                            type: 1,
                            title: getI18nValue("公共常用语导入"),
                            offset: '20px',
                            area: ['450px', '220px']
                        }, "${ctxPath}/pages/agentconfig/phrase/phrase-import.jsp?qcleId=" + DIR_ID + "&type=" + type, null);
                    } else {
                        popup.layerShow({
                            type: 1,
                            title: getI18nValue("个人常用语导入"),
                            offset: '20px',
                            area: ['450px', '220px']
                        }, "${ctxPath}/pages/agentconfig/phrase/phrase-import.jsp?qcleId=" + DIR_ID + "&type=" + type, null);
                    }

                }

                function OnRightClick(event, treeId, treeNode) {
                	$("#code").val(treeNode.CODE);
                	if(treeNode.id == "-1"){// 根目录不操作右键菜单
                		return;
                	}else if(treeNode.PARENT_ID != "-1"){// 非一级目录隐藏关联选项
                		$(".relaType").hide();
                	}else{
                		$(".relaType").show();
                	} 
                    zTree = $.fn.zTree.getZTreeObj("ztree");
                    rMenu = $("#rMenu");
                    zTree.selectNode(treeNode);
                    showRMenu("node", event.clientX, event.clientY);
                    var nodes = zTree.getSelectedNodes();

                    $("#id").val(nodes[0].id);
                    $("#text").val(nodes[0].name);
                }

                function showRMenu(type, x, y) {
                    $("#rMenu ul").show();
                    if (type == "root") {
                        $("#m_del").hide();
                        $("#m_check").hide();
                        $("#m_unCheck").hide();
                    } else {
                        $("#m_del").show();
                        $("#m_check").show();
                        $("#m_unCheck").show();
                    }
                    rMenu.css({
                        "top": y + "px",
                        "left": x + "px",
                        "visibility": "visible"
                    });
                    $("body").bind("mousedown", onBodyMouseDown);
                }

                function hideRMenu() {
                    if (rMenu)
                        rMenu.css({
                            "visibility": "hidden"
                        });
                    $("body").unbind("mousedown", onBodyMouseDown);
                }

                function onBodyMouseDown(event) {
                    if (!(event.target.id == "rMenu" || $(event.target).parents(
                            "#rMenu").length > 0)) {
                        rMenu.css({
                            "visibility": "hidden"
                        });
                    }
                }

                function zTreeOnClick(event, treeId, treeNode) {
                    $("#id").val(treeNode.id);
                    $("#code").val(treeNode.code);
                    $("#text").val(treeNode.name);
                    $("#delRes").show();
                    searchResData();
                }

                function searchResData(flag) {
                    if (flag == '1') {
                        $("#searchForm").queryData({
                            id: 'main',
                            page: {
                                curr: 1
                            }
                        });
                    } else {
                        $("#searchForm").queryData({
                            id: 'main'
                        });
                    }
                }

                //打开常用语目录新增界面
                phrase.toAddPhraseDir = function(pid) {
                    hideRMenu();
                    var id = pid?pid:$("#id").val();
                    var type = $("#type").val();

                    popup.layerShow({
                        type: 1,
                        title: getI18nValue("新增分类"),
                        offset: '20px',
                        area: ['520px', '580px']
                    }, "${ctxPath}/servlet/phrase?action=phraseDirEdit", {
                        PID: id,
                        ID: "",
                        TYPE: type
                    }); //新增传父id 不传当前id
                }

                //打开常用语目录修改界面
                phrase.toEditPhraseDir = function() {
                    hideRMenu();
                    var type = $("#type").val();
                    var id = $("#id").val();
                    if (id == -1) {
                        layer.msg(getI18nValue("请选择分类"));
                        return;
                    }
                    popup.layerShow({
                        type: 1,
                        title: getI18nValue("修改分类"),
                        offset: '20px',
                        area: ['460px', '580px']
                    }, "${ctxPath}/servlet/phrase?action=phraseDirEdit", {
                        PID: "",
                        ID: id,
                        TYPE: type
                    });
                }

                //打开新增常用语界面
                phrase.addPhrase = function() {
                    var DIR_ID = $("#id").val(); //目录id
                    var text = $("#text").val(); //目录id
                    var type = $("#type").val();

                    console.log(DIR_ID, text, type)

                    if (DIR_ID == "" || DIR_ID == -1) {
                        layer.msg(getI18nValue("请选择分类"));
                        return;
                    }
                    popup.layerShow({
                        type: 1,
                        title: getI18nValue("新增常用语"),
                        offset: '20px',
                        area: ['460px', '580px']
                    }, "${ctxPath}/pages/agentconfig/phrase/phraseEdit.jsp", {
                        ID: "",
                        DIR_ID: DIR_ID,
                        text: text,
                        TYPE: type

                    });
                }

                function getQueryVariable(variable) {
                    var query = window.location.search.substring(1);
                    var vars = query.split("&");
                    for (var i = 0; i < vars.length; i++) {
                        var pair = vars[i].split("=");
                        if (pair[0] == variable) {
                            return pair[1];
                        }
                    }
                    return (false);
                }

                //进入到修改常用语界面
                phrase.editPhrase = function(ID,DIR_ID) {
                    var type = $("#type").val();
                    var text = $("#text").val();// 目录名称
                    popup.layerShow({
                        type: 1,
                        title: getI18nValue("修改常用语"),
                        offset: '20px',
                        area: ['460px', '580px']
                    }, "${ctxPath}/pages/agentconfig/phrase/phraseEdit.jsp", {
                        ID: ID,
                        DIR_ID: DIR_ID,
                        text: text,
                        TYPE: type
                    });
                }
                phrase.load = function() {
                    $('#searchForm').render({
                        success: function() {
                            requreLib.setplugs('slimscroll,ztree', function() {
                               	var setting = {
                              			callback: {onClick: zTreeOnClick,onRightClick: OnRightClick},
                               		expandAll: true
                               	}
                               	commonTree.initTree(setting);
                            	$('#ztree').slimScroll({
                                    height: '700px',
                                    color: '#ddd'
                                });
                            });
                        }
                    });
                    searchResData();
                }

                //重置
                phrase.reset = function() {
                    $("#divId select").val("");
                    $("#divId input").val("");
                };


                phrase.loadData = function() {
                    $("#searchForm").initTableEx({
                        mars: 'phrase.getPhraseList',
                        id: 'main',
                        height: 'full-180',
                        limit: 15,
                        limits:[15,25,50,100,200],
                        cols: [
                            [{
                                    width: 60,
                                    field: 'ID',
                                    title: getI18nValue("选择"),
                                    type: 'checkbox'
                                }, {
                                	width:70,
									align:'center',
									title: getI18nValue("序号"),
									type:'numbers'
                                }, {
                                    field: 'NAME',
                                    title: getI18nValue("目录")
                                }, {
                                    field: 'TITLE',
                                    title: getI18nValue("标题")
                                }, {
                                	width: 90,
                                    field: 'CONTENT_TYPE',
                                    title: getI18nValue("内容类型"),
                                    align: 'center',
                                    templet: function(row){
                                    	var color = {"text":"label-primary","image":"label-success","video":"label-info","file":"label-warning"};
                                    	var type = getDictTextByCode("C_CF_PHRASE_CONTENT_TYPE",row.CONTENT_TYPE);
                                    	return '<span class="label '+color[row.CONTENT_TYPE]+'">'+ type +'</span>';
                                    }
                                },{
                                    field: 'CONTENT',
                                    title: getI18nValue("内容"),
                                    templet: function(row){
                                    	return decodeURIComponent(row.CONTENT);
                                    }
                                }, {
                                    field: 'KEY_WORD',
                                    title: getI18nValue("关键字")
                                }, {
                                    width: 90,
                                    field: 'ENABLE_STATUS',
                                    title: getI18nValue("启用状态"),
                                    align: 'center',
                                    templet: function(row) {
                                        var enableStatus = row['ENABLE_STATUS'];
                                        var val = getDictTextByCode("ENABLE_STATUS", enableStatus);
                                        if ("01" == enableStatus) {
                                            return "<span class='layui-btn layui-btn-xs label-success '>" + val + "</span>"
                                        } else {
                                            return "<span class='layui-btn layui-btn-xs layui-btn-danger '>" + val + "</span>"
                                        }
                                    }
                                }
                                /* ,{width:100,field:'IS_PUBLIC', title: getI18nValue("是否公开"),templet:function(row){
                                	return  getDictTextByCode('SF_YN',row.IS_PUBLIC);
                                	 }} */
                                , {
                                    width: 100,
                                    field: 'TYPE',
                                    align: 'center',
                                    title: getI18nValue("操作"),
                                    templet: function(row) {
                                        var temp = '<a  href="javascript:void(0)"  onclick="phrase.editPhrase(\'' + row.ID + '\',\''+row.DIR_ID+'\')">' + getI18nValue("修改") + '</a> ' +
                                            '<a  href="javascript:void(0)"  onclick="phrase.del(\'' + row.ID + '\')">' + getI18nValue("删除") + '</a> ';
                                        return temp;
                                    }
                                }
                            ]
                        ]
                    });
                }
                phrase.delPhraseDir = function() {
                    hideRMenu();
                    var id = $("#id").val();
                    layer.confirm(getI18nValue("确定要删除吗"), {
                        icon: 3,
                        title: getI18nValue('提示'),
                        offset: '20px',
                        btn: [getI18nValue('确定'), getI18nValue('取消')]
                    }, function() {
                        ajax.remoteCall("${ctxPath}/servlet/phrase?action=DeletePhraseDir", {
                            id: id
                        }, function(result) {
                            if (result.state == 1) {
                                layer.msg(result.msg, {
                                    icon: 1
                                });
                                phrase.load();

                            } else {
                                layer.alert(result.msg, {
                                    icon: 5,
                                    title: getI18nValue('提示'),
                                    btn: [getI18nValue('确定'), getI18nValue('取消')]
                                });
                            }
                        });
                    });

                }
                phrase.dels = function() {
                    var DIR_ID = $("#id").val(); //目录id

                    if (DIR_ID == "" || DIR_ID == -1) {
                        layer.msg(getI18nValue("请选择分类"));
                        return;
                    }
                    var arr = new Array();
                    var checkStatus = table.checkStatus('main');
                    if (checkStatus.data.length == 0) {
                        layer.msg(getI18nValue("请选择需要删除的行"));
                        return;
                    } else {
                        for (j = 0; j < checkStatus.data.length; j++) {
                            arr.push("'" + checkStatus.data[j].ID + "'");
                        }
                    }
                    layer.confirm(getI18nValue("确定要删除吗"), {
                        icon: 3,
                        title: getI18nValue('提示'),
                        offset: '20px',
                        btn: [getI18nValue('确定'), getI18nValue('取消')]
                    }, function() {
                        var id = arr.join(",");
                        var data = {
                            id: id
                        };
                        ajax.remoteCall("${ctxPath}/servlet/phrase?action=DeletePhrases", data, function(result) {
                            if (result.state == 1) {
                                layer.msg(result.msg, {
                                    icon: 1,
                                    time: 1200,
                                    offset: '40px'
                                }, function() {
                                    phrase.loadData();
                                });
                            } else {
                                layer.alert(result.msg, {
                                    icon: 5
                                });
                            }
                        });
                    });
                }
                phrase.sync = function() {
                    layer.confirm(getI18nValue("确认同步常用语到缓存"), {
                        icon: 3,
                        title: getI18nValue('提示'),
                        offset: '20px',
                        btn: [getI18nValue('确定'), getI18nValue('取消')]
                    }, function() {
                        ajax.remoteCall("${ctxPath}/servlet/phrase?action=syncPhrase", {}, function(result) {
                            if (result.state == 1) {
                                layer.msg(result.msg, {
                                    icon: 1
                                });
                                phrase.loadData();
                            } else {
                                layer.alert(result.msg, {
                                    icon: 5
                                });
                            }
                        });
                    });
                }

                phrase.del = function(id) {
                    hideRMenu();
                    layer.confirm(getI18nValue("确定要删除吗"), {
                        icon: 3,
                        title: getI18nValue('提示'),
                        offset: '20px',
                        btn: [getI18nValue('确定'), getI18nValue('取消')]
                    }, function() {
                        ajax.remoteCall("${ctxPath}/servlet/phrase?action=DeletePhrase", {
                            id: id
                        }, function(result) {
                            if (result.state == 1) {
                                layer.msg(result.msg, {
                                    icon: 1
                                });
                                phrase.loadData();
                            } else {
                                layer.alert(result.msg, {
                                    icon: 5
                                });
                            }
                        });
                    });
                }

                //导出
                phrase.exportData = function(){
                	var DIR_ID = $("#id").val(); //目录id
                    if (DIR_ID == "" || DIR_ID == -1) {
                        layer.msg(getI18nValue("请选择分类"));
                        return;
                    }
                	
                	var arr = new Array();
                	var phrase;
                	var tips;
                    var checkStatus = table.checkStatus('main');
                    if (checkStatus.data.length == 0) {
                    	tips = getI18nValue("当前没有勾选常用语，即将导出当前目录所有常用语");
                    	phrase = "";
                    } else {
                        for (j = 0; j < checkStatus.data.length; j++) {
                            arr.push(checkStatus.data[j].ID);
                            tips = getI18nValue('是否导出？');
                        }
                        phrase = arr.join(",");
                    }
        			layer.confirm(tips,{icon: 3, title:getI18nValue('提示'),offset:'20px',btn : [ getI18nValue('确定'), getI18nValue('取消') ]}, function(index){
        				layer.close(index);
        				var data = form.getJSONObject("#searchForm");
        				location.href = "${ctxPath}/servlet/phrase?action=phraseExport&"+$("#searchForm").serialize()+"&phrase="+phrase+"&type=${param.type}";;
        			});		
        		}
                
                //打开穿梭框
                phrase.showSlider = function(type){// 01-渠道，02-技能组
                	hideRMenu();
                	var title = type=="02"?"关联技能组":"关联渠道";
                	popup.layerShow({
                        type: 1,
                        title: getI18nValue(title),
                        offset: '20px',
                        area: ['860px', '780px']
                    }, "${ctxPath}/pages/agentconfig/phrase/dir-relative.jsp", {
                        DIR_ID: $("#id").val(),
                        TYPE: type,
                        CODE: $("#code").val(),
                        url: "${ctxPath}/servlet/phrase?action=dirRelation" , // 用于请求对应后台的url
                        sender: "phrase",
                    });
                }
                
        		//关联技能组
/*         		picConfig.connectSkill = function(){
        			var dirId = $("#pid").val();
        			popup.layerShow({btn: [getI18nValue('保存'),getI18nValue('关闭')],type:2,title:getI18nValue("技能组"),offset:'20px',area:['380px','420px'],yes:function(index, layero){
        				var data = treeObj.getCheckedNodes(true);
        				var json=[];
        				for(var index in data){
        					var item=data[index];
        					if(item['children']&&item['children'].length>0){
        					}else{
        						json.push(item['SKILL_GROUP_ID']);
        					}
        				}
        				ajax.remoteCall("${ctxPath}/servlet/pic?action=Connect", {id:dirId,type:'02',busiId:json.join()}, function(result) {
        					if(result.state == 1){
        					    layer.msg(result.msg,{icon: 1,time:1000},function(){
        					    	layer.closeAll();
        					    });
        					}else{
        						layer.alert(result.msg,{icon: 5});
        					}
        				});
        			}},"${ctxPath}/pages/media/common/connect-skill.jsp",{dirId:dirId});
        		} */
                
                function getDictTextByCode(code, val) {
                    if (!code) {
                        console.error(getI18nValue('没有传字典编号'));
                    } else {
                        var dict = dictJson[code];
                        if (!dict) {
                            dict = getDictByCode(code);
                            if (dict.length == 0) {
                                console.error(getI18nValue('根据字典编号找不到字典项'));
                                return val;
                            }
                            dict = dict[controls].data;
                            dictJson[code] = dict;
                        }
                        for (var i in dict) {
                            if (i == val) {
                                return dict[i];
                            }
                        }
                    }
                    return val;
                }
                
                var commonTree = {
                	// 树div元素id
                	el: 'ztree',
                	// mars请求
                	mars: 'phrase.getCatalogList',
                	// 请求参数
               		params: {
               			type: '${param.type}'
               		},
               		// id对应的数据库字段
               		idKey: 'ID',
               		// 父id对应的数据库字段
               		parentKey: 'PARENT_ID',
               		// 第一级目录的parentId
               		firstParentCode: '-1',
               		// 节点的数据格式转换
               		initNode: function(data) {
                        data.name = data.NAME;
                        data.id = data.ID;
                        data.code = data.CODE;
                        return data;
               		},
               		// 第一个节点
               		firstNode: {
               			id: '-1',
               			name: getI18nValue('目录')
               		},
               		// 初始化树
               		initTree: function(setting) {
               			commonTree.getData(function(data) {
                   			var $el = $('#'+commonTree.el);
                   			var config = $.extend({}, $el.data('setting'), setting);
                   			commonTree.firstNode.children = commonTree.handleData(data) || [];
	               			$.fn.zTree.init($el, config, commonTree.firstNode);
	               			if (setting && setting.expandAll) {
		                        var ztreeObject = $.fn.zTree.getZTreeObj(commonTree.el);
		                        if (ztreeObject != null) {
		                            ztreeObject.expandAll(true);
		                        }
	               			}
                    	});
               		},
                	handleData: function(datas, targetCode) {
                        var result = [];
                        for (var i in datas) {
                        	var data = datas[i];
                        	if (!data.ID) { continue; }
                            var resCode = data[commonTree.idKey];
                            var parentCode = data[commonTree.parentKey];
                            if ((targetCode==null && parentCode == commonTree.firstParentCode) || (targetCode && targetCode == parentCode)) {
                                datas.splice(i, 1, {});
                                data = commonTree.initNode(data);
                                data.children = commonTree.handleData(datas, resCode);
                                result.push(data);
                            }
                        }
                        return result;
                    },
                    getData: function(callback) {
                        var controls = commonTree.mars;
                        ajax.daoCall({
                            params: commonTree.params,
                            controls: [controls]
                        }, function(data) {
                            if (data[controls]) {
                                data = data[controls].data;
                                callback && callback(data);
                            }
                        })
                    }
                }
                
              	//高级查询
                phrase.moreQuery = function(){
                	$('#more-query-content').toggle();
              	}
                
            </script>

        </EasyTag:override>
        <%@ include file="/pages/common/layout_list.jsp"%>