<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title i18n-content="场景列表"></title>
	<style type="text/css">
		a:link{ color:#00adff;}
		.shadow{
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-moz-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form autocomplete="off"  name="sceneListForm" class="form-inline shadow" id="sceneListForm">
		<input name="infoType" class="form-control input-sm" value="01" type="hidden">
		<div style="height: 100%;" class="right">
			<div class="ibox">
				<div class="ibox-title clearfix">
					<div class="form-group">
						<h5 i18n-content="场景库维护"></h5>
						<div class="input-group input-group-sm pull-right">
							<EasyTag:res resId="cc-km-manage-edit-aysncAll"><button type="button" class="btn btn-sm btn-success btn-outline mr-5" onclick="questionListTmp.synAllInfo('01')" i18n-content="全量同步"></button></EasyTag:res>
							<button type="button" class="btn btn-sm btn-success btn-outline mr-5" onclick="sceneListTmp.sceneEdit()" i18n-content="新增"></button>
							<EasyTag:res resId="cc-km-manage-importInfo"><button type="button" class="btn btn-sm btn-info btn-outline mr-5" onclick="importKmInfo('01')" i18n-content="导入"></button></EasyTag:res>
							<EasyTag:res resId="cc-km-manage-exportInfo"><button type="button" class="btn btn-sm btn-info btn-outline mr-5"  onclick="exportKmInfo('01')" i18n-content="导出"></button></EasyTag:res>
							<EasyTag:res resId="cc-km-manage-removeInfo"><button type="button" class="btn btn-sm btn-danger btn-outline mr-5" onclick="sceneListTmp.sceneDel()" i18n-content="批量删除"></button></EasyTag:res>
						</div>
					</div>
					<hr style="margin: 5px -15px">
					<div class="form-group">
						<div class="input-group input-group-sm">
							<span class="input-group-addon" i18n-content="场景名称"></span> 
							<input name="QUESTION_NAME" class="form-control input-sm">
						</div>
						<div class="input-group input-group-sm">
							<span class="input-group-addon" i18n-content="关键字"></span> 
							<input name="SEARCH_CODE" class="form-control input-sm">
						</div>
						<div class="input-group input-group-sm">
							<span class="input-group-addon" i18n-content="状态"></span> 
							<select class="form-control input-sm" name="INFO_STATUS">
								<option value="" i18n-content="请选择"></option>
								<option value="0" i18n-content="正常"></option>
								<option value="8" i18n-content="待同步"></option>
								<option value="7" i18n-content="待完善"></option>
								<option value="3" i18n-content="未到期"></option>
								<option value="4" i18n-content="过期"></option>
							</select>
						</div>
						<div class="input-group input-group-sm">
							<span class="input-group-addon" i18n-content="是否需要更新"></span> 
							<select class="form-control input-sm" name="NEED_UPDATE">
								<option value="" i18n-content="请选择"></option>
								<option value="Y" i18n-content="是"></option>
								<option value="N" i18n-content="否"></option>
							</select>
						</div>
						<div class="input-group input-group-sm">
							<button type="button" class="btn btn-sm btn-default" onclick="sceneListTmp.searchData(true)">
								<span class="glyphicon glyphicon-search" ></span> <span i18n-content="查询"></span>
							</button>
						</div>
					</div>
				</div>
				<div class="ibox-content">
					<table id="sceneListTable"></table>
				</div>
			</div>
		</div>
	</form>
	<script type="text/html" id="sceneOperationTpl">
  		<a class="layui-btn layui-btn-xs" lay-event="sceneListTmp.sceneEdit" i18n-content="编辑"></a>
  		<a class="layui-btn layui-btn-xs" lay-event="questionListTmp.synKmInfo" i18n-content="同步"></a>
		<a class="layui-btn layui-btn-xs" lay-event="questionListTmp.linkInfoList" i18n-content="关联知识"></a>
	</script>
</EasyTag:override>

<EasyTag:override name="script">
	<script src="./../km-question-common.js" type="text/javascript" ></script>
	<script type="text/javascript">
		jQuery.namespace("sceneListTmp");
		
		//查询数据
		sceneListTmp.searchData=function(flag){
			if(flag) {
				$("#sceneListForm").queryData({id:'sceneListTable',page: { curr: 1}});
			} else {
				$("#sceneListForm").queryData({id:'sceneListTable'});
			}
		};
		//重置
		sceneListTmp.reset = function(){
			$("#sceneListForm .form-group select").val("");
	    	$("#sceneListForm .form-group input").val("");
		};
		sceneListTmp.loadData = function () {
	       	$("#sceneListForm").initTableEx({
	           	mars:'questionDao.questionList',
	           	data: {infoType:'01'},
	           	id:'sceneListTable',
	           	event:'sceneDetail',
	           	height: 'full-180',
	           	pages: true,
	           	limit:15,
            	limits: [15,30,45,60,100],
	           	cols: [[
           			{
           				width:60,
           				field:'INFO_ID', 
           				title: getI18nValue('选择'), 
           				type:'checkbox'
           			},
            		{
           				field:'LAY_INDEX', 
           				title: getI18nValue('序号'),
           				align: 'center',
           				width:60,
						type:'numbers'
           			},
					{
						field: 'INFO_TOPIC',
						title: getI18nValue('场景标题'),
						align: 'left',
						minWidth:120,
						templet: function(row) {
							if(row.NEED_UPDATE == 'Y') {
								return '<span class="layui-badge-dot" style="position: relative; left: -4px;"></span>' + '<a href="javascript:;" onclick="KmCommon.showDetail(\'\',\''+row.INFO_ID+'\',\'\',\''+row.INFO_TOPIC+'\')">'+row.INFO_TOPIC+'</a>';
							} else {
								return '<a href="javascript:;" onclick="KmCommon.showDetail(\'\',\''+row.INFO_ID+'\',\'\',\''+row.INFO_TOPIC+'\')">'+row.INFO_TOPIC+'</a>';
							}
						}
					},
					{
						field: 'SEARCH_CODE',
						title: getI18nValue('关键字'),
						align: 'left',
						minWidth:120,
						templet: function(row) {
							if(row.SEARCH_CODE) {
								var reg1 = new RegExp("-","g");
								var reg2 = new RegExp(";","g");
								var keyCodeArr = row.SEARCH_CODE.replace(reg1,",").replace(reg2,",").split(',');
								var html = '';
								for(var i in keyCodeArr) {
									if(keyCodeArr[i]){
										html += '<span class="layui-badge layui-bg-green mr-5">' + keyCodeArr[i] + '</span>';
									}
								}
								return html;
							} else {
								return '';
							}
						}
					},
					{
						field: 'INFO_STATUS',
						title: getI18nValue('状态'),
						align: 'center',
						width:120,
						templet: function(row) {
							var status = getDictTextByCode('KM_INFO_STATUS',row.INFO_STATUS);
							if(row.INFO_STATUS == '0') {
								return '<span class="layui-badge-rim" style="background-color: #7fd332; color: white; border: 0px;">' + status + '</span>';
							} else if('2,4,5'.indexOf(row.INFO_STATUS) > -1) {
								return '<span class="layui-badge">' + status + '</span>';
							} else if('1,7,8'.indexOf(row.INFO_STATUS) > -1){
								return '<span class="layui-badge layui-bg-orange">' + status + '</span>';
							} else {
								return '<span class="layui-badge-rim">' + status + '</span>';
							}
						}
					},
					{
						field: 'CREATE_DATE',
						title: getI18nValue('创建时间'),
						align: 'center',
						minWidth:100,
					},
					{
						field: 'UPDATE_DATE',
						title: getI18nValue('最近维护时间'),
						align: 'center',
						minWidth:100
					},
					{
						field: 'OPERATION',
						title: getI18nValue('操作'),
						align: 'center',
						minWidth:100,
						templet: '#sceneOperationTpl'
					}
				]],
				done:function() {
					execI18n();
				}
	    	});
	   	}
	
		sceneListTmp.sceneEdit = function(obj){
			if(obj) {
				popup.layerShow({
					type : 1,
					title : getI18nValue('场景维护'),
					shadeClose:false,
					offset : 'r',
					area : [ '50%', '100%' ],
					url:"${ctxPath}/pages/km/question/scene/sceneEdit.jsp",
					data:{
						id: obj.INFO_ID
					}
				});
			} else {
				popup.layerShow({
					type : 1,
					title : getI18nValue('添加场景'),
					shadeClose:false,
					offset : 'r',
					area : [ '50%', '100%' ],
					url:"${ctxPath}/pages/km/question/scene/sceneAdd.jsp",
					data:{}
				});
			}
		}
		
		// 删除场景库
		sceneListTmp.sceneDel = function(obj){
			KmCommon.deleteKmInfo(KmCommon.getCheckedIdsLayui('sceneListTable'), '01', sceneListTmp.searchData);
		}
		
		$(function(){
			$("#sceneListForm").render();
			sceneListTmp.loadData();
		});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>