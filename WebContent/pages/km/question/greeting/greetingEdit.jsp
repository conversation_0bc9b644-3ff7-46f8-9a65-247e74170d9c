<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="">编辑寒暄</title>
	<style>
		.layui-form-label {
			width: 110px;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form autocomplete="off"  id="greetingEditForm" class="layui-form" method="post"  autocomplete="off" >
		<div class="layui-form-item">
        	<label class="layui-form-label required"><span i18n-content="标题"></span>:</label>
            <div class="layui-input-block">
            	<input type="text" name="GREETING_TITLE" id="GREETING_TITLE" i18n-placeholder="请输入寒暄的标题" class="layui-input" required/>
            </div>
        </div>
		<div class="layui-form-item">
        	<label class="layui-form-label"><span i18n-content="关键字"></span>:</label>
            <div class="layui-input-block">
            	<input type="text" name="KM_INFO.KEY_WORD" id="keyWord" class="layui-input tagsInput" autocomplete="off">
            </div>
        </div>
		<div class="layui-form-item">
        	<label class="layui-form-label"><span i18n-content="相似问"></span>:</label>
            <div class="layui-input-block">
            	<input type="text" name="KM_INFO.QUESTION" id="question" class="layui-input tagsInput" autocomplete="off">
            </div>
        </div>
        <div class="layui-form-item">
        	<div class="layui-inline">
	        	<label class="layui-form-label required"><span i18n-content="有效期"></span>:</label>
	            <div class="layui-input-block">
	            	<input type="text" name="KM_INFO.VALIDITY_DATE" id="validityDate" class="layui-input" autocomplete="off">
	            </div>
	    	</div>
	    	<div class="layui-inline">
	    		<button type="button" class="layui-btn layui-btn-xs" onclick="changeValidityDate('01')" i18n-content="三个月"></button>
        		<button type="button" class="layui-btn layui-btn-xs" onclick="changeValidityDate('02')" i18n-content="半年"></button>
        		<button type="button" class="layui-btn layui-btn-xs" onclick="changeValidityDate('03')" i18n-content="一年"></button>
        		<button type="button" class="layui-btn layui-btn-xs" onclick="changeValidityDate('04')" i18n-content="永久"></button>
        	</div>
        </div>
		<div class="layui-form-item ml-15">
           	<div>
           		<div style="position: relative;">
					<fieldset class="layui-elem-field config-content"><legend><span i18n-content="答案"></span><button class="btn btn-xs btn-info" type="button" onclick="greetingEdit.contentAdd()" style="position: absolute;right: 15px;" i18n-content="新增"></button></legend>
						
			 		</fieldset>
				</div>
	        </div>
       	</div>
       	<div class="layui-form-item">
        	<label class="layui-form-label"><span i18n-content="附件"></span>:</label>
            <div class="layui-input-block">
				<input type="hidden" name="LINK_FILE1" id="LINK_FILE1" />
				<div id="file-content"></div>
				<button class="btn btn-sm" type="button" onclick="greetingEdit.uploadFile()" i18n-content="+添加"></button>
				<span style="color:red;display: inline-block;" i18n-content="(注:建议上传的知识附件小于10M)"></span>
            </div>
        </div>
	 	<div class="layui-form-item">
        	<div style="text-align: center;">
            	<button type="button" class="layui-btn" onclick="greetingEdit.ajaxSubmitForm()" i18n-content="保存">&emsp;&emsp;</button>
                <button type="button" class="layui-btn layui-btn-primary" onclick="popup.layerClose(this)" i18n-content="关闭">&emsp;&emsp;</button>
            </div>
        </div>
	</form>
	<script type="text/html" id="contentTmp">
		<div class="layui-field-box div-content-{{:index}}">
			<div class="day-data">
				<span class="layui-badge-rim mr-15" style="position: relative; top: -66px;">{{:index + 1}}</span>
				<textarea class="layui-input" style="width: 540px;height: 80px;display: inline;" rows="2" cols="2">{{:content}}</textarea>
				<button class="btn btn-xs btn-danger pull-right" onclick="greetingEdit.contentDel({{:index}})" style="float:right;position: relative; top: 0px;" type="button" i18n-content="删除"></button>
				<div class="ml-30 pl-15">
					{{for channelList}}
						<input type="checkbox" name="channel" value="{{:CHANNEL_KEY}}" {{if #parent.parent.data.channelKey != null && #parent.parent.data.channelKey.indexOf(CHANNEL_KEY) != -1}}checked=""{{/if}} lay-skin="primary" title="{{:NAME}}">
					{{/for}}
				</div>
			</div>
		</div>
	</script>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript" src="${ctxPath}/static/js/time.js"></script>
	<script type="text/javascript">
	jQuery.namespace("greetingEdit");
	$(function(){
		ajax.remoteCall('${ctxPath}/webcall?action=questionDao.channelList',{},function(result){
			console.log(result);
    		if(result) {
    			greetingEdit.channelList = result.data;
			}
    	});
		ajax.remoteCall('${ctxPath}/webcall?action=questionDao.questionInfo',{ID:'${param.id}'},function(result){
			var validityDate = '';
    		if(result.state == 1) {
				$("#GREETING_TITLE").val(result.infoJson.INFO_TOPIC);  
				var reg1 = new RegExp("-","g");
				var reg2 = new RegExp(";","g");
				$("input[name='KM_INFO.KEY_WORD']").val(result.infoJson.SEARCH_CODE.replace(reg1,",").replace(reg2,","));
				$("input[name='KM_INFO.QUESTION']").val(result.questions.join(","));
				if(result.infoJson.VALID_DATE && result.infoJson.EXPIRE_DATE) {
					validityDate = result.infoJson.VALID_DATE + ' ~ ' + result.infoJson.EXPIRE_DATE;
				}
				
				greetingEdit.itemId = result.infoJson.ITEM_ID;
				
				var fileArray = result.infoJson.EX_FILE;
				if (fileArray) {
					fileArray = $.parseJSON(fileArray);
					greetingEdit.fileArray = fileArray;
					greetingEdit.getFileByIds(fileArray);
				}
				
				var contentList = result.contentList;
				for(var i = 0; i < contentList.length; i++ ) {
					var contentJson = contentList[i];
					
					var data = {channelList:greetingEdit.channelList};
					data.index = greetingEdit.index;
					data.content = contentJson.CONTENT;
					data.channelKey = contentJson.channelKey;
					var tmp = $.templates("#contentTmp");
					var html=tmp.render(data);
					$(".config-content").append(html);
					layui.use(['form'],function(){
						var form = layui.form;
						form.render();
					});
					greetingEdit.index = greetingEdit.index + 1;
				}
			}
    		execI18n();
    		layui.use(['form','laydate','tagsInput'],function(){
    			var laydate = layui.laydate;
    			var tagsInput = layui.tagsInput;
    			var form = layui.form;
    			laydate.render({
    				elem: '#validityDate',
    				range: '~',
    				value: validityDate
    			});
    			$('.tagsInput').tagsInput();
    			form.render();
    		});
    	});
		execI18n();
	});
	
	greetingEdit.channelList = [];
	greetingEdit.index = 0;
	greetingEdit.contentAdd = function() {
		if($("[class*='div-content-']").length == greetingEdit.channelList.length) {
			layer.msg(getI18nValue("答案数不能超过渠道数哦!"),{icon: 5, time:1200});
			return;
		}
		var data = {channelList:greetingEdit.channelList};
		data.index = greetingEdit.index;
		var tmp = $.templates("#contentTmp");
		var html=tmp.render(data);
		$(".config-content").append(html);
		layui.use(['form'],function(){
			var form = layui.form;
			form.render();
		});
		greetingEdit.index = greetingEdit.index + 1;
		execI18n();
	}
	greetingEdit.contentDel = function(index) {
		$(".div-content-" + index).remove();
	}
	greetingEdit.ajaxSubmitForm = function(flag){
		var greetingTitle = $("#GREETING_TITLE").val();
		if(!greetingTitle) {
			layer.msg(getI18nValue("标题不能为空"),{icon: 5, time:1200});
			$("#GREETING_TITLE").focus();
			return;
		}
		var keyWord = [];
		$("input[name='KM_INFO.KEY_WORD']").next().find(".tag").each(function(i,d){
			keyWord.push($(d).text().trim());
		});
		var question = [];
		$("input[name='KM_INFO.QUESTION']").next().find(".tag").each(function(i,d){
			question.push($(d).text().trim());
		});
		
		var channelValue = '';
		$("input[name='channel']:checked").each(function(i,d){
			if(channelValue.indexOf("-" + $(d).val()) != -1) {
				layer.msg(getI18nValue("答案渠道不能重复哦!"),{icon: 5, time:1200});
				channelValue = '-';
				return false;
			}
			channelValue = channelValue + "-" + $(d).val();
		});
		if(channelValue == '-') {
			return;
		}
		if(channelValue == '') {
			layer.msg(getI18nValue("答案渠道不能为空!"),{icon: 5, time:1200});
			return;
		} else if(channelValue == '-') {
			return;
		}
		var startDate = '';
		var endDate = '';
		var validityDate = $("#validityDate").val();
		if(validityDate == '') {
			layer.msg(getI18nValue("有效期不能为空"),{icon: 5, time:1200});
			$("#validityDate").focus();
			return;
		}
		startDate = validityDate.split(" ~ ")[0].trim();
		endDate = validityDate.split(" ~ ")[1].trim();
		var content = [];
		$("[class*='div-content-']").each(function(i,d){
			var contentVal = $(d).find("textarea").val();
			var contentData = {};
			var channelKey = '';
			$(d).find("input[name='channel']:checked").each(function(i,d){
				channelKey = channelKey + "-" + $(d).val();
			});
			contentData.channelKey = channelKey;
			contentData.contentVal = contentVal;
			content.push(contentData);
		});
		var data = {};
		data.questionTitle = greetingTitle;
		data.infoType = "04";
		data.keyWord = keyWord.join(";");
		data.question = question;
		data.startDate = startDate;
		data.endDate = endDate;
		data.content = content;
		data.EX_FILE = greetingEdit.fileArray;
		data.ID = '${param.id}';
		ajax.remoteCall("${ctxPath}/servlet/question?action=questionEdit", data, function(result) { 
			if(result.state == 1){
				layer.msg(result.msg,{icon:1,time:1200},function(){
					layer.closeAll();
					greetingListTmp.searchData();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});  
 	}
	
	greetingEdit.uploadFile = function (setting, callback) {
		if (greetingEdit.fileArray.length > 4) {
			layer.msg(getI18nValue("只允许上传5个附件"));
		} else {
			popup.layerShow($.extend({}, { url: '/cc-km/pages/km/info/files/upload-file.jsp',data:{itemId: greetingEdit.itemId}, title: getI18nValue('上传文件'), type: 1, offset: '20px', area: ['400px', '200px'], shadeClose: false }));
		}
	}

	greetingEdit.getFileHtml = function (id, name, url) {
		return '<div><a href="' + url + '" target="_blank" file-id="' + id + '">' + name + '</a><span class="glyphicon glyphicon-remove" style="cursor:pointer;" onclick="greetingEdit.delFile(this)"></span></div>'
	}
	greetingEdit.delFile = function (obj) {
		var index = $(obj).parent().index();
		$(obj).parent().remove();
		greetingEdit.fileArray.splice(index,1);
	}

	greetingEdit.fileArray = [];

	var uploadFileCallBack = function (list) {
		for (j = 0; j < list.length; j++) {
			var data = list[j];
			$('#file-content').append(greetingEdit.getFileHtml(data.id, data.name, data.url));
			
			var fileArray = greetingEdit.fileArray;
			if(!fileArray) {
				fileArray = [];
			}
			fileArray.push(data);
			greetingEdit.fileArray = fileArray;
		}
	}

	greetingEdit.getFileByIds = function (fileArray) {
		if (fileArray) {
			for (var i in fileArray) {
				var data = fileArray[i];
				$('#file-content').append(greetingEdit.getFileHtml(data.id, data.name, data.url));
			}
		}
	}
	
	function changeValidityDate(type) {
		var date = new Date();
		if(type == "01") {
			date.setMonth(date.getMonth() + 3);
       		$("#validityDate").val(getTodayDate() + " ~ " + dateFormat(date,"yyyy-MM-dd"));
       	}else if(type == "02") {
       		date.setMonth(date.getMonth() + 6);
       		$("#validityDate").val(getTodayDate() + " ~ " + dateFormat(date,"yyyy-MM-dd"));
       	}else if(type == "03") {
       		date.setMonth(date.getMonth() + 12);
       		$("#validityDate").val(getTodayDate() + " ~ " + dateFormat(date,"yyyy-MM-dd"));
       	}else if(type == "04") {
       		date.setMonth(date.getMonth() + 1200);
       		$("#validityDate").val(getTodayDate() + " ~ " + dateFormat(date,"yyyy-MM-dd"));
    	}
	}
	
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>