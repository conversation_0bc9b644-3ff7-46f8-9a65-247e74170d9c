<%@page import="org.easitline.common.utils.calendar.EasyDate"%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="相似问列表"></title>
	<style type="text/css">
		*::-webkit-scrollbar-thumb {
		    background: #3DB6A4;
		    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .2) 50%, rgba(255, 255, 255, .2) 75%, transparent 75%, transparent);
		}
		*::-webkit-scrollbar {
		    width: 6px;
		    overflow-y: scroll;
		}
		*::-webkit-scrollbar-track {
		    background-color: #eee;
		}
		*::-webkit-scrollbar-track-piece {
		    background-color: #eee;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form class="form-inline" id="questionForm">
		<input type="hidden" name="templateItemId" value="${param.templateItemId }" />
		<input type="hidden" name="templateItemTitle" value="${param.templateItemTitle }" />
		<input type="hidden" name="type" value="${param.type }" />
		<input type="hidden" name="qType" value="2" />
		<div class="ibox">
			<div class="ibox-title clearfix" id="divId">
 				<div class="form-group">
					<div class="input-group input-group-sm pull-left mr-10" style="width: 240px;">
						<span class="input-group-addon" i18n-content="相似问"></span>
						<input name="questions" class="form-control input-sm">
					</div>
					<div class="input-group input-group-sm pull-left">
						<button type="button" data-event="enter" class="btn btn-sm btn-default" onclick="questionFun.query()"><span class="glyphicon glyphicon-search"></span> <span i18n-content="查询"></span></button>
				   	</div>
 				</div>
			</div>
			<div class="ibox-content">
				<table id="questionTable"></table>
			</div>
		</div>
		<div class="layer-foot text-c">
			<c:if test="${param.type == 2 }" >
				<button type="button" class="btn btn-primary btn-sm" onclick="questionFun.ajaxSubmitForm()" id="saveBtn" i18n-content="提交">  </button>
			</c:if>
		    <button type="button" class="btn btn-default btn-sm ml-20" onclick="popup.layerClose(this)" i18n-content="关闭">  </button>
		</div>
	</form>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		var questionFun = {
			query : function(flag){
				$("#questionForm").queryData({id:'questionTable'});
			},
			//重置
			reset : function(){
				$("#divId select").val("");
		    	$("#divId input").val("");
			},
			init : function(){
				$("#questionForm").initTableEx({
	            	mars:'kmTemplate.questionList',
	            	id:'questionTable',
	            	page: false,
	            	limit: 100,
	            	height: 'full-200',
	            	cols: [[
		            	{type:'checkbox'},
						{width:60,field:'ID', title: getI18nValue('序号'), type:'numbers'},
			            {
			            	field: 'ITEM_TITLE',
			            	title: getI18nValue('知识项标题'),
							align:'center'
						},{
							field: 'QUESTIONS',
							title: getI18nValue('相似问'),
							align:'center'
						}
					]],
					done: function() {
						execI18n();
					}
	            });
			},
			ajaxSubmitForm: function() {
				var titleArray = [];
				var data = table.checkStatus('questionTable').data;
				for(var i in data) {
					titleArray.push(data[i].QUESTIONS);
				};
				parent.itemEdit.setQuestion(titleArray);
				popup.layerClose(this);
			}
		}
	  	$(function() {
	  		questionFun.init();
	  	});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>