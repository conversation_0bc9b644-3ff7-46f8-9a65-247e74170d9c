<%@page language="java" contentType="text/html;charset=UTF-8"%>
<%@page import="com.yunqu.cc.km.base.Constants"%>
<%@page import="org.easitline.common.core.context.AppContext"%>

<%@ include file="/pages/common/global.jsp"%>
<%
	AppContext appContext = AppContext.getContext(Constants.APP_NAME);
	request.setAttribute("assignSwitch", appContext.getProperty("assignSwitch", "1"));
	request.setAttribute("feedbackType", appContext.getProperty("feedbackType", "1"));
%>
<EasyTag:override name="head">
	<title i18n-content="知识点查询"></title>
	<style>
		.km-info-item{padding: 8px 0;border-bottom: 1px dashed  #ddd;}
		.km-info-item a{font-size:16px;text-decoration: underline;line-height: 30px;color: #0000CC;}
		.km-info-item .status{}
		.km-info-item .sub-title{color:#999;margin-top: 0px;font-size: 12px;}
		.km-info-item .sub-content{
			color: #333;padding: 5px;text-overflow: clip;
			overflow: hidden;word-wrap: break-word;font-size: 13px;   
		 	line-height: 1.54;word-break: break-word;max-height: 100px;
		 }
		 .list-notice li{padding: 8px 3px;font-size: 13px;}
		 .list-notice li .notice-info{color: #0000CC;font-size: 13px;}
		 .list-notice li .notice-info .notice-date{color: #666;}
		 .table a{color: #0000CC;}
		 
		 .searchBox .form-control{margin-right: -2px;}
		 .searchBox .input-group-addon{
		 	background-color: #3385ff;
		 	color: #fff;
		 }
		 #msg{font-size: 12px;color: #999;margin-left: 20px;}
		.km-ul{list-style: none;margin:0;padding:0}
		.km-ul li{margin:0;padding:0;margin-left: 20px;height: 30px;line-height: 30px;}
		.km-ul li a{color:#20a0ff}
		.km-ul li .title{margin: 0;padding: 0;text-decoration: none;width: 190px;overflow: hidden;display: inline-block;text-overflow: ellipsis;white-space: nowrap;}
		.km-ul li .sub-title{float:right;margin-right:5px;}
		
		
		.km-keyCode-list{width:350px;;z-index:3;position: absolute; background: #fff;padding:0;margin:0;top:36px;left:0px;border: 1px solid #ddd;border-radius: 3px;border-top: none;}
		.km-keyCode-list li{padding:4px 8px;;margin:0;list-style: none;cursor: pointer;}
		.km-keyCode-list li:hover{color:#00a2ee;}
		.fastNav a{color: #0000CC;text-decoration: underline;font-size: 13px;}
	</style>
	<link href="/easitline-static/lib/layui/css/layui.css" rel="stylesheet">
	<style>

		.layui-col-md2{width: 220px!important;}
		.layui-col-md7{width: calc(100% - 550px)!important;margin-left: 10px;}
		.layui-col-md3{width: 310px!important;margin-left: 10px;}
		.km-info-item .fa{
			opacity:0;
		}
		.km-info-item:hover .fa{
			opacity:1;
		}
		.showTree{
			width: calc(100% - 550px)!important;margin-left: 10px;
		}
		.hideTree{
			width: calc(100% - 330px)!important;margin-left: 0px;
		}
		.shadow,.layui-card,.ibox{
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-moz-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
		}
		.msgbox{
			position: absolute;right: 54px;top: 0px;display: none;padding: 5px;width: 200px;font-size: 12px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;z-index: 9999;border-radius: 5px;
			border:1px solid #9BDF70;
			background-color:#F0FBEB;
		}
		.toSeeA:visited{
			color: #715393 !important;
		}
		.toSeeA:hover{
			color: red !important;
		}
		#ztree{
			width: 100% !important;
			overflow-x: auto !important;
		}
		
		.slimScrollDiv{
			overflow: visible !important;
		}
		.slimScrollDiv::-webkit-scrollbar {
		  display: none; /* Chrome Safari */
		  width: 0px;
		}
	</style>
	<link rel="stylesheet" href="${ctxPath}/static/css/ZdCascader.css" type="text/css">
</EasyTag:override>
<EasyTag:override name="content">
	<div class="layui-row">
		<form id="searchForm">
			<input type="hidden" name="dirId">
			<input type="hidden" name="accessType" value="0">
			<div class="layui-col-md2">
				<div class="layui-card">
					<div class="layui-card-header"><span i18n-content="知识点目录"></span><span id="sub" ><i class="layui-icon layui-icon-about mr-10" style="color: #1E9FFF;"></i></span><span id="titleAndTime"> </span></div>
					<div class="layui-card-body">
						<div class="input-group input-group-sm" style="margin:5px;">
							<input class="form-control input-sm" type="text" name="treeDirName" i18n-placeholder="输入名称搜索">
							<span class="input-group-addon btn" onclick="KmInfoList.searchDir()">
								<span class="glyphicon glyphicon-search"></span>
							</span>
						</div>
						<div class="ztree" id="ztree" data-mars="KmDirDao.getDirTree" data-setting="{callback: {onClick: KmInfoList.zTreeOnClick}}"></div>
					</div>
				</div>
			</div>
			<div class="layui-col-md7 form-inline">
				<div class="ibox">
					<div class="ibox-title clearfix">
						<div class="form-group">
							<div class="input-group searchBox" style="width: 500px;">
								<input name="keyCode" type="text" class="form-control" maxlength="30" i18n-placeholder="请输入标题/内容,回车搜索" value="${param.keyCode}" autocomplete="off">
								<span class="input-group-btn">
									<button class="btn btn-primary" onclick="KmInfoList.query()" type="button" i18n-content="查询"></button>
								</span>
							</div>
							<span id="msg"></span>
							<div class="input-group pull-right">
								<button type="button" class="btn btn-sm btn-link" onclick="KmInfoList.moreQuery()"><i class="fa fa-filter"></i>
									<span i18n-content="高级搜索"></span></button>
								<button type="button" class="btn btn-sm btn-link" onclick="KmInfoList.setTree()"><i class="fa fa-tree"></i>
									<span i18n-content="目录"></span></button>
							</div>
						</div>
						<div class="form-group" id="more-query-content" style="display: none;">
							<div class="input-group input-group-sm">
								<span class="input-group-addon" i18n-content="标题"></span>
								<input name="infoTopic" type="text" class="form-control input-sm" maxlength="30" style="width:120px" i18n-placeholder="请输入标题">
							</div>
							<div class="input-group input-group-sm hidden">
								<span class="input-group-addon" i18n-content="内容"></span>
								<input name="content" type="text" class="form-control input-sm" maxlength="30" style="width:120px" i18n-placeholder="请输入内容">
							</div>
							<div class="input-group input-group-sm">
								<span class="input-group-addon" i18n-content="渠道"></span>
								<select name="channelKey" class="form-control input-sm" data-mars="KmInfoDao.channelList">
									<option value="" i18n-content="请选择"></option>
								</select>
							</div>
							<div class="input-group input-group-sm" style="display: none">
								<button type="button" class="btn btn-sm btn-default" onclick="KmInfoList.query()"><span class="glyphicon glyphicon-search"></span>
									<span i18n-content="查询"></span></button>
							</div>
							<div class="btn-group btn-group-sm pull-right" style="margin-top:5px;">
								<button type="button" class="btn btn-success btn-outline" i18n-title="添加到我的收藏" onclick="KmInfoList.addToMyFavor()"
								 i18n-content="+添加收藏"></button>
							</div>
							<div class="btn-group btn-group-sm pull-right mr-5" style="margin-top:5px;">
								<button type="button" class="btn btn-sm btn-info" onclick="KmInfoList.feedback()" i18n-content="反馈"></button>
							</div>
						</div>
					</div>
					<div class="ibox-content" style="min-height:568px;">
						<div data-container="el" id="el" data-page-hide="true">
							<div style="margin:40px 0;font-size:14px;" i18n-content="请选择一个目录进行查看!"></div>
						</div>
						<div class="row paginate mt-30">
							<jsp:include page="/pages/common/pagination.jsp" />
						</div>
						<script id="tpl" type="text/x-jsrender">
							{{for list}}
								<div class="km-info-item">
									<a  href="javascript:void(0)" onclick="KmCommon.showDetail('{{:ID}}','{{:INFO_ID}}','{{:CHANNEL_KEY}}','{{getInfoTopic:INFO_TITLE}}')"  i18n-title="知识标题">
										{{if INFO_TYPE == "03" }}<span i18n-title="知识点标题">[{{:INFO_TITLE}}]</span>{{/if}}
										{{:TITLE}}
									</a>
									<div class="pull-right">
										<span class="fa" i18n-title="反馈" style="cursor:pointer; font-size:12px;" onclick="KmInfoList.feedback('{{:INFO_ID}}','{{:ID}}')" i18n-content="反馈"></span>											
										<span class="fa fa-crosshairs" i18n-title="定位所在目录" style="cursor:pointer" onclick="KmCommon.goToDir('{{:DIR_ID}}')">&nbsp;</span>											
										<label class="checkbox checkbox-info checkbox-inline"><input type="checkbox" value="{{:INFO_ID}}"><span></span></label>
									</div>
									{{isShowContent:#data}}
									<div class="sub-title" style="position: relative;">
										<span><span i18n-content="创建时间"></span>:{{:CREATE_DATE}}</span> &nbsp;&nbsp;
										<span><span i18n-content="浏览次数"></span>:{{:CLICK_COUNT||0}}</span>
										<!-- <div class="toSee" onmouseover="toSee(this)" onmouseleave="toCantSee(this)" style="position: absolute;right: 10px;top: 0px;padding-left: 10px;height: 30px;">附件预览
											<div class="msgbox">
													{{for EX_FILE}}
														<a class="toSeeA" style="font-size: 14px;color: #2DA5FB;" href="{{:PATH}}" target="_blank">{{:NAME}}</a><br>
													{{/for}}
											</div>
										</div> -->
									</div>
								</div>
							{{/for}}					         
						</script>
					</div>
				</div>
			</div>
		</form>
		<div class="layui-col-md3">
			<form id="rightForm">
				<input type="hidden" name="pageSize" value="8">
				<input type="hidden" name="newInfoPageSize" value="8">
				<div class="layui-card">
					<div class="layui-card-header" i18n-content="快捷入口"></div>
					<div class="layui-card-body fastNav">
						<a class="mr-20" href="javascript:KmInfoList.infoSearchLog()" i18n-content="关键字排名"></a>
						<a class="mr-20" href="javascript:KmInfoList.historyVisit()" i18n-content="浏览历史"></a>
						<a class="mr-20" href="javascript:KmInfoList.myFavor()" i18n-content="我的收藏"></a>
						<EasyTag:res resId="cc-km-manage-exportInfo"><a class="mr-20" href="javascript:KmInfoList.exportNewInfo()" i18n-content="导出新知识"></a></EasyTag:res>
						<EasyTag:res resId="cc-km-manage-exportInfo"><a class="mr-20" href="javascript:KmInfoList.exportSelectedInfo()" i18n-content="导出所选知识点"></a></EasyTag:res>
					</div>
				</div>
				<div class="layui-card">
					<div class="layui-card-header">
						<span i18n-content="最新知识点"></span>
						<span class="pull-right more">
							<a href="javascript:void(0)" onclick="KmInfoList.moreNewInfo()" i18n-content="更多"></a>
						</span>
					</div>
					<div class="layui-card-body">
						<ul id="new-el" class="list-notice" data-template="new-tpl" data-container="new-el" data-mars="KmInfoDao.newList"></ul>
					</div>
				</div>
				<div class="layui-card">
					<div class="layui-card-header"><span i18n-content="点击排行榜"></span>
						<span class="pull-right more"><a href="javascript:void(0)" onclick="KmInfoList.moreRankInfo()" i18n-content="更多"></a></span>
					</div>
					<div class="layui-card-body">
						<div id="rank-content">
							<table data-mars="KmInfoDao.clickCountList" class="table table-condensed">
								<thead>
									<tr>
										<td style="white-space: nowrap;" i18n-content="标题"></td>
										<td style="white-space: nowrap;text-align: center" i18n-content="点击量"></td>
									</tr>
								</thead>
								<tbody id="rank-el"></tbody>
							</table>
						</div>
					</div>
				</div>
			</form>
		</div>
		<div class="ibox-content table-responsive">
          	<div class="stat-desc" id="statDesc" style="display:none;color:#676A6C;">
           		<p i18n-content="备注:"></p>
           		<p i18n-content="1、知识目录中的数据来源于角色|部门权限中配置的查询目录"></p>
           		<p i18n-content="2、查询的库跟据系统管理->参数配置->知识库参数配置中的知识查询配置"></p>
           		<p i18n-content="3、知识查询查询的内容默认为主内容，可通过高级查询中的渠道来指定查询内容"></p>
          	</div>
       	</div>
		<script id="new-tpl" type="text/x-jsrender">
			{{for  list}}
				<li onclick="KmCommon.showDetail('','{{:INFO_ID}}','','{{:INFO_TOPIC}}')">
					<div class="notice-info" title="{{:INFO_TOPIC}}"><span class="notice-date">{{formatDate:CREATE_DATE}}</span><h5>{{:INFO_TOPIC}}</h5></div>
				</li>
			{{/for}}					         
		</script>
		<script id="rank-tpl" type="text/x-jsrender">
			{{for  list}}
				<tr>
					<td><span class="layui-badge-rim">{{:#index+1}}</span> <a  href="javascript:void(0)" onclick="KmCommon.showDetail('{{:ITEM_ID}}','{{:INFO_ID}}','','{{:TITLE}}')" title="[{{:INFO_TOPIC}}]{{:TITLE}}">[{{:INFO_TOPIC}}]{{:TITLE}}</a></td>
					<td style="text-align:center">{{:CLICK_COUNT}}</td>
				</tr>
			{{/for}}					         
		</script>
	</div>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
		$(function() {
			KmInfoList.treeShow();
			KmInfoList.init();
		});
	
		function toSee(params) {
			if ($($($(params).children()[0])).html().trim() == '') {

			} else {
				$($(params).children()[0]).show()
			}
		}

		function toCantSee(params) {
			$($(params).children()[0]).hide()
		}
		$.views.converters("getInfoTopic", function(val) {
			return $('<span>' + val + '</span>').text();
		});
		$.views.converters("formatDate", function(val) {
			return KmCommon.dateFormatter(val);
		});
		$.views.converters("isShowContent", function(data) {
			//处理高亮字段
			var hlCols = data.hlCols;
			if (hlCols != null && hlCols.length > 0) {
				var html = [];
				for (var i = 0; i < hlCols.length; i++) {
					var hlCol = hlCols[i];
					var val = data[hlCol] || '';
					if (hlCol == 'CONTENT') {
						html.push('<div class="sub-content" i18n-title="知识内容"><span i18n-content="知识内容"></span> : ' + val + '</div>');
					}
					/* else if(hlCol == 'TOPIC_CODE'){
						html.push('<div class="sub-content" title="标题简拼">标题简拼 : '+val+'</div>');
					}else if(hlCol == 'SEARCH_CODE'){
						html.push('<div class="sub-content" title="关键字">关键字 : '+val+'</div>');
					}else if(hlCol == 'attr_content'){
						html.push('<div class="sub-content" title="关键字简拼">关键字简拼 : '+val+'</div>');
					}*/
					else if (hlCol == 'ATTR_FILE_0' || hlCol == 'ATTR_FILE_1' || hlCol == 'ATTR_FILE_2' || hlCol == 'ATTR_FILE_3' ||
						hlCol == 'ATTR_FILE_4') {
						html.push('<div class="sub-content" i18n-title="附件内容"><span i18n-content="附件内容"></span> : ' + val + '</div>');
					}
				}
				return html.join('');
			}
			return '<div class="sub-content" i18n-title="知识内容"><span i18n-content="知识内容"></span> : ' + data['CONTENT'] +
				'</div>';
		});
		var KmInfoList = {
			assignSwitch: '${assignSwitch}',
			query: function() {
				var dirId = $('#searchForm').find('[name="dirId"]').val();
				var keyCode = $('#searchForm').find('[name="keyCode"]').val();
				var infoTopic = $('#searchForm').find('[name="infoTopic"]').val();
				var searchCode = $('#searchForm').find('[name="searchCode"]').val();
				var topicCode = $('#searchForm').find('[name="topicCode"]').val();
				if (!dirId && !keyCode && !infoTopic && !searchCode && !topicCode) {
					layer.msg(getI18nValue('请输入查询条件或者选择一个查询目录!'));
					return;
				}
				$("#searchForm").searchData({
					el: $('#el'),
					success: function(results) {
						var result = results['KmInfoDao.list'];
						result.data = JSON.parse(decodeURIComponent(result.data.replace(/\+/g, '%20')));

						var elTpl = $.templates("#tpl");
						var elHtml = elTpl.render({
							list: result.data
						});
						$("#el").html(elHtml);

						if (result.state == '0') {
							$('#el').html(
								'<div style="margin:40px 0;font-size:16px;text-align:center"><i class="fa fa-exclamation-circle">&nbsp;</i>' +
								result.msg + '</div>');
						} else {
							if (result.totalRow == 0) {
								var html =
									'<div style="margin:40px 0;font-size:14px;text-align:center"><span i18n-content="搜索不到相关知识"></span>';
								if (KmInfoList.assignSwitch == 'true') { //需要反馈流程的才有按钮
									html =
										'<div style="margin:40px 0;font-size:14px;text-align:center"><span i18n-content="搜索不到相关知识，是否向管理员反馈？"></span><button type="button" class="btn btn-sm btn-info" onclick="KmInfoList.feedback()" i18n-content="反馈"></button></div>';
								}
								$('#el').html(html);
							}
							if (result.totalRow > 0) {
								var keyCode = $('#searchForm').find('[name="keyCode"]').val();
								var beginNum = ((result.pageNumber - 1) * result.pageSize);
								beginNum = beginNum == 0 ? 1 : beginNum;
								if (keyCode) {
									$("#msg").html(getI18nValue('搜索') + " <font color='red'>" + keyCode + "</font> " + getI18nValue('获得约') +
										result.totalRow + " " + getI18nValue('条结果') + "," + getI18nValue('以下是') + beginNum + "-" + (result.pageNumber *
											result.pageSize) + getI18nValue('条') + "。");
								} else {
									$("#msg").html(getI18nValue('知识库约') + result.totalRow + " " + getI18nValue('条') + "," + getI18nValue(
										'以下是') + beginNum + "-" + (result.pageNumber * result.pageSize) + getI18nValue('条') + "。");
								}
							} else {
								var keyCode = $('#searchForm').find('[name="keyCode"]').val();
								$("#msg").html(getI18nValue('搜索') + " <font color='red'>" + keyCode + "</font> " + getI18nValue('未找到知识记录') +
									"。");
							}
						}
						execI18n();

						$(".sub-content").click(function() {
							if ($(this).css('max-height') == '100px') {
								$(this).css('max-height', '1000px');
							} else {
								$(this).css('max-height', '100px');
							}
						});
					}
				});
			},
			treeShow: function() {
				var state = localStorage.getItem("kmTree");
				if (state == 'close') {
					$(".layui-col-md2").hide();
					$(".layui-col-md7").removeClass("showTree");
					$(".layui-col-md7").addClass("hideTree");
				} else {
					$(".layui-col-md2").show();
					$(".layui-col-md7").removeClass("hideTree");
					$(".layui-col-md7").addClass("showTree");
				}
			},
			setTree: function() {
				var state = localStorage.getItem("kmTree");
				if (state == 'close') {
					localStorage.setItem("kmTree", 'open');
				} else {
					localStorage.setItem("kmTree", 'close');
				}
				KmInfoList.treeShow();
			},
			moreQuery: function() {
				$('#more-query-content').toggle(200);
			},
			searchDir: function() {
				var searchCode = $('#searchForm input[name="treeDirName"]').val();
				$('#el').removeAttr('data-mars', 'KmInfoDao.list')
				$("#searchForm").render({
					data: {
						treeDirName: searchCode
					},
					success: function() {
						$('#el').attr('data-mars', 'KmInfoDao.list')
					}
				});
			},
			//ztree上的点击事件
			zTreeOnClick: function(event, treeId, treeNode) {
				if (treeNode != null && treeNode.id) {
					$('#searchForm').find('[name="dirId"]').val(treeNode.id);
					KmInfoList.query();
				}
			},
			//获取当前操作的节点
			getClickNode: function() {
				var clickNode = $('#rMenu').data('clickNode');
				return clickNode != null ? clickNode : {};
			},
			//获取checkbox中的记录
			getCheckedData: function() {
				var checkedData = $('#el input:checkbox:checked');
				var ids = [];
				if (checkedData != null && checkedData.length > 0) {
					for (var i = 0; i < checkedData.length; i++) {
						ids.push(checkedData[i].value);
					}
				}
				return ids;
			},
			//反馈
			feedback: function(infoId, itemId) {
				var selectedNode = $.fn.zTree.getZTreeObj('ztree').getSelectedNodes()[0];
				var dirId = selectedNode == null ? '' : selectedNode.id;
				var keyCode = $('#searchForm').find('[name="keyCode"]').val();
				var data = {
					dirId: dirId,
					keyCode: keyCode,
					infoId: infoId,
					itemId: itemId
				};

				if ('${feedbackType}' == '1') {
					popup.layerShow({
						moveOut: true,
						type: 1,
						title: getI18nValue('反馈'),
						offset: '20px',
						area: ['600px', '365px']
					}, '${ctxPath}/pages/km/feedbackSimplify/km-feedback-info-edit.jsp', data);
				} else {
					popup.layerShow({
						moveOut: true,
						type: 1,
						title: getI18nValue('反馈'),
						offset: '20px',
						area: ['600px', '365px']
					}, '${ctxPath}/pages/km/feedback/km-feedback-info-edit.jsp', data);
				}
			},
			//我的收藏
			myFavor: function() {
				popup.openTab({
					url: "${ctxPath}/pages/km/query/userFavor/km-user-favor-list.jsp",
					title: getI18nValue('我的收藏'),
					reload: true,
					data: {}
				});
			},
			//添加到我的收藏
			addToMyFavor: function() {
				var ids = this.getCheckedData();
				if (ids != null && ids.length > 0) {
					layer.confirm(getI18nValue('是否确定添加到我的收藏？'), {
						icon: 3,
						title: getI18nValue('添加到我的收藏提示'),
						offset: '20px',
						btn: [getI18nValue('确定'), getI18nValue('关闭')]
					}, function(index) {
						layer.close(index);
						ajax.remoteCall("${ctxPath}/servlet/kmUserFavor?action=batchAdd", {
							'infoIds': ids
						}, function(result) {
							if (result.state == 1) {
								layer.msg(result.msg, {
									icon: 1,
									time: 1200,
									offset: '40px'
								});
								KmInfoList.query();
							} else {
								layer.alert(result.msg, {
									icon: 5
								});
							}
						});
					});
				} else {
					layer.msg(getI18nValue("请选择需要添加收藏的记录！"));
					return;
				}
			},
			//导出最新知识点
			exportNewInfo: function() {
				location.href = '${ctxPath}/servlet/export?action=exportNewInfo';
			},
			exportSelectedInfo: function() {
				var ids = this.getCheckedData();
				if (ids != null && ids.length > 0) {
					location.href = '${ctxPath}/servlet/export?action=exportSelectedInfo&infoIds=' + ids.join(',');
				} else {
					layer.msg(getI18nValue("请选择需要导出的知识点！"));
				}
			},
			//搜索关键词排行
			infoSearchLog: function() {
				popup.layerShow({
					moveOut: true,
					type: 2,
					title: getI18nValue('搜索关键词排行'),
					offset: '20px',
					area: ['835px', '550px'],
					maxmin: true,
					url: '${ctxPath}/pages/km/query/km-info-search-log-list.jsp'
				});
			},
			//历史访问记录
			historyVisit: function() {
				popup.layerShow({
					moveOut: true,
					type: 2,
					title: getI18nValue('历史访问记录(最近20条)'),
					offset: '20px',
					area: ['700px', '550px'],
					maxmin: true,
					url: '${ctxPath}/pages/km/info/history/km-info-history-visit-list.jsp'
				});
			},
			//我的反馈记录
			myFeedbackList: function() {
				popup.layerShow({
					moveOut: true,
					type: 2,
					title: getI18nValue('我的反馈记录'),
					offset: '20px',
					area: ['900px', '650px']
				}, '${ctxPath}/pages/km/feedback/km-feedback-result-list.jsp', {});
			},
			//更多最新信息
			moreNewInfo: function() {
				popup.openTab({
					url: "${ctxPath}/pages/km/query/km-info-new-list.jsp",
					title: getI18nValue('最新知识点'),
					reload: true,
					data: {}
				});
			},
			//更多排行信息
			moreRankInfo: function() {
				popup.openTab({
					url: "${ctxPath}/pages/km/analyze/km-info-visit-count-list.jsp",
					title: getI18nValue('点击排行统计'),
					reload: true,
					data: {}
				});
			},
			//初始化事件
			initEvent: function() {
				//目录搜索input的enter事件
				$('#searchForm input[name="treeDirName"]').keydown(function(e) {
					if (e.keyCode == 13) {
						KmInfoList.searchDir();
					}
				});

				//回车搜索事件
				$(
					'#searchForm [name="keyCode"],#searchForm [name="infoTopic"],#searchForm [name="searchCode"],#searchForm [name="topicCode"]'
				).keydown(function(e) {
					if (e.keyCode == 13) {
						KmInfoList.query();
					}
				});

				$('body').click(function() {
					$('.km-keyCode-list').hide();
				})
			},
			init: function() {
				this.initEvent();
				requreLib.setplugs('ztree,gm,slimscroll', function() {
					$('#searchForm').render({
						success: function(results) {
							$('#el').attr('data-mars', 'KmInfoDao.list');
							$('#searchForm .paginate').hide();
							var treeObj = $.fn.zTree.getZTreeObj('ztree')
							var nodes = treeObj.getNodes();
							if ('${param.keyCode}') { //有传入查询条件时按传入的条件查询
								KmInfoList.query();
							} else if (nodes[0] && nodes[0].children && nodes[0].children[0]) { //没有则按左侧第一个子节点目录查询
								treeObj.selectNode(nodes[0]);
								treeObj.setting.callback.onClick(null, 'ztree', nodes[0]);
							}
							$('#ztree').slimScroll({
								height: $(window).height() - 120,
								color: '#ddd'
							});
						}
					});
					$('#rightForm').render({success: function(results) {
						result = results['KmInfoDao.newList'];
						if (result.state == '0') {
							$('#new-el').html('<div style="margin:10px;font-size:14px;">' + result.msg + '</div>');
						} else {
							if (result.data.length == 0) {
								$('#new-el').html(
									'<div style="font-size:16px;text-align:center;color:#919699;height:180px;line-height:180px;"><i class="fa fa-exclamation-circle">&nbsp;</i>' +
									getI18nValue('暂无最新知识点') + '</div>');
							}
						}

						result = results['KmInfoDao.vistCountList'];
						if (result) {
							if (result.state == '0') {
								$('#rank-el').html('<tr><td colspan="3"><div style="margin:10px;font-size:15px;">' + result.msg +
									'</td></tr>');
							} else {
								if (result.data.length == 0) {
									$('#rank-el').html(
										'<tr><td colspan="3"><div style="margin:10px;font-size:16px;text-align:center;"><i class="fa fa-exclamation-circle">&nbsp;</i>' +
										getI18nValue('暂无排行信息') + '</td></tr>');
								}
							}
						}
						
						result = results['KmInfoDao.clickCountList'];
						if(result) {
							if(result.state == '1') {
								if(result.data && result.data.length > 0) {
									var rankTpl = $.templates('#rank-tpl');
									var rankHtml = rankTpl.render({
										list: result.data
									});
									$("#rank-el").html(rankHtml);
								} else {
									$("#rank-el").html('<tr><td class="text-center nodata" colspan="0">' + getI18nValue('暂无数据') + '</td></tr>');
								}
							}
						}
					}});
				});
			}
		};

		if (typeof(top.CallControl.voiceHelper) != 'undefined') {
			top.CallControl.voiceHelper.kmsCallback = doQuery;
		}

		function doQuery(val) {
			$('input[name="keyCode"]').val(val);
			KmInfoList.query();
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>
