<%@page import="java.lang.reflect.Method"%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%
//避免commonext.jar未更新缺少getJSScript()方法导致jsp编译失败
		String script = "";
		try{
			Class<?> c = Class.forName("com.yq.busi.common.base.CEConstants");
			Method getScript = c.getDeclaredMethod("getJSScript");
			script = (String)getScript.invoke(null, null);
		}catch(Exception e){}
%>
<!DOCTYPE html>
<html lang="zh-CN" class="no-js">
    <head>
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="renderer" content="webkit"> 
        <link rel="icon" type="image/x-icon" href="favicon.ico">
         <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport">
        <meta content="IE=EmulateIE8" http-equiv="X-UA-Compatible">
        <meta content="yes" name="apple-mobile-web-app-capable">
        <meta content="black" name="apple-mobile-web-app-status-bar-style">
        <meta name="robots" content="index,follow">
        <meta http-equiv="pragma" content="no-cache">
 		<meta http-equiv="cache-control" content="no-cache">
	 	<meta http-equiv="expires" content="0">   
		<link href="/easitline-static/lib/bootstrap/css/bootstrap.min.css" rel="stylesheet">
		<link href="/easitline-static/lib/font-awesome/css/font-awesome.min.css" rel="stylesheet">
		<link href="/easitline-static/css/easitline.ui.css?v=20180129" rel="stylesheet">
		<link href="/easitline-static/lib/check/awesome-bootstrap-checkbox.css" rel="stylesheet">
		<link href="/easitline-static/lib/layui/css/layui.css" rel="stylesheet">
        <script type="text/javascript" src="/easitline-static/js/jquery.min.js"></script> 
           <%=script %>
        <style type="text/css">
       	/* .gray-bg, .bg-muted {
     	  	background-color: white !important;
		} */
		*::-webkit-scrollbar {
		     width: 10px;
		     height: 10px;
		     background-color: rgba(255, 255, 255, 0);
		   }
		   
		   *::-webkit-scrollbar-track {
		     border-radius: 10px;
		     background-color: rgba(230, 230, 230, 0.05);
		   }
		   
		   *:hover::-webkit-scrollbar-track {
		     background-color: rgba(230, 230, 230, 0.5);
		   }
		   
		   *::-webkit-scrollbar-thumb {
		     height: 20px;
		     border-radius: 10px;
		     -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0);
		     background-color: rgba(216, 216, 216, 0.4);
		     transition: background-color 1s;
		   }
		   
		   *:hover::-webkit-scrollbar-thumb {
		     background-color: rgba(216, 216, 216, 1);
		   }
		   
		   *::-webkit-scrollbar-thumb:hover {
		     background-color: rgba(190, 190, 190, 1);
		   }
		   
		   *::-webkit-scrollbar-thumb:active {
		     background-color: rgba(160, 160, 160, 1);
		   }
				
        </style>
        <EasyTag:block name="head"></EasyTag:block>
    </head>
<body class="gray-bg">
		<div class="container-fluid">
	        <EasyTag:block name="content"></EasyTag:block>
		</div>
		<script type="text/javascript" src="/cc-notes/static/js/my_i18n.js"></script>
		<script type="text/javascript" src="/cc-base/static/js/i18n.js"></script>
		<script type="text/javascript" src="/easitline-static/js/xss.min.js"></script>
		<script type="text/javascript" src="/easitline-static/lib/bootstrap/js/bootstrap.min.js"></script>
		<script type="text/javascript" src="/easitline-static/js/jsrender.min.js"></script>
		<script type="text/javascript" src="/easitline-static/lib/layer/layer.js"></script>
		<script type="text/javascript" src="/easitline-static/js/easitline.core-2.0.0.js?v=20180129"></script>
		<script type="text/javascript" src="/easitline-static/js/requreLib.js"></script>
		<script type="text/javascript" src="/cc-base/static/js/yq/extends.js"></script> 
		<script type="text/javascript" src="/easitline-static/lib/layui/layui.js"></script>
		<script type="text/javascript" src="/easitline-static/js/layTable.js?v=20181211"></script> 	
		<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
		<script type="text/javascript" src="/cc-notes/static/js/time.js"></script>
   		<EasyTag:block name="script"></EasyTag:block>
</body>
</html>