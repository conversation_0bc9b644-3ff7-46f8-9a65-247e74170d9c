<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="班组分配业务员"></title>
</EasyTag:override>

<EasyTag:override name="content">
	<form class="form-inline" id="searchUserForm" data-toggle="render" data-pk="${param.skillGroupId }">
		<input type="hidden" name="groupId" id="groupId" value="${param.skillGroupId }" />
		<input type="hidden" name="workGroupId" value="${param.workGroupId }">
		<div class="ibox">
			<div class="ibox-title clearfix">
				<div class="form-group">
					<div class="input-group input-group-sm">
						<span class="input-group-addon" i18n-content="姓名"></span>
						<input type="text" name="agentName" autocomplete="off" class="form-control input-sm" onkeydown='if(event.keyCode==13){return false;}' style="width: 100px">
					</div>
					<div class="input-group input-group-sm">
						<span class="input-group-addon" i18n-content="登录账号"></span>
						<input type="text" name="userAcct" autocomplete="off" class="form-control input-sm" onkeydown='if(event.keyCode==13){return false;}' style="width: 100px">
					</div>
					<div class="input-group input-group-sm">
						<span class="input-group-addon" i18n-content="工号"></span>
						<input type="text" name="agentPhone" autocomplete="off" class="form-control input-sm" onkeydown='if(event.keyCode==13){return false;}' style="width: 100px">
					</div>
					<div class="input-group input-group-sm">
						<button type="button" class="btn btn-sm btn-default" onclick="SkillGroup.loadData()">
							<span class="glyphicon glyphicon-search"></span>
							<span i18n-content="查询"></span>
						</button>
					</div>
				</div>
			</div>
			<div class="ibox-content">
				<table id="main" lay-filter="test"></table>
			</div>
		</div>
	</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
	requreLib.setplugs("layui", function() {
		SkillGroup.loadData();
	});

	var SkillGroup = {
		loadData: function() {
			var flag=false;
			if("voice"!='${param.skillType}'){
				flag=true;
			}
			$("#searchUserForm").initTableEx({
				mars : 'skillGroup.groupUserList',
				id : 'main',
				totalRow : false, //显示汇总行
				limit : 15,
				page : true,
				height : 'full-120',
				toolbar : false,
				cols : [[
					{
						width : 80,
						field : 'USER_ID',
						title : '选择',
						type : 'checkbox'
					},
					{
						width : 80,
						title : '序号',
						type : 'numbers'
					},
					{
						minWidth : 100,
						field : 'AGENT_NAME',
						title : '姓名',
						templet : function(row) {
							return '<a  href="javascript:void(0)" onclick="SkillGroup.editUser(\''
									+ row.USER_ID
									+ '\',\''
									+ row.USER_ACCT
									+ '\',\''
									+ row.AGENT_PHONE
									+ '\',\''
									+ row.SKILL_GROUP_NAME
									+ '\')">'
									+ row.AGENT_NAME
									+ '</a>';
						}
					},
					{
						minWidth : 120,
						field : 'USER_ACCT',
						title : '登录帐号',
						sort : true
					},
					{
						field : 'AGENT_PHONE',
						title : '工号',
						sort : true
					},
					{
						minWidth : 90,
						field : 'WORK_GROUP_NAME',
						title : '排队组'
					},
					{
						minWidth : 90,
						field : 'PREFIX_NUM',
						title : '外显号码'
					},
					{
						width : 160,
						field : 'USER_ID',
						title : '外呼权限',
						hide : flag,
						templet : function(row) {
							var html = '';
							var label = '';
							var labelend = '';
							var inboundHtml = '';
							if (row.INBOUND == 1) {
								inboundHtml = '<input userid="'+row.USER_ID+'" type="checkbox" name="INBOUND" lay-skin="primary" value="1" userAcc="'+row.USER_ACCT+'" checked ><span>呼入</span>';
							} else {
								inboundHtml = '<input userid="'+row.USER_ID+'" type="checkbox" name="INBOUND" lay-skin="primary"  value="1" userAcc="'+row.USER_ACCT+'" ><span>呼入</span>';
							}
							html = label + inboundHtml + labelend;
							var outboundHtml = '';
							if (row.OUTBOUND == 1) {
								outboundHtml = '<input userid="'+row.USER_ID+'" type="checkbox" name="OUTBOUND" lay-skin="primary" value="1" userAcc="'+row.USER_ACCT+'" checked ><span>呼出</span>';
							} else {
								outboundHtml = '<input userid="'+row.USER_ID+'" type="checkbox" name="OUTBOUND" lay-skin="primary" value="1"  userAcc="'+row.USER_ACCT+'" ><span>呼出</span>';
							}
							html = html + label + outboundHtml + labelend;
							return html;
						}
					},
					{
						width : 80,
						field : 'USER_STATE',
						title : '状态',
						templet : function(row) {
							if (row.CENTER_USER_STATE == 1) {
								return ' <span href="javascript:void(0)" class="layui-btn layui-btn-xs layui-btn-danger"> 暂停 </span>'
							} else {
								return ' <span href="javascript:void(0)" class="layui-btn layui-btn-xs" > ' + userStatusFn(row.USER_STATE) +' </span>'
							}
						}
					},
					{
						minWidth : 160,
						field : 'GROUP_LIST',
						title : '所属技能组'
					},
					{
						minWidth : 90,
						field : 'IDX_ORDER',
						align : 'center',
						title : '优先级',
						sort : true,
						templet : function(row) {
							var temp = '<a href="javascript:void(0)" onclick="SkillGroup.editIdxOrder(\'' + row.USER_ID + '\',\'' + row.USER_ACCT + '\',\'' + row.SKILL_GROUP_ID + '\',\'' + row.BUSI_ORDER_ID + '\',\'' + row.IDX_ORDER + '\')">' + (row.IDX_ORDER?row.IDX_ORDER:0) + '</a>'
							return temp;
						}
					},{
						minWidth : 90,
						align : 'center',
						title : '操作',
						fixed:'right',
						templet:function(row){
							var temp = '<a class="layui-btn layui-btn-danger  layui-btn-xs" onclick="SkillGroup.delWorkUser(\'' + row.USER_ID + '\',\'' + row.WORK_GROUP_ID + '\',\'' + row.SKILL_GROUP_ID + '\')">'+getI18nValue('删除')+'</a>'
							return temp;
						}
					}
				]],
				rowEvent : true,
				done : function() {
					$(".userClass").show();
					$(".workGroupClass").hide();
					layui.form.on('checkbox()', function(obj) {
						var elemTemp = obj.elem;
						if (elemTemp) {
							var userAcc = elemTemp.attributes[5];
							var usreid = elemTemp.attributes[0];
							SkillGroup.OnChangeBound(usreid.nodeValue,userAcc.nodeValue,elemTemp.name,elemTemp.checked);
						}
					});
				}
			});

		},
		editIdxOrder: function(userId,userAcc,skillGroupId,busiOrderId,idxOrder){
	  		var skillGroupName = $("#skillName").val();
	  		var param = {skillGroupId:skillGroupId,busiOrderId:busiOrderId,userId:userId,idxOrder:idxOrder,userAcc:userAcc,skillGroupName:skillGroupName,mgrType: "1"};
		    popup.layerShow({type:1,title:'设置优先级（'+userAcc+'）',offset:'20px',area:['400px','180px']},"${ctxPath}/pages/skillGroup/skillGroup-user-edit.jsp",param);
		},
		OnChangeBound: function(userId,userAcc,type,status){
			ajax.remoteCall("${ctxPath}/servlet/userMgr?action=updateBound",{userId:userId,type:type,status:status,userAcc:userAcc},function(result) { 
				if(result.state != 1){
					layer.alert(result.msg);
				}
			});
		}
	}
	
	var userStatusFn = function(val) {
		if(val==0){
			return getI18nValue("启用");
		}else if(val==1){
			return "<span class='label label-warning'>"+getI18nValue("暂停")+"</span>";
		}
	}
	
	SkillGroup.delWorkUser = function(userId,workGroupId,skillGroupId){
		var data = {
				USER_ID:userId,
				SKILL_GROUP_ID:skillGroupId,
				WORK_GROUP_ID:workGroupId
			}
		layer.confirm(getI18nValue('确定删除吗？'),{icon:0,btn:[getI18nValue('确定'),getI18nValue('取消')]},function(index){
			layer.close(index);
			ajax.remoteCall("${ctxPath}/servlet/skillGroup?action=delWorkUser", data, function(result) {
		  		if(result.state == 1){
				    layer.msg(getI18nValue(result.msg),{icon: 1,time:1200,offset:'40px'},function(){
				    	SkillGroup.loadData()
				    });
				}else{
					layer.alert(result.msg,{icon: 5});
				}
	  		});
		});
		
		

	}
	
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>