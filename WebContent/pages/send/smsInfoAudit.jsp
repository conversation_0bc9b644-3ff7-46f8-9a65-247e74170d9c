<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="短信发送审核"></title>
	<style type="text/css">
	    th{
	        text-align:center;/** 设置水平方向居中 */
	        vertical-align:middle/** 设置垂直方向居中 */
	    }
	    a:link{ color:#00adff;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<input type="hidden" id="sendTimeBeginSet">
	<input type="hidden" id="sendTimeEndSet">
	<form  name="searchForm" class="form-inline" id="searchForm" >
	<input type="hidden" id="STATUS" name="STATUS" value="4">
   		<div class="ibox" >
  			<div class="ibox-title clearfix">
    			<div class="form-group">
      				<h5 i18n-content="短信发送审核"></h5>
				</div>
				<hr style="margin: 5px -15px">
				<div class="form-group" id="divId">
				    <div class="input-group ">
			        	<span class="input-group-addon" i18n-content="渠道"></span>	
						<select class="form-control input-sm" name="channel" style="width:130px;" data-mars="smsInfo.channelList">
							<option value="" i18n-content="请选择"></option>
						</select>
				 	</div>
				  	<div class="input-group">
			 			<span class="input-group-addon" i18n-content="创建时间"></span>
			        	<input type="text" class="form-control input-sm" id="sendDateBegin" name="sendDateBegin"  data-mars="common.getLastMonthTime" data-mars-top="true" autocomplete="off" style="height:30px;width:142px" > 
	                	<span class="input-group-addon">~</span>
	                	<input type="text" class="form-control input-sm" id="sendDateEnd" name="sendDateEnd"  data-mars="common.getTodayTime" data-mars-top="true" autocomplete="off" style="height:30px;width:142px" > 
	                	<span class="input-group-addon">-</span>
	                	<select class="form-control input-sm" name="dateRange" onchange="smsSendRecord.changeDate($(this))">
	                   		<option value="" i18n-content="请选择"></option>
							<option value="today" i18n-content="今天"></option>
							<option value="yesterday" i18n-content="昨天"></option>
							<option value="thisWeek" i18n-content="本周"></option>
							<option value="RecentlyOneMonth" i18n-content="近一个月"></option>
							<option value="RecentlyThreeMonth" i18n-content="近三个月"></option>
						</select>
                	</div>
				   	<div class="input-group ">
						<button type="button" class="btn btn-sm btn-default" onclick="smsSendRecord.searchData('1')"><span class="glyphicon glyphicon-search"></span><span i18n-content="查询"></span></button>
					</div>
				 	<div class="input-group ">
						<button type="button" class="btn btn-sm btn-default" onclick="smsSendRecord.reset()"><span class="glyphicon glyphicon-repeat"></span><span i18n-content="重置"></span> </button>
					</div>
					<div class="input-group input-group-sm pull-right btn-group">
						<button type="button" class="btn btn-sm btn-info  btn-outline pull-right" onclick="smsSendRecord.editData()" i18n-content="审核"></button>
					</div>
				</div>
     	    </div>  
           	<div class="ibox-content table-responsive">
   	     		<table id="dataList"></table>
           	</div> 
		</div>
	</form>
</EasyTag:override>

<EasyTag:override name="script">
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript" src="/cc-sms/static/js/time.js"></script>
	<script type="text/javascript">
	jQuery.namespace("smsSendRecord");
	$(function() {
		$("#searchForm").render({success:function(){
			 requreLib.setplugs('layui',function(){
				//加载时间控件
				 layui.use('laydate', function(){
	   			  var laydate = layui.laydate;
	   			laydate.render({elem:'#sendDateBegin',type:'datetime',lang:getDateLang()});
	   			laydate.render({elem:'#sendDateEnd',type:'datetime',lang:getDateLang()});
	   		}) 
	       	$("#sendTimeBeginSet").val($("#sendDateBegin").val());
		    $("#sendTimeEndSet").val($("#sendDateEnd").val()); 
		    smsSendRecord.loadData();
			 }); 
		}});
	});
	//初始化加载数据
	smsSendRecord.loadData=function(){
		$("#searchForm").initTableEx({
			mars:"smsInfo.smsInfoList",
			id:"dataList",
			limit:'15',
			height: 'full-165',
			limits:[15,25,50,100,200],
			cols: [
				[
					{width:60,field:'ID', title: getI18nValue('选择'), type:'checkbox'},
					{field:'CONTENT',title:getI18nValue('短信内容'),align:'center',width:200,templet:function(row){
						var title = '<a href="javascript:void(0)" onclick="smsSendRecord.details('+row.ID+')" >'+row.CONTENT+'</a>';
						return title;
					}},
					{minWidth:120,field:'STATUS',title:getI18nValue('状态'),align:'center',templet:function(row){
						return getDictTextByCode('SMS_INFO_STATUS',row.STATUS);
					}},
					{minWidth:120,field:'COUNT',title:getI18nValue('收信人数量'),align:'center',templet:function(row){
						if(row.COUNT ==''){
							return '0'
						}else{
							return row.COUNT;
						}
					}},
					{field:'CREATE_TIME',title:getI18nValue('创建时间'),align:'center',width:180},
					{field:'SEND_TIME',title:getI18nValue('发送时间'),align:'center',width:180},
					{minWidth:120,field:'CREATE_ACC',title:getI18nValue('创建人账号'),align:'center'},
					{minWidth:120,field:'CREATE_USER_NAME',title:getI18nValue('创建人名称'),align:'center'},
					{minWidth:120,field:'NAME',title:getI18nValue('渠道名称'),align:'center'},
					{minWidth:120,field:'TYPE',title:getI18nValue('短信类型'),align:'center',templet:function(row){
						return getDictTextByCode('SMS_INFO_TYPE',row.TYPE)
					}},
					{minWidth:120,field:'SOURCE',title:getI18nValue('短信来源'),align:'center',templet:function(row){
						return getDictTextByCode('SMS_INFO_SOURCE',row.SOURCE)
					}},
					{minWidth:120,field:'NEED_SMS_RECEIPT',title:getI18nValue('是否需要回执'),align:'center',templet:function(row){
						return getDictTextByCode('SMS_NEED_RECEIPT',row.NEED_SMS_RECEIPT);
					}},
					 {field:'HANDLE', title: getI18nValue('操作'), width:150, align:'center',fixed: 'right',templet:function(row){
				        var temp ='';
				        temp +=' <span href="javascript:void(0)" class="layui-btn layui-btn-xs" onclick="smsSendRecord.details(\''+row.ID+'\')">'+getI18nValue("详情")+'</span> ';
				        temp +=' <span href="javascript:void(0)" class="layui-btn layui-btn-xs" onclick="smsSendRecord.onceEditData(\''+row.ID+'\')">'+getI18nValue("审核")+'</span>';	 	
				        return temp;
				         }}
				]
			]
		});
	}
	smsSendRecord.searchData=function(flag){
		var t1=$("#startDate").val();
		var t2=$("#endDate").val();
		if(t1!=''&&t2!=''){
			if(t1>t2){
				layer.alert(getI18nValue("结束时间不能大于开始时间"),{icon: 2});				
				return false;
			}
		}
		if(flag=="1"){
			$("#searchForm").queryData({id:'dataList',page:{curr:1}});
		}else{
			$("#searchForm").queryData({id:'dataList'});
		}
	};
	//详情
	smsSendRecord.details=function(id){
		popup.openTab({
			title : getI18nValue('短信发送审核详情'),
		    url:"${ctxPath}/pages/send/smsSendAudit.jsp",
		    data:{"id":id}
		});
	}
	
	//审核
	smsSendRecord.editData = function(){
		var files = '';
		//var filesCid = '';
		var checkStatus = table.checkStatus('dataList');
		if(checkStatus.data.length==0){
			layer.msg(getI18nValue("请选择需要审核的短信！"));
			return ;
		}else{
			for(j = 0; j < checkStatus.data.length; j++) {
				files+=','+checkStatus.data[j].ID;
				//filesCid+=','+checkStatus.data[j].CID;
			}
		}
		if(files.length > 1){
			files = files.substring(1);
		}
		/* if(filesCid.length > 1){
			filesCid = filesCid.substring(1);
		} */
	    popup.layerShow({type:1,title:getI18nValue('审核'),offset:'20px',area:['400px','300px']},"${ctxPath}/pages/send/smsSendAuditEdit.jsp",{idfiles:files});//filesCid:filesCid
   	}
	
	//单挑审核
	smsSendRecord.onceEditData = function(ID){
	    popup.layerShow({type:1,title:getI18nValue('审核'),offset:'20px',area:['400px','300px']},"${ctxPath}/pages/send/smsSendAuditEdit.jsp",{idfiles:ID});
	}
	
	//重置
	smsSendRecord.reset=function(){
		$("#divId input").val("");
    	$("#divId select").val("");
    	$("#sendDateEnd").val($("#sendTimeEndSet").val());
		$("#sendDateBegin").val($("#sendTimeBeginSet").val());
	}
	//时间段选择
	smsSendRecord.changeDate = function(p){
		var dateRange = p.val();
     	if(dateRange == "today") {
     		$("#sendDateBegin").val(getTodayStartTime());
     		$("#sendDateEnd").val(getTodayEndTime());
     	}else if(dateRange == "yesterday") {
  			$("#sendDateBegin").val(getYesterDayStartTime());
 			$("#sendDateEnd").val(getYesterDayEndTime());
  		}else if(dateRange == "thisWeek") {
     		$("#sendDateBegin").val(getThisWeekStartTime());
     		$("#sendDateEnd").val(getThisWeekEndTime());
     	}else if(dateRange == "RecentlyOneMonth") {
     		$("#sendDateBegin").val(getRecentlyOneMonthStartTime());
     		$("#sendDateEnd").val(getTodayEndTime());
     	}else if(dateRange == "RecentlyThreeMonth") {
  			$("#sendDateBegin").val(getRecentlyThreeMonthStartTime());
 			$("#sendDateEnd").val(getTodayEndTime());
  		}
	}
	function get_nowDateTime(){
		var date = new Date();
		var day = date.getDate() < 10 ? "0" + date.getDate():date.getDate();
		var month = (date.getMonth() + 1) > 9 ? (date.getMonth() + 1) : "0"	+ (date.getMonth() + 1);
		var hour = date.getHours() <10? "0"+ date.getHours():date.getHours();
		var minute = date.getMinutes() <10 ? "0"+date.getMinutes():date.getMinutes();
		var second = date.getSeconds() <10 ? "0"+ date.getSeconds():date.getSeconds();
		return date.getFullYear() + "-" + month + "-"  + day ;
	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>