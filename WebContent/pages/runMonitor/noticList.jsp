<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title><span i18n-content="系统通知" ></span></title>
	<style type="text/css">
		a:link{ color:#00adff;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="NoticList_Form" name="NoticList_Form" class="form-inline">
       	<div class="ibox">
       		<div class="ibox-title clearfix" id="divId">
       		    <h5><span class="" i18n-content="系统通知"></span></h5>
       		    <div class="input-group input-group-sm pull-right btn-group">
       		     	<!-- 
                    <button type="button" class="btn btn-sm btn-success btn-outline"  
                    	onclick="list.exportLoginLog()"><i class="glyphicon glyphicon-export"></i>
                      	导 出
                    </button>
                    -->
                </div>
       		    <hr style="margin: 5px -15px">
       		    <div class="form-group">
					<div class="input-group ">
						<span class="input-group-addon" i18n-content="接收人类型"></span> 
						<select name="RECEIVER_TYPE" id="RECEIVER_TYPE" class="form-control input-sm" size="1" style="width: 200px">
             		    	<option value="" i18n-content="请选择"></option>
             		    	<option value="01" i18n-content="个人"></option>
             		    	<option value="02" i18n-content="部门"></option>
             		    	<option value="03" i18n-content="所有人"></option>
             		    </select>
				    </div>
				  	<div class="input-group input-group-sm">
				 		<span class="input-group-addon" i18n-content="是否已读"></span> 
				 		<select class="form-control input-sm" name="IS_READ" style="width:100px"  data-mars="common.getDict(SF_YN)">
							<option value="" i18n-content="请选择"></option>
						</select>
			    	</div>
			    	<div class="input-group input-group-sm">
				 		<span class="input-group-addon" i18n-content="创建时间"></span> 
						<input type="text" name="startDate" id="startDate" class="form-control input-sm layui-input"
							style="width:140px" autocomplete="off">
						 <span class="input-group-addon">-</span>	
						 <input type="text" name="endDate" id="endDate" class="form-control input-sm layui-input" 
						 	style="width:140px" autocomplete="off"> 
			    	</div>
			 		<div class="input-group ">
					 	<button type="button" class="btn btn-sm btn-default" onclick="NoticList.searchList(1)"><span class="glyphicon glyphicon-search" i18n-content="查询"></span></button>
			  		</div>
			  		<div class="input-group ">
						<button type="button" class="btn btn-sm btn-default" onclick="NoticList.reset()"><span class="glyphicon glyphicon-repeat" i18n-content="重置"></span></button>
					</div> 	
	      		</div>
      		</div> 
         	<div class="ibox-content layui-row" style="vertical-align:top;width:100%;">
        		<div class="form-group layui-col-xs12 layui-col-sm12 layui-col-md8" style="vertical-align:top;">
        			<div class="input-group" style="padding-left: 15px">
						<span i18n-content="通知列表"></span>
					</div> 	
        			<div class="ibox-content">
        				<table id="NoticList_List"></table>
         			</div> 
         		</div>
         		<div class="form-group layui-col-xs12 layui-col-sm12 layui-col-md4" style="vertical-align:top;">
        			<input name="NOTICE_ID" id="NOTICE_ID" type="hidden"/>
        			<div class="input-group" style="padding-left: 15px">
						<span i18n-content="接收列表"></span>
					</div>
        			<div class="ibox-content">
        				<table id="NoticUserList_List"></table>
         			</div> 
        		</div>
         	</div> 
		</div>                
	</form>
</EasyTag:override>

<EasyTag:override name="script">
<script type="text/javascript">
	jQuery.namespace("NoticList");
	requreLib.setplugs("wdate");
	
	//查询
	NoticList.searchList = function (flag){
		if(flag == '1'){
			$("#NoticList_Form").queryData({id:'NoticList_List', page: {curr:1 }});
			$("#NOTICE_ID").val("");
			$("#NoticList_Form").queryData({id:'NoticUserList_List', page: {curr:1 }});
		}else{
			$("#NoticList_Form").queryData({id:'NoticList_List'});
			$("#NOTICE_ID").val("");
			$("#NoticList_Form").queryData({id:'NoticUserList_List'});
		}
	}
	
	//重置
	NoticList.reset=function(){
		$("#divId select").val("").trigger("change");
    	$("#divId input").val("");
	};
	
	//接口调用数据加载
	NoticList.loadData = function(){
		$("#NoticList_Form").initTableEx({
			mars:'notice.list',
		 	id: 'NoticList_List',
		 	height: 'full-160',
		 	cellMinWidth: 80,
		 	limit:15,
		 	limits:[15,25,50,100,200],
			cols: [[
			    {width:60,type:'numbers', title: getI18nValue('序号')}
	         	,{width:100,field:'USER_ACC', title: getI18nValue('发送坐席')}
	         	,{width:130,field:'CREATE_TIME', title: getI18nValue('创建时间')}
	         	,{width:130,field:'TYPE', title: getI18nValue('通知类型')}
	         	,{width:100,field:'TITLE', title: getI18nValue('标题')}
	         	,{width:120,field:'CONTENT', title: getI18nValue('内容')}
	         	,{field:'BAKUP', title: getI18nValue('备注')}
	         	,{width:130,field:'EXPIRED_TIME', title: getI18nValue('过期时间')}
	         	]
			],
			rowEvent: function (row, event){
				$("#NOTICE_ID").val(row.ID);
				$("#NoticList_Form").queryData({id:'NoticUserList_List'});
			}
		});
	}
	//初始化加载数据
	NoticList.loadData2 = function(){
		$("#NoticList_Form").initTableEx({
			 mars:'notice.getNoticeUserlist',
			 id:'NoticUserList_List',
			 height: 'full-160',
			 limit:15,
			 limits:[15,25,50,100,200],
			 cols: [
				[
				 {width:60,field:'ID', title: getI18nValue('序号'), type:'numbers'}
		         ,{field:'RECEIVER_TYPE', title: getI18nValue('接收人类型'), templet:function(row){
		        	 if(row.RECEIVER_TYPE == '01'){
		        		 return getI18nValue("个人");
		        	 }else if (row.RECEIVER_TYPE == '02'){
		        		 return getI18nValue("部门");
		        	 }else if (row.RECEIVER_TYPE == '03'){
		        		 return getI18nValue("所有人");
		        	 }else{
		        		 return "";
		        	 }
		         }}
		         ,{field:'RECEIVER', title: getI18nValue('接收人')}
		       ]
			],
			rowEvent: function (row, event){
			}
		});
	}
	$(function(){
		$("#NoticList_Form").render({success:function (){
			layui.use('laydate', function(){
				var laydate = layui.laydate;
				//日期时间选择器
				laydate.render({
				  elem: '#startDate'
				  ,type: 'datetime'
					  ,lang: getDateLang()
				});
				//日期时间选择器
				laydate.render({
				  elem: '#endDate'
				  ,type: 'datetime'
					  ,lang: getDateLang()
				});
			});
		}});
		NoticList.loadData();
		NoticList.loadData2();
    });
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>