<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="外部接口配置"></title>
	<style type="text/css">
		a:link{ color:#00adff;}
		#dataList tr td{white-space:nowrap;min-width:100px;max-width:300px;text-overflow:ellipsis;overflow:hidden}
		.ibox-content table tr td label+label{margin-left:10px}
		.labelListDiv{height: 33px;line-height: 32px;padding: 0px 10px;font-size: 13px;cursor: pointer;}
		.labelListDiv a{text-decoration: none;}
		.labelListDivActive{
			background-color:#f2f2f2;
			border: 1px solid #eee;
          	border-left-width: 5px;
          	border-radius: 3px;
         	border-left-color: #1b809e;
		}
		.labelListDiv:hover{background-color:#f2f2f2}
		::-webkit-scrollbar {
			width: 8px;
			height: 8px;
			background: transparent;
		}
		::-webkit-scrollbar-track {
			background: transparent;
		}
		::-webkit-scrollbar-thumb {
			border-radius: 8px;
			background-color: #C1C1C1;
		}
		::-webkit-scrollbar-thumb:hover {
			background-color: #A8A8A8;
		}
		.shadow {
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-moz-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
		}
		.btn{
			margin-right: 10px;
		}
		.layui-table-cell {
		    height:auto;
		    overflow:visible;
		    text-overflow:inherit;
		    white-space:normal;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form action="" method="post" name="searchForm" class="form-inline" id="searchForm" onsubmit="return false;" autocomplete="off">
	<input type="hidden" value="" name="TYPE_ID" id="TYPE_ID">
   	<div style="background-color: #fff;width: 16%;float: left;padding-bottom: 10px;" class="shadow">
			<div style="height: 52px;line-height: 52px;padding: 0px 15px;border-bottom: 1px solid #eee;"><span i18n-content="接口类型"></span>
				    <button style="float: right;margin-top: 11px;" type="button" class="btn btn-sm btn-success btn-outline " onclick="workorder.addGroup()" i18n-content="新增"></button>
				</div>
			    <div data-template="labelList-template"  id="labelTableHead" data-mars="outInfDao.getTypeList" data-container="#labelTableHead" style="background-color: #fff;">
			    </div>
			 </div>
			<script id="labelList-template" type="text/x-jsrender">
	 							   {{for  data}}
										<div class="clearfix labelListDiv" onclick="workorder.loadLabelInfo('{{:ID}}','{{:NAME}}',$(this))">
											<div class="pull-left"><a style="color: #000;" href="javascript:void(0)" >{{:NAME}}</a></div>
                                            <div class="pull-left"><a style="color: #000;" href="javascript:void(0)" ></a></div>
											<div width="60px" class="pull-right">
								                <a style="font-size: 14px;margin-left: 10px;" onclick="workorder.delOrderGroup('{{:ID}}')" href="javascript:void(0)" i18n-title = "删除" class="pull-right" i18n-content="删除"></a>
								                <a style="font-size: 14px;" onclick="workorder.editOrderGroup('{{:ID}}')" href="javascript:void(0)" i18n-title = "修改" class="pull-right" i18n-content="修改"> </a> 
											</div>                                         
									    </div>
								    {{/for}}	
			</script>
					<div style="width: 82%;float: left;margin-left: 15px;" class="shadow">
		             	    <div class="ibox-header">
			             		<div class="ibox-title">
									<div class="form-group">
										<h5 i18n-content="接口列表">  <span id="labelMemberTitle"></span></h5>
										<div class="input-group input-group-sm pull-right" >
											<button type="button" class="btn btn-sm btn-success btn-outline" onclick="workorder.addInf()" i18n-content="新增接口"> </button>
											<button type="button" class="btn btn-sm btn-success btn-outline" onclick="workorder.delAll()" i18n-content="批量删除"></button>
										</div>
									</div>
									<hr style="margin: 3px -15px">
									 <div class="form-group" id="agentDiv">
										   <div class="input-group input-group-sm" id="divId">
												<span class="input-group-addon" i18n-content="创建时间"></span> 
												<input type="text" name="startDate" id="startDate" class="form-control input-sm" autocomplete="off"  style=" width: 140px">
												<span class="input-group-addon">-</span> 
												<input type="text" name="endDate" id="endDate" class="form-control input-sm" autocomplete="off"  style=" width: 140px">
												</div>
				   						   <div class="input-group input-group-sm">
						                      <span class="input-group-addon" i18n-content="接口编号"></span>
						                      <input type="text" name="sortNum" class="form-control input-sm" style="width:152px;">
					                       </div>
					                       <div class="input-group input-group-sm">
						                      <span class="input-group-addon" i18n-content="接口名称"></span>
						                      <input type="text" name="infName" class="form-control input-sm" style="width:152px;">
					                       </div>
										   <div class="input-group input-group-sm">
												<button type="button" class="btn btn-sm btn-default" onclick="workorder.searchData('1')"><span class="glyphicon glyphicon-search"></span> <span i18n-content="查询"></span></button>
										   </div>
										   <div class="input-group" id="resetDiv">
												<button type="button" class="btn btn-sm btn-default" onclick="workorder.reset()"><span class="glyphicon glyphicon-repeat"></span> <span i18n-content="重置"></span></button>
										  </div> 
									  </div>
			             	    </div>
			              	    <div class="ibox-content">
				              	    <table id="tree-table" ></table>
								</div>
		              	    </div>
					</div>
	
	</form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	jQuery.namespace("workorder");
	layui.use('laydate', function(){
		  var laydate = layui.laydate;
		  laydate.render({ elem: '#startDate',type: 'datetime',lang:getDateLang() });
		  laydate.render({ elem: '#endDate',type: 'datetime' ,lang:getDateLang()});
	})
	//重置
	workorder.reset=function(){
    	//$("#divId input").val("");
    	$("#agentDiv input").val(""); 
	};
	
	workorder.searchData=function(flag){
		if(flag=='1'){
			$("#searchForm").queryData({id:'tree-table',page:{curr:1}});
		}else{
			$("#searchForm").queryData({id:'tree-table'});
		}
	 }	
		//默认选择第一个
		$(function(){
			$("#searchForm").render({
				success:function(result){
					requreLib.setplugs('slimscroll',function(){
						$('#labelTableHead').slimScroll({  
				              height: document.documentElement.clientHeight-100,
				              color: '#ddd'
				      	});
						$(".labelListDiv").find("a").first().click();
					})
				}
			});  
		})
		//左侧新增分组
		workorder.addGroup=function(){
			popup.layerShow({type:1,title:getI18nValue('新增分类'),offset:'20px',area:['860px','450px']},"${ctxPath}/pages/outInf/outInfTypeEdit.jsp",{});
		}
		//左侧的修改
		workorder.editOrderGroup= function(id) {
			popup.layerShow({type:1,title:getI18nValue('编辑分类'),offset:'20px',area:['860px','450px']},"${ctxPath}/pages/outInf/outInfTypeEdit.jsp",{outInfTypeId:id});
		}
		//左侧的删除
		workorder.delOrderGroup= function(obj) {
			if(obj) {
				layer.confirm(getI18nValue('是否确定删除当前类型?'),{icon: 3, title:getI18nValue('提示'),offset:'20px',btn:[getI18nValue('确定'),getI18nValue('取消')]},  function(index){
					var url = "${ctxPath}/servlet/outInf?action=delRecord";
					ajax.remoteCall(url,{ID:obj},function(result) {
						var state = result.state;
						if(state=='1') {
							layer.msg(result.msg,{icon: 1,time:1200});
							workorder.loadData();
							location.reload();
						} else {
							layer.alert(result.msg,{icon: 5});
							return;
						}
					});
				});
			}
		}
		//右侧新增接口
		workorder.addInf = function(INF_ID){
			var TYPE_ID = $("#TYPE_ID").val();
			popup.layerShow({type:1,title:getI18nValue('新增接口'),offset:'20px',area:['860px','450px']},"${ctxPath}/pages/outInf/outInfEdit.jsp",{OUT_INF_TYPE_ID:TYPE_ID,INF_ID:INF_ID});
		}
		//右侧批量删除
		workorder.delAll = function(){
			var arr = getCheckArr();
			var sortNumArr = getCheckSortNumArr();
			if(!arr || arr.length==0) {
				layer.alert(getI18nValue("请选择要删除的数据！"),{icon: 3, title:getI18nValue('删除'),btn:[getI18nValue('确定')] ,offset:'20px'})
				return ;
			}
			layer.confirm(getI18nValue('是否删除选中接口配置？'),{icon: 3, title:getI18nValue('提示'),offset:'20px',btn:[getI18nValue('确定'),getI18nValue('取消')]},  function(index){
				layer.close(index);
				var data = {ids:arr,sortNums:sortNumArr,outInfTypeId:$("#OUT_INF_TYPE_ID").val()};
				ajax.remoteCall("${ctxPath}/servlet/outInf?action=batchDelInf",data,function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon: 1,offset:'80px',time:2000},function(){
							workorder.loadData();
						});
					}else{
						layer.alert(result.msg);
					}
				});
			});
		}
		//右侧删除（单个）
		workorder.delInf = function(id,sortNum){
			layer.confirm(getI18nValue('确定删除接口配置？'),{icon: 3, title:getI18nValue('提示'),offset:'20px',btn:[getI18nValue('确定'),getI18nValue('取消')]},  function(index){
				layer.close(index);
				var data = {ID:id,sortNum:sortNum};
				ajax.remoteCall("${ctxPath}/servlet/outInf?action=delInf",data,function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon: 1,offset:'80px',time:2000},function(){
							workorder.loadData();
						});
					}else{
						layer.alert(result.msg);
					}
				});
			});
		}
		//点击分组的事件
		workorder.loadLabelInfo = function(labelId,labelName,obj){
			$("#TYPE_ID").val(labelId);
			$("#labelMemberTitle").html("(<font color=red>"+labelName+"</font>)");
			$(obj).addClass("labelListDivActive");
			$(obj).siblings().removeClass("labelListDivActive") 
			workorder.loadData();
		}
		//渲染表格
		workorder.loadData = function(){
			$("#searchForm").initTableEx({
				mars:"outInfDao.infList"
				,id:'tree-table'
				,limit:15
				,limits:[15,25,50,100,200]
			 	,height:'full-180'
				,id:'tree-table'
				,cols: [
					[
                          {type: 'checkbox',width:60},    
						  {width:150,align:'center',field:'SORT_NUM', title: '接口编号',sort:true},
						  {width:150,align:'center',field:'INF_NAME', title: '接口名称',sort:true},
						  {width:100,field:'ENABLE_STATUS', title: getI18nValue('是否启用'),templet:function(row){
					        	 if(row.ENABLE_STATUS=="01"){
										var check='checked=""';
									}
									var html='<input type="checkbox" '+check+' name="open" lay-skin="switch" lay-filter="switchStatuss" lay-text="'+getI18nValue('启用|禁用')+'" data-id="'+row.ID+'" >';
									return html;
					       }},
						  {field:'CONFIG_JSON',minWidth:300,title: getI18nValue('接口JSON信息'),align:'left', templet:function(row){
					        	 //格式化
					        	 var html = "";
					        	 var jo=JSON.parse(row.CONFIG_JSON);
					        	 for(var i=0;i<jo.length;i++){
					        		var name = jo[i].name;
					        		var key = jo[i].key;
					        		var value = jo[i].value;
					        		html += getI18nValue("参数名称：")+name+" "; 
					        		html += getI18nValue("参数KEY：")+key+" "; 
					        		html += getI18nValue("参数VALUE：")+value+"<br>"; 
					        	 }
					        	 return html;
					         }},
						  {width:160,align:'center',field:'CREATE_TIME', title: '创建时间',sort:true},
					      {width:120,align:'center',field:'ID', title: '操作',
					        	 templet:function(row){
					        		 var temp=' <span href="javascript:void(0)" class="layui-btn layui-btn-xs layui-btn-success" onclick="workorder.addInf(\''+row.ID+'\')"> '+getI18nValue('修改')+' </span>'
					        		 temp +=' <span href="javascript:void(0)" class="layui-btn layui-btn-xs layui-btn-danger" onclick="workorder.delInf(\''+row.ID+'\',\''+row.SORT_NUM+'\')"> '+getI18nValue('删除')+' </span>'
									return temp;
					        }},
			         ]
				],
				rowEvent: true,
				row: function(row) {
					//console.log(row);
				},
				done:function(res){
					layui.form.on('switch(switchTest)', function(obj){
							var elemTemp=obj.elem;
							var userId = $(elemTemp).attr("userId");
							var userAcc = $(elemTemp).attr("userAcc");
							layer.tips(getI18nValue('温馨提示：通知状态已改变'), obj.othis)
							if(obj.value=="Y"){
								workorder.OnChange(userId, userAcc, "N"); 
							}else{
								workorder.OnChange(userId, userAcc, "Y"); 
							}
						});
					execI18n();
				  }
				,success:function(){
						layui.use('form', function(){
							var form = layui.form; 
							//监听状态开关
							form.on('switch(switchStatuss)', function(data){
								var id=this.dataset.id;
								var status="02";
								if(this.checked==true){
									//启用
									status="01";
								}
								setGroupLeader(id,status);
							});
						});
					},
			});
   		}
		
		setGroupLeader = function(id,status){
		  	ajax.remoteCall("${ctxPath}/servlet/outInf?action=updateStatus", {id:id,status:status}, function(result) {
		  		if(result.state == 1){
				    layer.msg(result.msg,{icon: 1,time:1200,offset:'40px'},function(){
				    	workorder.loadData();
				    });
				}else{
					layer.alert(result.msg,{icon: 5});
				}
	  		});
		}
		function getCheckArr() {
			var arr = [];
			$.each(table.checkStatus("tree-table").data,function(i,n){
				arr.push(n.ID);
            });
			return arr;
		}
		function getCheckSortNumArr() {
			var arr = [];
			$.each(table.checkStatus("tree-table").data,function(i,n){
				arr.push(n.SORT_NUM);
            });
			return arr;
		}
		 
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>