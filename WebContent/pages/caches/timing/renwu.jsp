<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>定时任务管理</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form name="searchForm" class="form-inline" id="searchForm" data-toggle="render">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		       <h5><span class="glyphicon glyphicon-list"></span>定时任务管理</h5>
						   </div>
						  <hr style="margin: 5px -15px">
						  <div class="form-group">
						  		<div class="input-group input-group-sm">
							      <span class="input-group-addon">任务类型</span>	
									<select class="form-control input-sm" name="TYPE" style="width:100px" data-cust-context-path="/yq_common" data-mars="dict.getDictList('JOB_TYPE')">
										<option value="">请选择</option>
									</select>
							   </div>
							   <div class="input-group input-group-sm">
								      <span class="input-group-addon">任务名称</span>	
									  <input type="text" name="NAME" class="form-control input-sm" style="width:100px">
							   </div>
							   <div class="input-group input-group-sm">
							      <span class="input-group-addon">执行类型</span>	
									<select class="form-control input-sm" name="EXEC_TYPE" style="width:100px" data-cust-context-path="/yq_common" data-mars="dict.getDictList('EXEC_TYPE')">
										<option value="">请选择</option>
									</select>
							   </div>
							    <div class="input-group input-group-sm">
		           		              <span class="input-group-addon">执行时间段</span>	
									  <input type="text" name="startDate" id="startDate" class="form-control input-sm" style="width:150px" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',autoPickDate:true,maxDate:'#F{$dp.$D(\'endDate\')}'})">
									  <span class="input-group-addon">-</span>	
									  <input type="text" name="endDate" id="endDate" class="form-control input-sm" style="width:150px" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',autoPickDate:true,minDate:'#F{$dp.$D(\'startDate\')}'})">									  
							    </div>
							    <div class="input-group input-group-sm">
										<button type="button" class="btn btn-sm btn-default" onclick="CustTemp.loadData()"><span class="glyphicon glyphicon-search"></span> 查询</button>
								</div>
						   </div>
             	    </div>  
	              	<div class="ibox-content">
	              	<div class="row table-responsive">
		           	     <table style="min-width:3080px;width:100%" class="table table-auto table-bordered table-hover table-condensed" data-auto-fill="10" id="tableHead" data-mars="jobs.jobsQueruy">
                             <thead>
	                         	 <tr>
	                         	 	  <th>操作</th>
								      <th>任务类型</th>
								      <th>任务名称</th>
								      <th>执行类型</th>
								      <th>执行规则</th>
								      <th>延迟时间(秒)</th>
								      <th>成功次数</th>
								      <th>失败次数</th>
								      <th>上次执行时间</th>
								      <th>上次执行结果</th>
								       <th>请求接口</th>
								      <th>请求参数</th>
								      <th>备注</th>
								      <th>启用状态</th>
								      <th>创建人账号</th>
								      <th>创建时间</th>
								      <th>修改时间</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                             </tbody>
		                 </table>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for  list}}
										<tr>
											<td><a href="javascript:void(0)" onclick="CustTemp.playRecord('{{:ID}}')">详情 - </a>
											<a href="javascript:void(0)" onclick="CustTemp.insertData('{{:ID}}')">立即执行</a>
											</td>
											<td>{{dictFUN:TYPE "JOB_TYPE"}}</td>
											<td>{{:NAME}}</td>
											<td>{{dictFUN:EXEC_TYPE "EXEC_TYPE"}}</td>
											<td>{{:EXEC_RULE}}</td>
											<td>{{:EXEC_DELAY}}</td>
											<td>{{:SUCC_NUM}}</td>
											<td>{{:FAIL_NUM}}</td>
											<td>{{:LAST_EXEC_TIME}}</td>
											<td>{{:LAST_EXEC_RESULT}}</td>
											<td>{{:REQ_MAIN}}</td>
											<td>{{:REQ_PARAM}}</td>
											<td>{{:BAKUP}}</td>
											<td>{{dictFUN:ENABLE_STATUS "ENABLE_STATUS"}}</td>
											<td>{{:CREATE_ACC}}</td>
											<td>{{:CREATE_TIME}}</td>
											<td>{{:UPDATE_TIME}}</td>
									    </tr>
								    {{/for}}					         
							 </script>
	                     <div class="row paginate">
	                     	<jsp:include page="/pages/common/pagination.jsp"/>
	                     </div> 
	                   </div> 
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
		jQuery.namespace("CustTemp");
		requreLib.setplugs("wdate");
		CustTemp.loadData=function(){
			$("#searchForm").searchData();
		}
		CustTemp.playRecord = function(Id){
			popup.layerShow({type:2,title:'详情',offset:'20px',area:['1140px','660px']},"${ctxPath}/pages/timing/examine.jsp?Id="+Id,{});
		}
		CustTemp.insertData = function(Id) {
			var data = form.getJSONObject("#searchForm");
		 	ajax.remoteCall("${ctxPath}/servlet/dobse?action=diatelyQuery",{Id:Id},function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon: 1,time:1200},function(){
						biaoqian.renderData();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			  }
			);
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>