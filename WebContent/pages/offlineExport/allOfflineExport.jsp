<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="全部离线导出申请"> </title>
	<style>
		 .layui-table-cell {
		    height:auto;
		    overflow:visible;
		    text-overflow:inherit;
		    white-space:normal;
		}
		a {
    		color: #337ab7;
    		text-decoration: none;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
    <form name="generalForm" class="form-inline" id="generalForm">
    	<input type="hidden" id="wTimeStyleId" name="wTimeStyleId" value='${param.wTimeStyleId}'>
       	<div class="ibox">
       		<div class="ibox-title clearfix" id="divId">
       		      <h5><span class="" i18n-content="全部离线导出申请"></span><span><i class="layui-icon layui-icon-about" style="color: #f0ad4e;" i18n-title="数据权限"></i></span></h5>
       		      <div class="input-group input-group-sm pull-right btn-group ">
						<button  type="button" class="btn btn-sm btn-success btn-outline" onclick="list.batchCheck()" i18n-content="批量审核"></button>  
				  </div>
       		      <hr style="margin: 5px -15px">
       		      <div class="form-group">
       		      	<div class="input-group ">
                      	<span class="input-group-addon" i18n-content="报表名称"></span>
                      	<input type="text" name="CREATE_ACC" class="form-control input-sm" style="width:152px;" i18n-placeholder="输入报表名称">
                    </div>
                    <div class="input-group ">
                      	<span class="input-group-addon" i18n-content="申请人账号"></span>
                      	<input type="text" name="CREATE_NAME" class="form-control input-sm" style="width:120px;">
                    </div>
                    <div class="input-group ">
                      	<span class="input-group-addon" i18n-content="申请人姓名"></span>
                      	<input type="text" name="NAME" class="form-control input-sm" style="width:120px;">
                    </div>
       		      	<div class="input-group ">
						<span class="input-group-addon" i18n-content="状态"></span> 
						<select class="form-control input-sm" name="STATUS" id="STATUS" data-mars="common.getDict(EXP_APPLY_STATUS)">
							<option value="" i18n-content="请选择"></option>
						</select>
					</div>
				    <div class="input-group input-group-sm">
					 		<span class="input-group-addon" i18n-content="申请时间段"></span> 
							<input type="text" name="BEGIN_DATE" id="BEGIN_DATE" class="form-control input-sm layui-input"
								style="width:140px" autocomplete="off">
							 <span class="input-group-addon">-</span>	
							 <input type="text" name="END_DATE" id="END_DATE" class="form-control input-sm layui-input" 
							 	style="width:140px" autocomplete="off"> 
				    	</div>
				 	<div class="input-group ">
						 <button type="button" class="btn btn-sm btn-default" onclick="list.searchData('1')"><span class="glyphicon glyphicon-search" i18n-content="查询"></span></button>
				  	</div>
				  	<div class="input-group ">
							<button type="button" class="btn btn-sm btn-default" onclick="list.reset()"><span class="glyphicon glyphicon-repeat" i18n-content="重置"></span></button>
					</div> 	
				</div> 
	      </div>
      </div> 
        	<div class="ibox-content" >
        		<table id="main"></table>
         	</div> 
</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript">
	
	jQuery.namespace("list");
	
	layui.use('laydate', function(){
		  var laydate = layui.laydate;
		  laydate.render({ elem: '#BEGIN_DATE' ,type: 'datetime',lang:getDateLang()});
		  laydate.render({ elem: '#END_DATE' ,type: 'datetime',lang:getDateLang()});
	})
	
	$(function(){
		$("#generalForm").render({success : function(result){
			list.loadData();
		}});
		
	});
	
	//初始化加载数据
	list.loadData = function(){
		$("#generalForm").initTableEx({
			 mars:'offlineExport.allList',
			 height: 'full-180',
			 id:'main',
			 limit:15,
			 cols: [
				[
			      {width:60,field:'ID', title: getI18nValue('选择'), type: 'checkbox'}
			     ,{width:60,align:'center',title: getI18nValue('序号') ,type:'numbers'}
				 ,{field:'NAME',minWidth:300, title: getI18nValue('报表名称'),align:'center'}
		         ,{field:'CREATE_TIME', width:160,title: getI18nValue('申请时间'),align:'center'}
		         ,{field:'STATUS',width:100, title: getI18nValue('状态'),align:'center', templet:function(row){
		        	 return getDictTextByCode('EXP_APPLY_STATUS',row.STATUS);
		         }}
		         ,{field:'APPLY_REASON', minWidth:150,title: getI18nValue('申请原因'),align:'left'}
		         ,{field:'AUDIT_TIME', width:160,title: getI18nValue('审核时间'),align:'center'}
		         ,{field:'AUDIT_STATUS',width:100, title: getI18nValue('审核状态'),align:'center', templet:function(row){
		        	 return getDictTextByCode('EX_APPLY_AUDIT_STATUS',row.AUDIT_STATUS);
		         }}
		         ,{field:'DOWNLOAD_TIME', width:100,title: getI18nValue('下载次数'),align:'center'}
		         ,{width:130,title: getI18nValue('操作'),align:'center',fixed:'right', templet:function(row){
		        	 //如果导出完成，则提供导出按钮
		        	 var temp="";
		        	 
		        	 <EasyTag:res resId="cc-base-system-btnauth-offlineCheck">
		        	 	if("01"!=row.AUDIT_STATUS){
			        	 	temp+=' <span href="javascript:void(0)" class="layui-btn layui-btn-xs" onclick="list.checkOne(\''+row.ID+'\')">'+getI18nValue('审核')+' </span>';		        		 
			        	 }
		        	 </EasyTag:res>
		        	 
		        	 if("05"==row.STATUS){
		        		 var url = encodeURIComponent(encodeURIComponent(row.EXP_URL));
		        		 //temp=' <span href="javascript:void(0)" class="layui-btn layui-btn-xs" onclick="list.download(\''+url+'\')">'+getI18nValue('下载')+' </span>';
		        		 temp=' <a href="javaScript:list.download(\''+url+'\',\''+row.ID+'\');">'+getI18nValue("下载")+'</a>';
		        	 }else{
		        		 temp+=' <span href="javascript:void(0)" class="layui-btn layui-btn-disabled layui-btn-xs" >'+getI18nValue('下载')+' </span>';
		        	 }
		        	 return temp;
		        	 
		         }}
		       ]
			],
			done:function(res, curr, count){
				 //动态监听表头高度变化，冻结行跟着改变高度
		        $(".layui-table-header  tr").resize(function () {
		            $(".layui-table-header  tr").each(function (index, val) {
		                $($(".layui-table-fixed .layui-table-header table tr")[index]).height($(val).height());
		            });
		        });
		        //初始化高度，使得冻结行表头高度一致
		        $(".layui-table-header  tr").each(function (index, val) {
		            $($(".layui-table-fixed .layui-table-header table tr")[index]).height($(val).height());
		        });
		        //动态监听表体高度变化，冻结行跟着改变高度
		        $(".layui-table-body  tr").resize(function () {
		            $(".layui-table-body  tr").each(function (index, val) {
		                $($(".layui-table-fixed .layui-table-body table tr")[index]).height($(val).height());
		            });
		        });
		        //初始化高度，使得冻结行表体高度一致
		        $(".layui-table-body  tr").each(function (index, val) {
		            $($(".layui-table-fixed .layui-table-body table tr")[index]).height($(val).height());
		        });
				 
			}
		});
	}
	//查询数据
	list.searchData=function(flag){
		if(flag=='1'){
			$("#generalForm").queryData({id:'main',page:{ curr: 1}});
		}else{
			$("#generalForm").queryData({id:'main'});
		}
	};

	//重置
	list.reset=function(){
		$("#divId select").val("");
    	$("#divId input").val("");
	};
	
    list.allExport = function(){
    	popup.openTab("${ctxPath}/pages/offlineExport/allOfflineExport.jsp",getI18nValue("全部导出申请"),null);
	}
	
    list.download = function(filePath,id){
    	//记录下载次数
    	var params = {};
		params['id']=id;
		ajax.remoteCall("${ctxPath}/servlet/offlineExport?action=downloadCount",params,function(result) { 
			if(result.state == 1){
				window.location.href="${ctxPath}/servlet/attachment?action=download&filePath="+filePath; 
				$("#generalForm").queryData({id:'main'});
			}else{
			layer.msg(getI18nValue(result.msg),{icon: 5});
			}
		}); 
	}
    
    list.batchCheck = function(){
    	var checkStatus = table.checkStatus('main');
  		 var ids = checkStatus.data;
		 if (ids.length < 1) {
			layer.msg(getI18nValue('请选择需要审核的离线导出申请！'),{icon:0});
		} else {
			var arr = new Array();
			for (var i = 0; i < ids.length; i++) {
				var auditStatus = ids[i].AUDIT_STATUS;
				if("01"==auditStatus){
					layer.msg(getI18nValue('请勿选择审核状态为已经通过的离线导出！'),{icon:0});
					return;
				}
				arr.push(ids[i].ID);
			}
			popup.layerShow({type:1,title:getI18nValue('离线导出审核'),shadeClose:false,area:['420px','320px'],offset:'20px'},"${ctxPath}/pages/offlineExport/offlineExport-check.jsp",{ids:arr.toString()});
		}
	}
    list.checkOne = function(id){
    	var arr = new Array();
		arr.push(id);
		popup.layerShow({type:1,title:getI18nValue('离线导出审核'),shadeClose:false,area:['420px','320px'],offset:'20px'},"${ctxPath}/pages/offlineExport/offlineExport-check.jsp",{ids:arr.toString()});
	}
 

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>