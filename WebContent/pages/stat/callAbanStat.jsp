<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title i18n-content="来电放弃分析"></title>
	<style type="text/css">
		#dataList tr td {
			white-space: nowrap;
			min-width: 50px;
			max-width: 200px;
			text-overflow: ellipsis;
			overflow: hidden
		}
	
		#searchForm th {
			white-space: nowrap;
		}
		
		.layui-table, .layui-table-view {
			margin: 0 0;
		}
			
		.shadow {
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-moz-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
		}
		
		#sub {
			transform: translate(7px, 2px);
			display: inline-block;
		}
	
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form action="" method="post" name="searchForm" class="form-inline shadow"
		id="searchForm">
		<div class="ibox">
			<div class="ibox-title clearfix"  id="divId">
				<div class="form-group">
					<h5>
					<span i18n-content="来电放弃分析"></span>	 <span id="titleAndTime"> </span><span id="sub" ><i class="layui-icon layui-icon-about" style="color: #1E9FFF;"></i></span>
					</h5>
					<div class="input-group input-group-sm pull-right">
						<button type="button" class="btn btn-sm btn-info btn-outline "
							onclick="RptTaskStat.exportRptTaskStat()">
							  <span i18n-content="导出"></span>
						</button>
					</div>
				</div>
				<hr style="margin: 3px -15px">
				<div class="form-group">
					<div class="input-group input-group-sm ml-20">
						<div class="input-group input-group-sm">
							<span class="input-group-addon" i18n-content="统计日期"></span>
		     				 <div class="layui-input-inline">
		    			   		<input type="text" class="layui-input" id="limitDate" autocomplete="off" style="height:30px;width:190px" name="limitDate"> 
		   			  		 </div>
		   				</div>
						<div class="input-group input-group-sm">
							<span class="input-group-addon">-</span> 
							<select class="form-control input-sm" name="dateRange" onchange="RptTaskStat.onCasecade($(this))">
								<option value="" i18n-content="请选择"></option>
								<option value="today" i18n-content="今天"></option>
								<option value="yesterday" i18n-content="昨天"></option>
								<option value="thisWeek" i18n-content="本周"></option>
								<option value="RecentlyOneMonth" i18n-content="近一个月"></option>
								<option value="RecentlyThreeMonth" i18n-content="近三个月"></option>
							</select>
						</div>
					</div>
					<div class="input-group input-group-sm">
				         <span class="input-group-addon" i18n-content="时段"></span>	
				 		 <select multiple="multiple" size="1" id="hour" name="hour" class="form-control input-sm">
           			 		<c:forEach var="i" begin="0" end="23">
							    <option value="${i }">${i }</option>
           			 		</c:forEach>
          				 </select>
				  	</div>
					<!-- 统计维度 -->
					<div class="input-group input-group-sm">
						<span class="input-group-addon" i18n-content="统计类型"></span> <select class="form-control" id="stType" name="stType">
							<option value="01" selected="selected" i18n-content="按天"></option>
							<option value="02" i18n-content="按月"></option>
							<option value="03" i18n-content="按年"></option>
						</select>
					</div>
					<div class="input-group input-group-sm">
						<span class="input-group-addon" i18n-content="平台号码"></span>
						<div class="layui-input-inline">
							<input type="text" class="layui-input" id="systemPhone" autocomplete="off" style="height:30px;width:190px" name="systemPhone">
						</div>
					</div>
					<div class="input-group input-group-sm">
						<button type="button" class="btn btn-sm btn-default"
							onclick="RptTaskStat.searchData('1')">
							<span class="glyphicon glyphicon-search"></span><span i18n-content="查询"></span>
						</button>
					</div>
					
 					<div class="input-group ">
						<button type="button" class="btn btn-sm btn-default" onclick="RptTaskStat.reset()"><span class="glyphicon glyphicon-repeat"></span> <span i18n-content="重置"></span></button>
					</div> 
				</div>
			</div>
		    <div class="ibox-content">
				<table class="layui-table layui-form" id="tree-table" lay-size="sm"></table>
			</div>
			<div class="stat-desc" id="statDesc" style="display:none;color:#676A6C;">
				<fieldset class="content-title">
						<legend  i18n-content="统计口径"></legend>
				</fieldset>
				<p i18n-content="IVR放弃数：进入IVR未转队列挂机的数量"></p>
				<p i18n-content="IVR停留时长：进入IVR的停留总时长"></p>
				<p i18n-content="转队列数：按键后转人工的数量"></p>
				<p i18n-content="队列放弃数：转队列后未转入坐席时挂机的数量"></p>
				<p i18n-content="队列等待时长：转队列后的等待时长"></p>
				<p i18n-content="队列弃呼率：队列放弃数/转队列数"></p>
				<p i18n-content="转坐席数：转坐席的数量"></p>
				<p i18n-content="坐席接起数：坐席接通量"></p>
				<p i18n-content="坐席接起率：坐席接起数/转坐席数"></p>
				<p i18n-content="坐席放弃数：坐席未接数"></p>
				<p i18n-content="总振铃时长：振铃时长"></p>
				<p i18n-content="坐席弃呼率：坐席未接数/转坐席数"></p>
				<p i18n-content="总放弃量：IVR放弃数+队列放弃数+坐席放弃数"></p>
				<p i18n-content="总接通率：坐席接起数/坐席接起数+总放弃数"></p>
				<p i18n-content="转队列接通率：坐席接起数/转队列数"></p>
			</div>
		</div>
	</form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${ctxPath}/static/js/time.js"></script>
	<script type="text/javascript" src="${ctxPath}/static/js/tableCommon.js"></script>
	<script type="text/javascript">
	
		jQuery.namespace("RptTaskStat");
		
		 RptTaskStat.searchData=function(flag){
				if(flag=='1'){
					$("#searchForm").queryData({id:'tree-table',page:{curr:1}});
				}else{
					$("#searchForm").queryData({id:'tree-table'});
				}
			}
		//重置
		RptTaskStat.reset=function(){
			$("#divId select").val("");
			$("#stType").val("01");
			$("#limitDate").val(getThisMonthStartDate() + " ~ " + getTodayDate());
			requreLib.setplugs('multiselect',function(){
				$("#hour").multiselect({
					 enableFiltering: true,
					 maxHeight: 200,
					 includeSelectAllOption: true,
					 selectAllText:getI18nValue('全选'),
					 nonSelectedText: getI18nValue('请选择')
				});
			});
		};
		
		function queryData(){
			$("#searchForm").queryData({success:function(data){
				resultJson = data['statistic.callAbandonStatList'];
			}});
		}
        $(function() {
			layui.use('laydate', function(){
  				var laydate = layui.laydate;
  			  	laydate.render({ elem: '#limitDate',range: '~' ,format: 'yyyy-MM-dd', value: getTodayDate() + " ~ " + getTodayDate() });
	  		});
			$("#searchForm").render({success : function() {
		  		RptTaskStat.loadData();
			}});
			requreLib.setplugs('multiselect',function(){
				$("#hour").multiselect({
					 enableFiltering: true,
					 maxHeight: 200,
					 includeSelectAllOption: true,
					 selectAllText:getI18nValue('全选'),
					 nonSelectedText: getI18nValue('请选择')
				});
			});
		}) 
		
		RptTaskStat.loadData = function(){
			$("#searchForm").initTable({
				url:'${ctxPath}/webcall?action=statistic.callAbandonStatList'
				,limit:15
				,page:true 	
				,title:'来电放弃分析'
				,cellMinWidth:60
				,height:comHeight-185 //具体高度页面自定义
				,loading:true
				,id:'tree-table'
				,cols: [
					[
						{width:100,align:'center',title: '序号' ,fixed:'left',type:'numbers'},
						{minWidth:100,align:'center',field:'S_DATE',sort:true,fixed:'left', title: '日期'},
						{minWidth:100,align:'center',field:'IVR_CLEAR_COUNT', title: 'IVR放弃数',sort:true},
						{minWidth:100,align:'center',field:'IVR_STAY_TIME', title: 'IVR停留时长',sort:true, templet:function(row){
							return formatSeconds(row.IVR_STAY_TIME);
			        	}},
						{minWidth:100,align:'center',field:'TO_QUEUE_COUNT', title: '转队列数',sort:true},
						{minWidth:100,align:'center',field:'TO_QUEUE_CLEAR_COUNT', title: '队列放弃数',sort:true},
			        	{minWidth:100,align:'center',field:'TO_QUEUE_TIME', title: '队列等待时长',sort:true,templet:function(row) {
			        		return formatSeconds(row.TO_QUEUE_TIME);
			        	}},
			        	{minWidth:100,align:'center',field:'', title: '队列弃呼率',sort:true, templet:function(row){
			        		var val1 = parseInt(row.TO_QUEUE_CLEAR_COUNT);
			        		var val2 = parseInt(row.TO_QUEUE_COUNT);
			        		if(val2 == 0) {
			        			return '0%';
			        		} else {
				        		return formatPerse(val1/val2) + "%";
			        		}
			        	}},
			        	{minWidth:100,align:'center',field:'TO_AGENT_COUNT', title: '转坐席数',sort:true},
						{minWidth:100,align:'center',field:'CONN_SUCC_COUNT', title: '坐席接起数',sort:true},
						{minWidth:100,align:'center',field:'CONN_SUCC_RATIO', title: '坐席接起率',sort:true, templet:function(row){
							var val1 = parseInt(row.CONN_SUCC_COUNT);
			        		var val2 = parseInt(row.TO_AGENT_COUNT);
			        		if(val2 == 0) {
			        			return '0%';
			        		} else {
				        		return formatPerse(val1/val2) + "%";
			        		}
			        	}},
						{minWidth:150,align:'center',field:'NOANSWER_COUNT', title: '坐席放弃数',sort:true},
						{minWidth:100,align:'center',field:'AGENT_STAY_TIME', title: '总振铃时长',sort:true,templet:function(row) {
			        		return formatSeconds(row.AGENT_STAY_TIME);
			        	}},
			        	{minWidth:100,align:'center',field:'', title: '坐席弃呼率',sort:true, templet:function(row){
			        		var val1 = parseInt(row.NOANSWER_COUNT);
			        		var val2 = parseInt(row.TO_AGENT_COUNT);
			        		if(val2 == 0) {
			        			return '0%';
			        		} else {
				        		return formatPerse(val1/val2) + "%";
			        		}
			        	}},
			        	{minWidth:100,align:'center',field:'TOTAL_CLEAR_COUNT',sort:true, title: '总放弃量'},	
			        	{minWidth:100,align:'center',field:'', title: '总放弃率',sort:true, templet:function(row){
			        		var val1 = parseInt(row.TOTAL_CLEAR_COUNT);
			        		var val2 = parseInt(row.TO_QUEUE_COUNT) + parseInt(row.IVR_CLEAR_COUNT);
			        		if(val2 == 0) {
			        			return '0%';
			        		} else {
				        		return formatPerse(val1/val2) + "%";
			        		}
			        	}},
			        	{minWidth:100,align:'center',field:'', title: '总接通率',sort:true, templet:function(row){
			        		var val1 = parseInt(row.CONN_SUCC_COUNT);
			        		var val2 = parseInt(row.TO_QUEUE_COUNT) + parseInt(row.IVR_CLEAR_COUNT);
			        		if(val2 == 0) {
			        			return '0%';
			        		} else {
				        		return formatPerse(val1/val2) + "%";
			        		}
			        	}},
			        	{minWidth:100,align:'center',field:'', title: '转队列接通率',sort:true, templet:function(row){
			        		var val1 = parseInt(row.CONN_SUCC_COUNT);
			        		var val2 = parseInt(row.TO_QUEUE_COUNT);
			        		if(val2 == 0) {
			        			return '0%';
			        		} else {
				        		return formatPerse(val1/val2) + "%";
			        		}
			        	}}
			         ]
				],
				done:function(res,curr,count){
		        	if(res) {
		        		var updateTime = res.updateTime;
						if(updateTime&&updateTime!=""){
							$("#titleAndTime").html("<font color='#5cb85c'>("+getI18nValue("数据更新时间")+""+updateTime+")</font>");
						}
						resultJson = res;
		        	}
		        } 
			});
       }
		
		RptTaskStat.exportRptTaskStat = function(){//'是否导出来电放弃分析？'
			layer.confirm(getI18nValue("是否导出来电放弃分析"),{icon: 3, title:getI18nValue('导出提示'),btn:[getI18nValue('确定'),getI18nValue('取消')],offset:'20px'}, function(index){
				layer.close(index);
				var hourId = "";
				var data = form.getJSONObject("#searchForm");
				if(data.hour){
					hourId = JSON.stringify(data.hour);
				}
				location.href = "${ctxPath}/servlet/export?action=exportCallAbanStat&"+$("#searchForm").serialize() + "&hourId="+encodeURI(hourId);
			});
		}
		
		//格式化为空的数字
		function formatNum(value) {
			if (value && value != "") {
				return value;
			}
			return 0;
		}
		
		function formatToMinute(seconds) {
            var f = parseFloat(seconds);
            if (isNaN(f)||f==0) {
                return 0;
            }
            if(f>0&&f<60){
            	return 1;
            }
            return Math.round(parseFloat(f/60)*100)/100;
        }
        //格式化秒数
        function formatSeconds(value) {
            if(parseInt(value).toString() == 'NaN'){
                return "00:00:00";
            }
            var theTime = parseInt(value);// 秒
            var theTime1 = 0;// 分
            var theTime2 = 0;// 小时
            if(theTime > 60) {
                theTime1 = parseInt(theTime/60);
                theTime = parseInt(theTime%60);
                if(theTime1 > 60) {
                    theTime2 = parseInt(theTime1/60);
                    theTime1 = parseInt(theTime1%60);
                }
            }
            var result = theTime;
            if(theTime<10){
                result = "0"+theTime;
            }
            if(theTime1 > 0) {
                if(theTime1<10){
                    result = "0"+theTime1+":"+result;
                }else{
                    result = ""+theTime1+":"+result;
                }
            }else{
                result = "00:"+result;
            }
            if(theTime2 > 0) {
                if(theTime2<10){
                    result = "0"+theTime2+":"+result;
                }else{
                    result = ""+theTime2+":"+result;
                }
            }else{
                result = "00:"+result;
            }
            return result;
        }
		function formatInt(value){
			if(parseInt(value).toString() == 'NaN'){
		    	return 0;
		    }
            return Math.round(value);
		}
		
		function formatPerse(x){
			var f = parseFloat(x); 
		    if (isNaN(f)) { 
		        return 0; 
		    } 
		    f = Math.round(x*10000)/100; 
		    return f+""; 
		}
		//设置时间
		RptTaskStat.onCasecade = function(p){
      	var dateRange = p.val();
         	if(dateRange == "today") {
         		$("#limitDate").val(getTodayDate() + " ~ " + getTodayDate());
         	}else if(dateRange == "yesterday") {
      		$("#limitDate").val(getYesterDayDate() + " ~ " + getYesterDayDate());
      	}else if(dateRange == "thisWeek") {
         		$("#limitDate").val(getThisWeekStartDate() + " ~ " + getThisWeekEndDate());
         	}else if(dateRange == "RecentlyOneMonth") {
         		$("#limitDate").val(getThisMonthStartDate() + " ~ " + getThisMonthEndDate());
         	}else if(dateRange == "RecentlyThreeMonth") {
      		$("#limitDate").val(getRecentlyThreeMonthStartDate() + " ~ " + getTodayDate());
      	}
      }
		
	</script>

</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>
