<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title i18n-content="话务坐席全数据总览"></title>
	<style type="text/css">
	.layui-table, .layui-table-view {
		margin: 0 0;
	}
	.shadow {
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
		-moz-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
		-webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
	}
	
	#sub {
		transform: translate(7px, 2px);
		display: inline-block;
	}
	.layui-laydate-content>.layui-laydate-list {
		padding-bottom: 0px;
		overflow: hidden;
	}
	
	.layui-laydate-content>.layui-laydate-list>li {
		width: 100%
	}
	/* #limitHour .layui-laydate-main{width:600px;} */
	.merge-box .scrollbox .merge-list {
		padding-bottom: 5px;
	}
	.ztreeDiv {
		display: none;
		position: absolute;
		border: 1px solid rgb(170, 170, 170);
		min-width: 150px;
		max-width: 300px;
		max-height: 200px;
		z-index: 10;
		overflow: auto;
		background-color: #F0F0F0
	}
</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form class="form-inline shadow" id="searchForm" data-page-hide="true">
		<div class="ibox">
			<div class="ibox-title clearfix">
				<div class="form-group">
					<h5>
						<span i18n-content="话务坐席全数据总览"></span>
						<span id="titleAndTime"></span>
						<span id="sub">
							<i class="layui-icon layui-icon-about" style="color: #1E9FFF;"></i>
						</span>
					</h5>
					<div class="input-group pull-right">
						<button type="button" class="btn btn-sm btn-info btn-outline" onClick="AgentStat.exportDetail()" i18n-content="导出">
						</button>
					</div>
				</div>
				<hr style="margin: 5px -15px">
				<div class="form-group" id="divId">
					<div class="input-group input-group-sm ml-20">
						<div class="input-group input-group-sm">
							<span class="input-group-addon" i18n-content="日期"></span>
							<div class="layui-input-inline">
								<input type="text" class="layui-input" id="limitDate" autocomplete="off" style="height: 30px; width: 200px" name="limitDate">
							</div>
						</div>
						<div class="input-group input-group-sm">
							<span class="input-group-addon">-</span> 
							<select class="form-control input-sm" name="dateRange" onchange="AgentStat.onCasecade($(this))">
								<option value="" i18n-content="请选择"></option>
								<option value="today" i18n-content="今天"></option>
								<option value="yesterday" i18n-content="昨天"></option>
								<option value="thisWeek" i18n-content="本周"></option>
								<option value="RecentlyOneMonth" i18n-content="近一个月"></option>
								<option value="RecentlyThreeMonth" i18n-content="近三个月"></option>
							</select>
						</div>
					</div>
					<div class="input-group input-group-sm" style="position: inherit">
						 <span class="input-group-addon"  i18n-content="技能组"></span>
						 <input name="skillName" id="skillName" readonly="readonly" onclick="showMenu(this,'consultTypeZtree')" class="form-control input-sm">
						 <input name="skillId" id="skillId" readonly="readonly" class="hidden">
						<div class="ztreeDiv" id="consultTypeZtree">
							<div class="ztree" DATA-MARS="common.groupTreeVoice" id="ztree" data-setting="{callback: {onClick: zTreeOnClick}}" style="max-height: 530px; overflow: auto; padding: 15px;">
							</div>
						</div>
					</div>
					<div class="input-group input-group-sm">
						<span class="input-group-addon" i18n-content="坐席"></span>
						<select id="agentId" name="agentId" class="form-control input-sm" data-mars="common.userDictByName">
							<option value="" i18n-content="请选择"></option>
						</select>
					</div>
					
					<div class="input-group input-group-sm">
						<button type="button" class="btn btn-sm btn-default" onclick="AgentStat.searchData('1')">
							<span class="glyphicon glyphicon-search"></span><span i18n-content="查询"></span>
						</button>
					</div>
					<div class="input-group ">
						<button type="button" class="btn btn-sm btn-default" onclick="AgentStat.reset()">
							<span class="glyphicon glyphicon-repeat"></span><span i18n-content="重置"></span>
						</button>
					</div>
				</div>
			</div>
			<div class="ibox-content table-responsive">
				<table id="tree-table">
				</table>
			</div>
			<div class="stat-desc" id="statDesc" style="display: none; color: #676A6C;">
				<fieldset class="content-title">
					<legend i18n-content="统计口径"></legend>
				</fieldset>
				<p i18n-content="1、坐席服务数：被分配话务数量（坐席未接数+坐席接通数）"></p>
				<p i18n-content="2、坐席未接数：被分配坐席后，未被接通的电话数量"></p>
				<p i18n-content="3、坐席接通数：人工坐席接通数量"></p>
				<p i18n-content="4、振铃时长：分配坐席后到坐席接通的时长（坐席接通时间-振铃时间）"></p>
				<p i18n-content="5、人工服务时长：人工坐席服务时长（人工结束时间-坐席接通时间）"></p>
				<p i18n-content="6、话后时长：处理话后业务时长（就绪状态时间-上一次人工结束时间）"></p>
				<p i18n-content="7、置忙时长：处于“置忙”状态的时长"></p>
				<p i18n-content="8、空闲时长：处于“空闲”状态的时长"></p>
				<p i18n-content="9、5s接通数：振铃时长小于等于5秒的电话接通数量"></p>
				<p i18n-content="10、10s接通数：振铃时长小于等于10秒的电话接通数量"></p>
				<p i18n-content="11、15s接通数：振铃时长小于等于15秒的电话接通数量"></p>
				<p i18n-content="12、20s接通数：振铃时长小于等于20秒的电话接通数量"></p>
				<p i18n-content="13、呼出总数：发起外呼的电话数量"></p>
				<p i18n-content="14、外呼接通数：外呼被接通的电话数量"></p>
				<p i18n-content="15、外呼时长：外呼电话的时长（外呼结束时间-客户接通时间）"></p>
				<p i18n-content="16、参与评价数：发起满意度调查的数量"></p>
				<p i18n-content="17、好评数：被评价满意的数量"></p>
			</div>
		</div>
	</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript"
		src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${ctxPath}/static/js/time.js"></script>
	<script type="text/javascript" src="${ctxPath}/static/js/tableCommon.js"></script>
	<script type="text/javascript">
		jQuery.namespace("AgentStat");

		//查询
		AgentStat.searchData = function(flag) {
			if(!$("#limitDate").val()) {
				layer.msg("请选择日期", {icon: 5,offset:'60px',time:1200});
				return;
			}
			if (flag == '1') {
				$("#searchForm").queryData({
					id : 'tree-table',
					page : {
						curr : 1
					}
				});
			} else {
				$("#searchForm").queryData({
					id: 'tree-table'
				});
			}
		}
		//重置
		AgentStat.reset = function() {
			$("#divId input").val("");
			$("#divId select").val("");
			$("#limitDate").val(getTodayDate() + " ~ " + getTodayDate());
		};
		$(function() {
			$("#searchForm").render({success : function() {
				layui.use('laydate', function() {
					var laydate = layui.laydate;
					laydate.render({
						elem : '#limitDate',
						range : '~',
						format : 'yyyy-MM-dd',
						value : getTodayDate() + " ~ " + getTodayDate()
					});
				});
				AgentStat.loadData();
			}});
		})

		AgentStat.loadData = function() {
			$("#searchForm").initTableEx({
				url : '${ctxPath}/webcall?action=report.agentWholeCallList',
				id : 'tree-table',
				limit : 15,
				page : true,
				title : getI18nValue('话务座席全数据总览计'),
				height : comHeight - 145, //具体高度页面自定义
				cellMinWidth : 60,
				rowEvent : true,
				loading : true,
				cols : [ [
						{
							minWidth : 150,
							align : 'center',
							field : 'DATE_ID',
							title : getI18nValue('日期'),
							fixed : 'center'
						},
						{
							minWidth : 150,
							align : 'center',
							field : 'TIME_FRAME',
							title : getI18nValue('半小时段'),
							fixed : 'left'
						},
						{
							minWidth : 150,
							align : 'center',
							field : 'AGENT_COUNT',
							sort : true,
							title : getI18nValue('坐席服务数'),
							totalRow : true,
							templet : function(row) {
								return parseInt(formatNum(row.NOANSWER_COUNT)) + parseInt(formatNum(row.AGENT_ANSWER_COUNT));
							}
						},
						{
							minWidth : 150,
							align : 'center',
							field : 'AGENT_MISS_ANSWER_COUNT',
							title : getI18nValue('坐席未接数'),
							totalRow : true,
							templet : function(row) {
								return formatNum(row.NOANSWER_COUNT);
							}
						},
						{
							minWidth : 150,
							align : 'center',
							field : 'AGENT_ANSWER_COUNT',
							title : getI18nValue('坐席接通数'),
							totalRow : true,
							templet : function(row) {
								return formatNum(row.AGENT_ANSWER_COUNT)
							}
						},
						{
							minWidth : 150,
							align : 'center',
							field : 'AGENT_TIME',
							title : getI18nValue('振铃时长'),
							totalRow : true,
							templet : function(row) {
								return formatSecond(row.AGENT_TIME)
							}
						},
						{
							minWidth : 150,
							align : 'center',
							field : 'AGENT_SERVER_TIME',
							title : getI18nValue('人工服务时长'),
							totalRow : true,
							templet : function(row) {
								return formatSecond(row.AGENT_SERVER_TIME)
							}
						},
						{
							minWidth : 150,
							align : 'center',
							field : 'LESS_FIVE_COUNT',
							title : getI18nValue('5s接通数'),
							totalRow : true,
							templet : function(row) {
								return formatNum(row.LESS_FIVE_COUNT);
							}
						},
						{
							minWidth : 150,
							align : 'center',
							field : 'LESS_TEN_COUNT',
							title : getI18nValue('10s接通数'),
							totalRow : true,
							templet : function(row) {
								return formatNum(row.LESS_TEN_COUNT);
							}
						},
						{
							minWidth : 150,
							align : 'center',
							field : 'LESS_FIFTEEN_COUNT',
							title : getI18nValue('15s接通数'),
							totalRow : true,
							templet : function(row) {
								return formatNum(row.LESS_FIFTEEN_COUNT);
							}
						},
						{
							minWidth : 150,
							align : 'center',
							field : 'LESS_TWENTY_COUNT',
							title : getI18nValue('20s接通数'),
							totalRow : true,
							templet : function(row) {
								return formatNum(row.LESS_TWENTY_COUNT);
							}
						},
						{
							minWidth : 150,
							align : 'center',
							field : 'CALL_COUNT',
							title : getI18nValue('呼出总数'),
							totalRow : true,
							templet : function(row) {
								return formatNum(row.CALL_COUNT)
							}
						},
						{
							minWidth : 150,
							align : 'center',
							field : 'CONN_SUCC_COUNT',
							title : getI18nValue('外呼接通数'),
							totalRow : true,
							templet : function(row) {
								return formatNum(row.CONN_SUCC_COUNT)
							}
						},
						{
							minWidth : 150,
							align : 'center',
							field : 'OUT_TOTAL_TIME',
							title : getI18nValue('外呼时长'),
							totalRow : true,
							templet : function(row) {
								return formatSecond(row.OUT_TOTAL_TIME)
							}
						},
						{
							minWidth : 150,
							align : 'center',
							field : 'SATISF_COUNT',
							title : getI18nValue('参与评价数'),
							totalRow : true,
							templet : function(row) {
								return formatNum(row.SATISF_COUNT)
							}
						},
						{
							minWidth : 150,
							align : 'center',
							field : 'GOOD_COUNT',
							title : getI18nValue('好评数'),
							totalRow : true,
							templet : function(row) {
								return formatNum(row.GOOD_COUNT)
							}
						}
	
				] ],
				done : function(res, curr, count) {
					if (res) {
						var updateTime = res.updateTime;
						if (updateTime && updateTime != "") {
							$("#titleAndTime").html("<font color='#5cb85c'>("
								+ getI18nValue("数据更新时间")
								+ ""
								+ updateTime
								+ ")</font>");
						}
						resultJson = res;
					}
				} 
			});

		}

		//格式化为空的数字
		function formatNum(value) {
			if (value && value != "") {
				return value;
			}
			return 0;
		}
		//格式化为整数的数字
		function formatNumZero(value) {
			if (value && value != "") {
				return value.toFixed(0);
			}
			return 0;
		}
		//格式化为两个小数点的数字
		function formatNumTwo(value) {
			if (value && value != "") {
				return Number(value).toFixed(2);
			}
			return 0.00;
		}
		function formatSecond(time) {
			if (time == undefined || time == null || time == '')
				return "0:00:00";
			if (isNaN(time)) {
				return "0:00:00";
			}
			time = parseInt(time);
			var h = Math.floor(time / 3600);
			var m = Math.floor(time % 3600 / 60);
			var s = time % 60;
			m = m < 10 ? '0' + m : m;
			s = s < 10 ? '0' + s : s;

			return h + ":" + m + ":" + s;
		}

		function formatPerse(v1, v2) {
			if (isNaN(v1) || isNaN(v2) || v1 == "" || v2 == "") {
				return 0;
			}
			var x = v1 / v2;
			if (isNaN(x)) {
				return 0;
			}
			var f = parseFloat(x);
			f = Math.round(x * 10000) / 100;
			return f + "";
		}

		function formatPerseZero(v1, v2) {
			if (isNaN(v1) || isNaN(v2) || v1 == "" || v2 == "") {
				return 0;
			}
			var x = v1 / v2;
			if (isNaN(x)) {
				return 0;
			}
			var f = parseFloat(x);
			return f.toFixed(0);
			//f = Math.round(x*10000)/100;
			// return f+"";
		}

		function formatDate(date) {
			if (date) {
				return date.substring(0, 4) + date.substring(4, 6)
						+ date.substring(6);
			}
			return "";
		}
		function formatHour(hour) {
			if (hour) {
				if (hour < 10) {
					return "0" + hour + ":00";
				}
				return hour + ":00";
			}
			return "";
		}
		
		function formatMinute(minute) {
			if (minute=='30') {
				return "00:00-00:29";
			}else if(minute=='59'){
				return "00:30-59:59";
			}else{
				return "00:00-00:00";
			}
		}
		
		function halfHour(hourId, minuteId){
			if(minuteId=="30"){
				return hourId + ":00 - " + hourId + ":29";
			}else{
				return hourId + ":30 - " + hourId + ":59";
			}
		}
		
		//设置时间
		AgentStat.onCasecade = function(p) {
			var dateRange = p.val();
			if (dateRange == "today") {
				$("#limitDate").val(getTodayDate() + " ~ " + getTodayDate());
			} else if (dateRange == "yesterday") {
				$("#limitDate").val(getYesterDayDate() + " ~ " + getYesterDayDate());
			} else if (dateRange == "thisWeek") {
				$("#limitDate").val(getThisWeekStartDate() + " ~ " + getThisWeekEndDate());
			} else if (dateRange == "RecentlyOneMonth") {
				$("#limitDate").val(getThisMonthStartDate() + " ~ " + getThisMonthEndDate());
			} else if (dateRange == "RecentlyThreeMonth") {
				$("#limitDate").val(getRecentlyThreeMonthStartDate() + " ~ " + getTodayDate());
			}
		}
		
		var skillZreeId;
		var zTree, rMenu;
		//技能组点击
		function zTreeOnClick(event, treeId, treeNode) {
			$("#skillName").val(treeNode.name);
			$("#skillId").val(treeNode.SKILL_GROUP_CODE);
			skillZreeId = treeNode.SKILL_GROUP_CODE;
			onChangeSkill();
			hideMenu('consultTypeZtree');
			
		}
		//技能组选择
		function onChangeSkill(){
			var skillGroupId = skillZreeId;
			var data = {};
			data.skillGroupId = skillGroupId;
			ajax.remoteCall("${ctxPath}/webcall?action=common.userDictByRecord",data,function(result) { 
				$("#agentId").find("option").remove();
				if(result.total>0){
					var agent = document.getElementById("agentId");
					var optionstring="";
					$.each(result.data,function(key,value){  
	                     optionstring += "<option data-fetch value=\"" + key + "\" >" + value + "</option>";  
	                 }); 
	                 $("#agentId").html("<option value='' i18n-content='请选择'></option> "+optionstring);
				}else{
					$("#agentId").html("<option value='' i18n-content='请选择'></option> ");
				}
			});
			
		}
		//显示菜单
		function showMenu(obj,treeId) {
			var leftPx = $(obj).offset().left;
			var topPx = $(obj).offset().top;
			var heightPx = $(obj).height()+$(obj).innerHeight()/2;
		    $("#"+treeId).css({ left: leftPx, top: topPx+heightPx }).slideDown("fast");
		    	if(treeId=="consultTypeZtree"){
			    	$("body").bind("mousedown", onBodyDownPc);
		    	}else{
			    	$("body").bind("mousedown", onBodyDownPc1);
		    	}
		}
		//隐藏菜单
		function hideMenu(divId) {
		    $("#"+divId).fadeOut("fast");
		    	$("body").unbind("mousedown", onBodyDownPc);
		}
		//全局点击事件
		function onBodyDownPc(event) {
		    if (!( event.target.id == "consultTypeZtree" || event.target.id == "proCodeShow" || $(event.target).parents("#ztree").length > 0)) {
		        hideMenu('consultTypeZtree');
		  	}
		}
		
		//导出
		AgentStat.exportDetail = function(){
			if(!$("#limitDate").val()) {
				layer.msg("请选择日期", {icon: 5,offset:'60px',time:1200});
			}
			layer.confirm(getI18nValue('是否导出话务坐席全数据总览'), {icon: 3, title: getI18nValue('导出提示'), offset: '20px'}, function (index) {
            	layer.close(index);
				location.href="${ctxPath}/servlet/statExport?action=agentWholeCallStat&"+$("#searchForm").serialize();
            });
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>