<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="外呼号码选择"></title>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="groupSelect">
		<input type="hidden" name="groupId" id="groupId" value="${param.groupId }"/>
		<input type="hidden" name="groupName" id="groupName" value="${param.groupName }"/>
		<div id="selectUser" class="row" >
			<div class="col-xs-5">
				<div class="panel panel-default">
				  <div class="panel-heading" i18n-content="可选号码">(<font id="leftNum">0</font>)</div>
				  <div class="panel-body" data-mars="prefixGroup.selectPrefixList"  data-template="list-templates">
				    
				  </div>
				</div>
			</div>
			<script id="list-templates" type="text/x-jsrender">
               <select class="form-control" id="select1"  style="min-height: 233px" multiple="multiple">
                  {{for data}}
                      <option value="{{:PREFIX_NUM}}">{{:PREFIX_NUM}}</option>
                  {{/for}}
               </select>
            </script>
			<div class="col-xs-2">
				<button class="btn btn-success btn-sm mt-50" id="add" type="button" i18n-content="添加"></button>
				<button class="btn btn-success btn-sm mt-20" id="add_all" type="button" i18n-content="全部添加"></button>
				<button  class="btn btn-default btn-sm mt-20" id="remove" type="button" i18n-content="移除"></button>
				<button  class="btn btn-default btn-sm mt-20" id="remove_all" type="button" i18n-content="全部移除"></button>
			</div>
			<div class="col-xs-5">
				<div class="panel panel-default">
				  <div class="panel-heading" i18n-content="已选号码">(<font id="rightNum">0</font>)</div>
				  <div class="panel-body">
				      <select class="form-control" name="prefixNums" id="select2" style="min-height: 233px" multiple="multiple"></select>
				  </div>
				</div>
			</div>
		</div>
		<div class="layer-foot text-c">
		   		<button class="btn btn-sm btn-primary"  type="button" onclick="saveData()" i18n-content="保存"></button>
		   		<button class="btn btn-sm btn-default ml-20"  type="button"  onclick="popup.layerClose(this)" i18n-content="关闭"></button>
	    </div>
	</form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	$(function(){
		
		$("#selectUser").render({data:{pk:'${param.groupId}'},success:function(result){
			$("#select2 option").prop("selected",true);
			
			$('#add').click(function(){
		        //获取选中的选项，删除并追加给对方
		        $('#select1 option:selected').appendTo('#select2'); 
		        computedNum();
		    });
			  //移到左边
		    $('#remove').click(function(){
		    	  $('#select2 option:selected').appendTo('#select1');
		    	  computedNum();
		    });
		  //全部移到右边
		    $('#add_all').click(function(){
		        //获取全部的选项,删除并追加给对方
		        $('#select1 option').appendTo('#select2');
		        computedNum();
		    });
		    //全部移到左边
		    $('#remove_all').click(function(){
		        $('#select2 option').appendTo('#select1');
		        computedNum();
		    });
		     
		    //双击选项
		    $('#select1').dblclick(function(){ //绑定双击事件
		        //获取全部的选项,删除并追加给对方
		        $("option:selected",this).appendTo('#select2'); //追加给对方
		        computedNum();
		    });
		     
		    //双击选项
		    $('#select2').dblclick(function(){
		        $("option:selected",this).appendTo('#select1');
		        computedNum();
		    });
			
		    //设置初始的可选号码数量
		    if(result['prefixGroup.selectPrefixList']){
			    var total = result['prefixGroup.selectPrefixList'].total;
				if(total&&total!=""){
			    	$("#leftNum").text(total);
				}
		    }
		}});
	});
	    function saveData() {
	    	$("#select2 option").prop("selected",true);
			var data = form.getJSONObject("#groupSelect");
			ajax.remoteCall("${ctxPath}/servlet/prefixGroup?action=select",data,function(result) { 
					if(result.state == 1){
						parent.Prefix.searchData();
						parent.layer.closeAll();
					}else{
						parent.layer.alert(result.msg,{icon: 5});
					}
				}
			);
		}
	    function getUserName(val){
	    	if(val!=''){
	    		return "("+val+")";
	    	}else{
	    		return "";
	    	}
	    }
	    
	    //计算可选数量和已选的数量
	    function computedNum(){
	    	var chNum = $("#select2 option").size();
	    	$("#rightNum").text(chNum);
	    	var chNum2 = $("#select1 option").size();
	    	$("#leftNum").text(chNum2);
	    }
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>