<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="群组管理"></title>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="easyform" data-mars="smsUserGroup.userGroupInfo" style="margin-top: 10px;"  autocomplete="off" data-mars-prefix="">
		<input type="hidden" name="GROUP_ID" value="${param.id}"> 
	  	<table class="table table-edit table-vzebra">
	        <tbody>
            	<tr>
                	<td class="required" width="60px" i18n-content="上级群组"></td>
	                <td width="220px"><input type="text" name="USER.GROUP_NAME" disabled="disabled" class="form-control input-sm" ></td>
	            </tr>
	            <tr>
	                <td class="required" i18n-content="群组名称"></td>
	                <td><input type="text" name="GROUP_NAME" id="GROUP_NAME" data-rules="required" class="form-control input-sm"></td>
	            </tr>
	            <!-- <tr>
	                <td i18n-content="是否公开"></td>
	                <td>
	                    <label class="radio-inline">
	           				<input type="radio" value="y" checked="checked" name="IS_PUBLIC"> <span i18n-content="公开"></span>
	        			</label>
	        			<label class="radio-inline">
       				 		<input type="radio" value="n"  name="IS_PUBLIC"> <span i18n-content="不公开"></span>
	        			</label>
	    			</td>
				</tr> -->
             	<tr>
	                <td i18n-content="群组描述"></td>
	                <td>
	                   <textarea class="form-control input-sm" name="GROUP_DESC" id="GROUP_DESC" rows="3"></textarea>
	                </td>
	            </tr>
   			</tbody>
   		</table>
		<div class="layer-foot text-c">
	 		<button class="btn btn-sm btn-primary"  type="button" id="submit-form" onclick="userEdit.ajaxSubmitForm()" i18n-content="保存">保存</button>
			<button class="btn btn-sm btn-default ml-20"  type="button" onclick="layer.closeAll();" i18n-content="关闭">关闭</button>
		</div>
	</form>				
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript" src="/cc-sms/static/js/my_i18n.js?v=2020061"></script> 
	<script type="text/javascript" src="/cc-base/static/js/i18n.js?v=2"></script> 
	<script type="text/javascript">
	jQuery.namespace("userEdit");
	userEdit.id='${param.id}';
	$(function(){
		$("#easyform").render();
		getQuesData(userEdit.id);
	});
	userEdit.ajaxSubmitForm = function(){
		if(form.validate("easyform")){		
			userEdit.updateData(); 	
		};
	}
	userEdit.updateData = function() {
		var data = form.getJSONObject("easyform");
		ajax.remoteCall("/cc-sms/servlet/smsUserGroup?query=groupUpdate",data,function(result) { 
			if(result.state == 1){
				layer.closeAll();
				window.location.reload(); 
				}else{
				layer.alert(getI18nValue(result.msg),{icon: 5});
			}
		});
	}
	function getQuesData(tId) {
		var data = {};
		data.id = tId;
		ajax.remoteCall("/cc-sms/servlet/smsUserGroup?query=groupInfo", data, function(result) {
			if (result.state == 1) {
				var ret = result.data;
				$("#PARENT_NAME").val(ret.GROUP_NAME);
				$("#GROUP_NAME").val(ret.GROUP_NAME);
				initradio('IS_PUBLIC',ret.IS_PUBLIC);
				$("#GROUP_DESC").html(ret.GROUP_DESC);
			} else {
				layer.alert(result.msg, {icon : 5});
			}
		});
	}
	function initradio(rName,rValue){
	    var rObj = document.getElementsByName(rName);
	    for(var i = 0;i < rObj.length;i++){
	        if(rObj[i].value == rValue){
	            rObj[i].checked =  'checked';
	        }
	    }
	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>