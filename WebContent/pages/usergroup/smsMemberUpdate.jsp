<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="群组成员管理"></title>
</EasyTag:override>
<EasyTag:override name="content">
<form id="easyform" data-mars="" style="margin-top: 10px;"  autocomplete="off" data-mars-prefix="">
	<input type="hidden"  name="deptId" id="deptId" value="${param.tId}">
	<input type="hidden"  name="ACCOUNT1" id="ACCOUNT1">
	<input type="hidden"  name="PHONENUM1" id="PHONENUM1">
	<table class="table table-edit table-vzebra">
    	<tbody>
	        <tr>
	            <td class="required" i18n-content="成员账号"></td>
	            <td><input type="text" name="ACCOUNT" min="1" max="10" id="ACCOUNT" data-rules="required" class="form-control input-sm"></td>
	            <td class="required" i18n-content="成员姓名"></td>
	            <td><input type="text" name="NAME"   data-rules="required" id="NAME" class="form-control input-sm"></td>
	        </tr>
            <tr>
                <td class="required" i18n-content="手机号码"></td>
                <td><input type="text" name="PHONENUM" data-rules="required"  id="PHONENUM" class="form-control input-sm"></td>
                <td i18n-content="性别"></td>
                <td>
                    <select name="SEX"  id="SEX" class="form-control input-sm">
                        <option value='0' i18n-content="男"></option>
	                    <option value='1' i18n-content="女"></option>
                    </select>
                </td>
            </tr>
            <tr>
                <td i18n-content="职位"></td>
                <td><input type="text" name="POSITION"  id="POSITION" class="form-control input-sm"></td>
                <td i18n-content="出生日期"></td>
          	 	<td><input type="text" name="BIRTH_DATE"  id="BIRTH_DATE" class="form-control input-sm Wdate" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})"  style="width:155px"></td> 	     	
            </tr>
     		<tr>
        		<td i18n-content="状态"></td>
        		<td colspan="3">
            		<label class="radio-inline">
   						<input type="radio" value="0" checked="checked" name="STATUS"> <span i18n-content="启用"></span>
					</label>
					<label class="radio-inline">
   						<input type="radio" value="1"  name="STATUS"> <span i18n-content="未启用"></span>
					</label>
        		</td>
   			</tr>
            <tr>
                <td i18n-content="备注"></td>
                <td colspan="3">
                   <textarea class="form-control input-sm" name="BAKUP" id="BAKUP" rows="3"  maxlength="100" onchange="this.value=this.value.substring(0, 100)" onkeydown="this.value=this.value.substring(0, 100)" onkeyup="this.value=this.value.substring(0, 100)"></textarea>
                </td>
            </tr>
   		</tbody>
   	</table>
	<div class="layer-foot text-c">
		<button class="btn btn-sm btn-primary"  type="button" id="submit-form" onclick="wdate.save()" i18n-content="保存"></button>
		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="layer.closeAll();" i18n-content="关闭"></button>
	</div>
</form>				
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="/cc-sms/static/js/my_i18n.js?v=2020061"></script> 
	<script type="text/javascript" src="/cc-base/static/js/i18n.js?v=2"></script> 
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">
		jQuery.namespace("wdate");
	    wdate.mdId='${param.id}';
	    $(function(){
	    	$(function(){
	    		$("#easyform").render(); 
	    	});
			if (wdate.mdId  == "" || wdate.mdId  == null) {
			} else {
				getQuesData(wdate.mdId);
			}
		
		});
	    wdate.save = function(){
	    	var time=$("#BIRTH_DATE").val();
	    	var ago=$("#PHONENUM").val();
			var times=getTime();
			if(time>=times&&''!=time&&null!=time){
				layer.msg(getI18nValue("出生时间不能大于当前时间"), {icon : 2});
				return false;
			}
			 var myreg=/^[1][3,4,5,7,8][0-9]{9}$/;  
	          if (!myreg.test(ago)) {
	        	  layer.msg(getI18nValue("出生时间不能大于当前时间"), {icon : 2});
	              return false;  
	          }
	    	if (form.validate("easyform")){
				if(wdate.mdId==''){
					wdate.insertData(); 
				}else{
					wdate.updateData(); 
				}
			};
		}
	    wdate.insertData = function() {	    	
	  		var data = form.getJSONObject("easyform");
			ajax.remoteCall("/cc-sms/servlet/smsUserGroup?query=memberAdd",data,function(result) { 
				//debugger;
					if(result.state == 1){
						layer.closeAll();
						userGroup.searchData(); 
						}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
			);
		}
	    wdate.updateData = function(){
	 		var data = form.getJSONObject("easyform");
			data.MEMBER_ID= wdate.mdId;
			ajax.remoteCall("/cc-sms/servlet/smsUserGroup?query=memberUpdate",data,function(result) { 
				if(result.state == 1){
					layer.closeAll();
					userGroup.searchData(); 
					}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
	 	}
		function getQuesData(tId) {
			var data = {};
			data.id = tId;
			ajax.remoteCall("/cc-sms/servlet/smsUserGroup?query=memberInfo", data, function(result) {
				if (result.state == 1) {
					var ret = result.data;
					$("#ACCOUNT").val(ret.ACCOUNT);
					$("#ACCOUNT1").val(ret.ACCOUNT);
					$("#NAME").val(ret.NAME);
					$("#PHONENUM").val(ret.PHONENUM);
					$("#PHONENUM1").val(ret.PHONENUM);
					$("#POSITION").val(ret.POSITION);
					$("#BIRTH_DATE").val(ret.BIRTH_DATE);
					initradio('STATUS',ret.STATUS);
					$("#BAKUP").html(ret.BAKUP);
					$("#SEX option[value='" + ret.SEX + "']").attr("selected", "selected");
				} else {
					layer.alert(result.msg, {icon : 5});
				}
			});
		}
		function initradio(rName,rValue){
			var rObj = document.getElementsByName(rName);
			for(var i = 0;i < rObj.length;i++){
			    if(rObj[i].value == rValue){
			        rObj[i].checked =  'checked';
			    }
			}
	    }
		
		function getTime(){
			var date= new Date();
			var day = date.getDate() < 10 ? "0" + date.getDate():date.getDate();
			var month = (date.getMonth() + 1) > 9 ? (date.getMonth() + 1) : "0"	+ (date.getMonth() + 1);
			var miao = date.getSeconds();
			var hour = date.getHours() < 10 ? "0" + date.getHours():date.getHours();
			var min = date.getMinutes() < 10 ? "0" + date.getMinutes():date.getMinutes();
			var miao = date.getSeconds() < 10 ? "0" + date.getSeconds():date.getSeconds();
			var now= date.getFullYear() + '-' + month + '-' + day;
			return now;
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>