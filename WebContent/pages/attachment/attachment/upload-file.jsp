<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
		<div class="container-fluid pt-15">
			<form id="fileForm" enctype="multipart/form-data"  method="post">
					<div id="msg" style="margin: 0px 0px 8px;color: #d93a3a;padd:5px 10px;font-size: 13px;word-break:break-word"></div>
					<input id="localfile" name="localfile" class="filestyle" data-icon="true" data-buttonText="请选择文件"  data-size="sm"	type="file"  accept=""  multiple/>
					<div class="layer-foot text-c">
					   		<input class="btn btn-sm btn-primary" id="fileUploadSaveBtn" onclick="easyUploadFile()" type="button" value="上传">
					   		<input class="btn btn-sm btn-default ml-20"  type="button" id="backbut" onclick="popup.layerClose(this);" value="关闭">
				   </div>
			</form>
		</div>
<script type="text/javascript" src="/easitline-static/lib/bootstrap/bootstrap-filestyle/bootstrap-filestyle.min.js"></script>
<script type="text/javascript">
        var path="";
		var callbackFn=uploadSetting.callback;
		$(function(){
			if(callbackFn&&$.isFunction(callbackFn)){
				$("#fileForm #msg").html('${message}');
			}else{
				layer.alert("必须填写正确的回调方法！",{icon:7},function(index){
					layer.close(index);
					popup.layerClose("#fileForm");
					return;
				});
			}
		});
		function easyUploadFile(){
			var fileList = document.getElementById("localfile").files;
			if(fileList.length<=0){
		    	alert("请选择上传的文件!")
		    	return;
			}
			for(var i=0;i<fileList.length;i++){
				var fileObj=fileList[i];
				if(!checkfile(fileObj)){
					return;
				}
			}
			var formData = new FormData($("#fileForm")[0]);
			console.log(formData.keys())
			var data=$.extend({},uploadSetting);
			var paramStr=jQuery.param(data);
			$.ajax({  
		          url: '/attachment/servlet/attachment?action=upload2&'+paramStr,  
		          type: 'POST',  
		          data: formData,async: false,cache: false,contentType: false,processData: false,  
		          success: function (result) {
				    	 layer.msg(result.msg,{time:1500,offset:'rb',icon:1},function(){
				    		 layer.closeAll('dialog');
				    		 path=result.data.idUrl;
				    		 $('#uploadPath').val(path);
				    		 LabelEdit.uploadData();
					    	 if(result.state  == 1){
					    		 popup.layerClose("#fileForm");
							 	 window[callbackFn](result.data,uploadSetting.args);
					    	 }
				    	 });
		          },error: function (returndata) {  
		        	 	 alert("上传失败!："+fileType);
			             layer.msg('上传失败!'); 
			             layer.closeAll('dialog');
		          },beforeSend:function(){
		        	     layer.msg('正在上传', {icon: 16,time:1000*100,offset:'30px'});
		        	 	 $("#fileForm #backbut,#fileUploadSaveBtn").attr("disabled", true);	
		          },complete:function(){
		        	  alert("上传成功!");
		        	  console.log(layer.index);
		        	  layer.close(layer.index-2);
				      $("#fileForm #backbut,#fileUploadSaveBtn").attr("disabled", false);
		          }  
		     }); 
		}
		
	function checkfile(fileObj){
		//校验文件类型
		var filename=fileObj.name.toLowerCase(); 
		var fileType=uploadSetting.fileType;
		if(fileType!=null&&fileType!=undefined&&fileType!=''){
			var fileExtension = filename.substring(filename.lastIndexOf('.') + 1);
			fileExtension=fileExtension.toLowerCase();
			if(fileType.indexOf(fileExtension)==-1){
				alert("上传仅支持格式："+fileType);
				return false;
			}
		}
		//校验文件大小
		var fileMaxSize=uploadSetting.fileMaxSize;
		if(fileMaxSize!=null&&fileMaxSize!=undefined&&fileMaxSize!=''){
			return checkFileSize(fileObj,fileMaxSize);
		}
		return true;
	}
		
	var isIE = /msie/i.test(navigator.userAgent) && !window.opera; 
	function checkFileSize(target,fileMaxSize) { 
		var fileSize = 0; 
		if (isIE && !target.files) { 
			target.select(); target.blur();
			var filePath = document.selection.createRange().text;
			var fileSystem = new ActiveXObject("Scripting.FileSystemObject"); 
			if(!fileSystem.FileExists(filePath)){ 
				alert("附件不存在，请重新输入！"); 
				return false; 
			} 
			var file = fileSystem.GetFile(filePath); 
			fileSize = file.Size; 
		} else { 
			fileSize = target.size; 
		}  
		var size = fileSize / 1024; 
		if(size>fileMaxSize){ 
			if(fileMaxSize>=1024){
				alert("文件大小限制"+(fileMaxSize/1024).toFixed(2)+"M内！"); 
			}else{
				alert("文件大小限制"+fileMaxSize+"k内！"); 
			}
			target.value =""; 
			return false; 
		} 
		return true;
	} 
</script>
