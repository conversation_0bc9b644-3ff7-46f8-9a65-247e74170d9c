<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title><span i18n-title="城市管理" ></span></title>
	<style type="text/css">
		a:link{ color:#00adff;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form action="" method="post" name="searchForm" class="form-inline"
		id="searchForm" onsubmit="return false" data-toggle="">
		<div class="ibox">
			<div class="ibox-title clearfix" id="divId">
				<div class="form-group">
					<h5><span i18n-content="城市管理" ></span></h5>
					<div class="input-group input-group-sm">
						<span class="input-group-addon" i18n-content="关键字"></span> <input type="text" name="KEY_WORD" autocomplete="off" i18n-placeholder="请输入城市编码/城市名称" class="form-control input-sm" maxlength="300" style="width: 200px">
					</div>
					<div class="input-group input-group-sm">
						<span class="input-group-addon" i18n-content="省份"></span> <select id="province" name="province" class="form-control input-sm" data-mars="area.provinceChooseSel">
							<option value="" i18n-content="请选择"></option>
						</select>
					</div> 
					<div class="input-group input-group-sm">
						<button type="button" class="btn btn-sm btn-default" onclick="area.searchData('1')"><span class="glyphicon glyphicon-search" i18n-content="查询"></span></button>
					</div>
					<div class="input-group ">
						<button type="button" class="btn btn-sm btn-default" onclick="area.reset()"><span class="glyphicon glyphicon-repeat" i18n-content="重置"></span></button>
					</div>
					<div class="input-group input-group-sm pull-right btn-group ">
						<button  id="area_min_add"  type="button" class="btn btn-sm btn-success btn-outline" onclick="area.addarea()" i18n-content="新增"></button>
						<button id="area_min_delete" type="button" class="btn btn-sm btn-danger btn-outline" onclick="area.dels()" i18n-content="删除"></button>
						<button  id="area_min_input"  type="button" class="btn btn-sm btn-info btn-outline" onclick="area.importData()" i18n-content="导入"></button>
						<button  id="area_min_output"  type="button" class="btn btn-sm btn-info btn-outline" onclick="area.downloadExl()" i18n-content="导出"></button>
						<button  id="provice_management"  type="button" class="btn btn-sm btn-info btn-outline" onclick="area.province()" i18n-content="省份管理"></button>
					</div>
				</div>
			</div>
			<div class="ibox-content">
				<table id="main"></table>
			</div>
		</div>
	</form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		jQuery.namespace("area");
		
		 requreLib.setplugs("layui", function() {
			$("#searchForm").render({
				success : function() {
					requreLib.setplugs('multiselect',function(){
						$("#province").multiselect({
							 enableFiltering: true,
							 maxHeight: 400,
							 includeSelectAllOption: true,
							 selectAllText:getI18nValue('全选'),
							 nonSelectedText: getI18nValue('请选择')
						});
					});
					area.initData();
				}
			});
		}); 
		
		//初始化加载数据
		area.initData = function(){
			$("#searchForm").initTableEx({
				 mars:'area.list',
				 height: 'full-160',
				 id:'main',
				 limit:'15',
				 limits:[15,25,50,100,200],
				 cols: [
					[
					  {width:90,field:'AREA_CODE',align:'center', title: getI18nValue('选择'), type:'checkbox'}
			         ,{width:90,title: getI18nValue('序号'), align:'center',type:'numbers'}
			         ,{minWidth:180,field:'AREA_CODE',align:'center', title: getI18nValue('城市编码')}
			         ,{minWidth:120,field:'AREA_NAME',align:'center', title: getI18nValue('城市名称')}
			         ,{minWidth:180,field:'ADMINISTRATIVE_CODE',align:'center', title: getI18nValue('行政编码')}
			         ,{minWidth:180,field:'PROVINCE_CODE',align:'center', title: getI18nValue('省份编码')}
			         ,{minWidth:120,field:'PROVINCE_NAME',align:'center', title: getI18nValue('省份名称')}
			         ,{minWidth:100,field:'ID', title: getI18nValue('操作'),align:'center', templet:function(row){
			        	 	var val = row.AREA_CODE;
			        		var rq="";
							rq = " <a href='javascript:area.editarea(\""+val+"\");'>"+getI18nValue('修改')+"</a>&nbsp;"
							rq = rq+"<a href='javascript:area.del(\""+val+"\");'>"+getI18nValue('删除')+"</a>&nbsp;"
							rq = rq+"<a href='javascript:area.county(\""+row.ADMINISTRATIVE_CODE+"\",\""+row.AREA_NAME+"\");'>"+getI18nValue('区县管理')+"</a>"
							return rq;
			         }}
			         ]
				]
			});
		}
		
		//重置
		area.reset=function(){
			$("#divId select").val("");
	    	$("#divId input").val("");
	    	$("#province").multiselect("destroy");
			$("#searchForm").render({success:function(){
				requreLib.setplugs('multiselect',function(){
					$("#province").multiselect({
						 enableFiltering: true,
						 maxHeight: 400,
						 includeSelectAllOption: true,
						 selectAllText:'全选',
						 nonSelectedText: '请选择'
					});
				});
        	}});
		};
		
		//查询
		area.searchData = function(flag) {
			if(flag=='1'){
				$("#searchForm").queryData({id:'main',page:{ curr: 1}});
			}else{
				$("#searchForm").queryData({id:'main'});
			}
		}
		
		//新增
		area.addarea = function() {
			popup.layerShow({
				type : 1,
				title : getI18nValue("新增城市"),
				offset : '100px',
				area : [ '450px', '350px' ],
				shadeClose:false
			},"${ctxPath}/pages/area/area-edit.jsp",null);
		}

		//修改
		area.editarea = function(areacode) {
			popup.layerShow({
				type : 1,
				title : getI18nValue("修改城市"),
				offset : '100px',
				area : [ '450px', '350px' ],
				shadeClose:false
			},"${ctxPath}/pages/area/area-edit.jsp",{areacode:areacode});
		}
		area.county = function(administrativeCode, areaName) {
			popup.openTab({url:"/cc-base/pages/county/county.jsp",title:getI18nValue("区县管理"),id:"county",data:{administrativeCode:administrativeCode,areaName:areaName}});
		}
		
		//批量删除
		area.dels=function(){
			var checkStatus = table.checkStatus('main');
			if(checkStatus.data.length==0){
				layer.msg(getI18nValue("请选择需要删除的行！"));
				return ;
			}
			layer.confirm(getI18nValue('确认要删除吗？'), {
				title:getI18nValue('提示'),
	            btn : [ getI18nValue('确定'), getI18nValue('取消') ]
	        }, function(index) {
	        	var arr=new Array();
				for(var i=0;i<checkStatus.data.length;i++){
					arr.push(checkStatus.data[i].AREA_CODE);
				}
				area.deleteData(arr);
	        });
		}
		
		//删除
		area.del=function(id){
			layer.confirm(getI18nValue('确认要删除吗？'), {
				title:getI18nValue('提示'),
	            btn : [ getI18nValue('确定'), getI18nValue('取消') ]
	        }, function(index) {
	        	var arr=new Array();
				arr.push(id);
				area.deleteData(arr);
	        });
		}
		area.deleteData = function(arr){
			var data={};
			data.ids=arr;
			ajax.remoteCall("${ctxPath}/servlet/area?action=delete",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon: 1});
					area.searchData();

				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		//导出
		area.downloadExl=function(){
			 location.href ="${ctxPath}/servlet/area?action=export&"+$("#searchForm").serialize();
		}
		
		//导入
		area.importData = function() {
	    	popup.layerShow({type : 2,title : getI18nValue("城市导入"),offset : '20px',area : [ '420px', '150px' ]}, "${ctxPath}/pages/area/perform-import.jsp",null);
		}
		
		//省份管理
		area.province =function(){
			popup.openTab({url:"/cc-base/pages/province/province.jsp",title:getI18nValue("省份管理"),id:"province"});

		}
</script>
	<script type="text/javascript">
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>