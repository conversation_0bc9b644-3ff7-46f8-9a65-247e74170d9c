<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="质检对象维护"></title>
	<style>
		.gray-bg .layui-layer-tips .layui-layer-content{
			padding-right: 0px !important;
    		padding: 0px 0px;
		}
		.gray-bg .layui-layer-tips .layui-layer-setwin {
    		right: -7px;
    		top: -7px;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form action="" method="post" name="searchForm" class="form-inline" id="searchForm" onsubmit="return false;" autocomplete="off">
                <input type="hidden" name="groupId" value="${param.groupId}">
                <input type="hidden" name="examAgentIds" id="examAgentIds">
             	<div class="ibox">
             		<div class="ibox-title clearfix" id="resetid">
             		  	<div class="form-group">
							<h5 i18n-content="质检对象维护"></h5>
						   	<div class="input-group input-group-sm pull-right btn-group">
								<button type="button" class="btn btn-sm btn-success btn-outline" onclick="GroupAgent.addGroupGroupAgent('${param.groupId}')"> +<span i18n-content="添加质检对象"></span></button>
								<button type="button" class="btn btn-sm btn-warning btn-outline" onclick="GroupAgent.batchDel()"><span class="glyphicon glyphicon-minus-sign"></span> <span i18n-content="批量移除"></span></button>
							</div>
				      </div>
				      <hr style="margin: 5px -15px">
						 <div class="form-group">             		    
             		          <div class="input-group input-group-sm">
								      <span class="input-group-addon" i18n-content="坐席账号"></span>	
									  <input type="text" name="agentPhone" class="form-control input-sm" style="width:140px">
							   </div>
							      <div class="input-group input-group-sm">
								     <span class="input-group-addon" i18n-content="角色"></span>	
							   		 <select class="form-control input-sm" data-mars="QcCommonDao.roleDict" name="roleId">
							   		 	 <option value="" i18n-content="请选择"></option>
			    					 </select>
							   </div>
						
							   <div class="input-group input-group-sm">
										<button type="button" class="btn btn-sm btn-default" onclick="GroupAgent.loadData()"><span class="glyphicon glyphicon-search"></span> <span i18n-content="查询"></span></button>
								</div>
								<div class="input-group input-group-sm">
									<button type="button" class="btn btn-sm btn-default" onclick="repeat()">
									 <span class="glyphicon glyphicon-repeat"></span> <span i18n-content="重置"></span></button>
								</div>
						  </div>
             	    </div>  
	           <div class="ibox-content">
			<table id="dataList"></table>
				</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		jQuery.namespace("GroupAgent");
		GroupAgent.entId = '${param.entId}';
		GroupAgent.groupId = '${param.groupId}';
		GroupAgent.operate = '${param.operate}';
		GroupAgent.channelType = '${param.channelType}';
		
		function layuiRender(){
			//进度条 加载 element 模块
			layui.use(['element','layer', 'form'], function(){
				var laylayer = layui.layer;
				$('.layui-table-grid-down').click(function(){
					var text = $(this).prev(".layui-table-cell").text();
					layer.tips('<span style="color: #423e3e;">' + text + '</span>',
		  					$(this).get(0), {
						tips: [1, '#ffffff'],
						time: 0,
						area: ['280px', 'auto'],
						closeBtn: 2
					});
				});
				$('.groups').each(function(index,ele){
					var text = '';
					if($(ele).find(".layui-table-cell").length > 0){
						text = $(ele).find(".layui-table-cell").text();
						if(text){
							$(ele).hover(
							    function(){
							    	$(this).find(".layui-table-grid-down").show();
							    }
							    ,function(){
							    	 $(this).find(".layui-table-grid-down").hide();
							    }
							);
						}
					}
				});
			});
		}

		$(function(){
			$("#allAgentIds").click(function(){
				$("input[name='examAgentIds']").prop("checked", $(this).is(':checked'));
			});
			$("#searchForm").render({success:function(){
				//GroupAgent.addGroupGroupAgent('${param.groupId}');
				if(GroupAgent.operate=='add'){
					GroupAgent.addGroupGroupAgent(GroupAgent.groupId);
				}
				GroupAgent.loadData();
				layuiRender();
			}});
		});
		//初始化加载数据
		GroupAgent.loadData = function(){
			$("#searchForm").initTableEx({
				mars:"QcGroupDao.groupAgentList",
				id:"dataList",
				limit:'15',
				height: 'full-165',
				limits:[15,25,50,100,200],
				cols: [
					[
						{width:60,type:'checkbox',fixed: 'left'},
						{width:80, title: '序号',type:'numbers',fixed: 'left'},
						{field:'AGENT_NAME', width:120,title: getI18nValue('坐席姓名'),fixed: 'left'},
						{field:'USER_ACCT', title:getI18nValue('坐席账号'), width:120, align:'left'},
						/* {field:'REAL_USER_NAME', width:120,title: getI18nValue('用户姓名'),align: 'left'},
						{field:'REAL_USER_ACCT',title:getI18nValue('用户账号'), width:120, align:'left'}, */
						{field:'ROLE_LIST',title:getI18nValue('角色'), minWidth:120, align:'left'},
						{field:'GROUP_LIST', title: getI18nValue('技能组'), minWidth:120, align:'left'},
				        {field:'ADD_TIME',title:getI18nValue('添加时间'), width:180, align:'center'},
				        {field:'ADDRESS',title:getI18nValue('地址'), minWidth:160, align:'left'},	
				        {field:'ENABLE_STATUS',title:getI18nValue('启用状态'), width:100, align:'center',templet : function(row){
							var check = "";
							if(row.ENABLE_STATUS=="01"){
								check='checked="checked"';
							}
							var html='';
							html='<input type="checkbox" '+check+' lay-skin="switch" lay-filter="ckStateUser" lay-text="'+getI18nValue("启用|禁用")+'" data-id="'+row.EXAM_AGENT_ID+'">';
							return html;
			       		}},
						{title: getI18nValue('操作'), width:100, align:'center',templet:function(row){
				        	var temp ='';
				        	 temp = '<span class="layui-btn layui-btn-xs" onclick="GroupAgent.delData(\''+row.EXAM_AGENT_ID+'\')">'+getI18nValue("移除")+'</span> '
				        	 return temp;
				         }}
					]
				],
				done:function(res,curr,count){
					/* var data = res;
					for (var i = 0; i < res.data.length; i++) {
						if (res.data[i].USER_ACCT != res.data[i].REAL_USER_ACCT) {
						    $("table tbody tr").eq(i).css('background-color', 'rgb(239, 181, 181)')
						    $(".layui-table-fixed .layui-table-body table tbody tr").eq(i).css('background-color', 'rgb(239, 181, 181)')
		                }
					} */
					var form = layui.form;
					form.render('checkbox');
			        form.on('switch(ckStateUser)', function (obj) {
			        	var id = this.dataset.id;
			            var status =this.checked ? '01' : '02';
			            updateEnable(id,status);
			        });
				}
			});
		}
		GroupAgent.searchData=function(){
			$("#searchForm").searchData();
		}
		GroupAgent.addGroupGroupAgent = function(groupId){
			popup.layerShow({type:2,title:getI18nValue('添加质检对象'),offset:'20px',area:['807px','657px']},"${ctxPath}/pages/group/qc-group-select-user.jsp",{groupId:groupId,channelType:GroupAgent.channelType,showType:"agent"});
		}
		
		GroupAgent.delData=function(groupAgentId){
	  		ajax.remoteCall("${ctxPath}/servlet/qcGroup?action=deleteGroupAgent", {groupAgentId:groupAgentId,examGroupId:GroupAgent.groupId}, function(result) {
	  			if(result.state == 1){
				    layer.msg(result.msg,{icon: 1,time:1200,offset:'40px'},function(){
				    	GroupAgent.loadData();
				    });
				}else{
					layer.alert(result.msg,{icon: 5});
				}
  			});
		}
		GroupAgent.batchDel=function(GroupAgentNum){
			var checkStatus = table.checkStatus('dataList')
			   ,datas = checkStatus.data;
				if(!datas.length>0||datas==null){
					layer.msg(getI18nValue("请选择要修改的质检对象"),{icon:0});
					return;
				}		
				var ids=[];	
				for(var i=0;i<datas.length;i++){		
					ids.push(datas[i].EXAM_AGENT_ID);
				}							
				$("#examAgentIds").val(ids);	
			var data = form.getJSONObject("#searchForm");
			data.examGroupId = GroupAgent.groupId;
	  		ajax.remoteCall("${ctxPath}/servlet/qcGroup?action=deleteBatchAgent", data, function(result) {
	  			if(result.state == 1){
				    layer.msg(result.msg,{icon: 1,time:1200,offset:'40px'},function(){
				    	GroupAgent.loadData();
				    	GroupAgent.initCheckAll();
				    });
				}else{
					layer.alert(result.msg,{icon: 5});
				}
  			});
		}

		function selectUser(userIds,userNames){
			var data = {userIds:userIds,groupId:GroupAgent.groupId};
	  		ajax.remoteCall("${ctxPath}/servlet/qcGroup?action=addGroupAgent", data, function(result) {
	  			if(result.state == 1){
				    layer.msg(result.msg,{icon: 1,time:1200,offset:'40px'},function(){
				    	GroupAgent.loadData();
				    });
				}else{
					layer.alert(result.msg,{icon: 5});
				}
  			});
		}
	
		GroupAgent.initCheckAll = function(){
			$("#allAgentIds").attr('checked',false); 
		}
		
		GroupAgent.clickCheck = function(obj){
			if(!$(obj).is(':checked')){
				GroupAgent.initCheckAll();
			}
		}
		function repeat() {
	 		$("#resetid input").val("");
	 		$("#resetid select").val("");
	 	}
		
		//控制人员的启用状态
		function updateEnable(id,status){
			ajax.remoteCall("${ctxPath}/servlet/qcGroup?action=updateAgentEnable", {id:id,status:status,type:"group"}, function(result) {
		  		if(result.state == 1){
				    layer.msg(result.msg,{icon: 1,time:1200,offset:'40px'},function(){
				    	GroupAgent.loadData();
				    });
				}else{
					layer.alert(result.msg,{icon: 5});
				}
	  		});
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>