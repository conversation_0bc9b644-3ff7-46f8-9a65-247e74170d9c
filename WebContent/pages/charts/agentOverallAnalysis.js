	/**
	 * 坐席整体质检分析js
	 */
	if(typeof qualityAdmin == "undefined"){
		jQuery.namespace("qualityAdmin");
	}
	jQuery.namespace("agentOverallAnalysisJs");
	
	agentOverallAnalysisJs.chartsResize = function(){
		if(agentOverallAnalysisJs.qcQuantityAnalysisMap && agentOverallAnalysisJs.qcQuantityAnalysisMap.resize){
			agentOverallAnalysisJs.qcQuantityAnalysisMap.resize();
		}
		if(agentOverallAnalysisJs.agentCallAnalysisMap && agentOverallAnalysisJs.agentCallAnalysisMap.resize){
			agentOverallAnalysisJs.agentCallAnalysisMap.resize();
		}
		if(agentOverallAnalysisJs.agentMediaAnalysisMap && agentOverallAnalysisJs.agentMediaAnalysisMap.resize){
			agentOverallAnalysisJs.agentMediaAnalysisMap.resize();
		}
		if(agentOverallAnalysisJs.callAvgScoreAnalysisMap && agentOverallAnalysisJs.callAvgScoreAnalysisMap.resize){
			agentOverallAnalysisJs.callAvgScoreAnalysisMap.resize();
		}
		if(agentOverallAnalysisJs.mediaAvgScoreAnalysisMap && agentOverallAnalysisJs.mediaAvgScoreAnalysisMap.resize){
			agentOverallAnalysisJs.mediaAvgScoreAnalysisMap.resize();
		}
		if(agentOverallAnalysisJs.oneVoteItemAnalysisMap && agentOverallAnalysisJs.oneVoteItemAnalysisMap.resize){
			agentOverallAnalysisJs.oneVoteItemAnalysisMap.resize();
		}
	}
	
	//坐席整体质检分析
	agentOverallAnalysisJs.agentOverallAnalysis = function () {
		var beginStartDate = $("#beginStartDate").val();
		var endStartDate = $("#endStartDate").val();
		if(!beginStartDate || !endStartDate){
			layer.msg(getI18nValue("请选择时间范围"), {icon:5});
			return false;
		}
		var days = calcBwDays(beginStartDate, endStartDate);
		if(!(days >= 0 && days <= 31)){
			layer.msg(getI18nValue("请选择正确的时间范围，时间不能大于31天"), {icon:5});
			return false;
		}
		var agentDept = "";
		if(qualityAdmin.agentDeptTree){
			agentDept = qualityAdmin.agentDeptTree.getValue("valueStr") || "";
		}
		var taskId = "";
		if(qualityAdmin.taskTree){
			taskId = qualityAdmin.taskTree.getValue() || "";
			if(taskId && taskId.indexOf(",") > -1){
				var arr = taskId.split(",");
				if(arr.length >= 2){
					taskId = arr[1] || "";
				}
			}
		}
		ajax.daoCall({"params":{beginStartDate:beginStartDate,endStartDate:endStartDate,deptCode:agentDept,taskId:taskId},"controls":[
    		"charts.qcAgentDataOverview","charts.agentExtractAnalysis"
    		, "charts.agentAvgScoreAnalysis", "charts.agentOneVoteItemAnalysis"]}, function (result){
    		//质检数据总览
    		var qcAgentDataOverviewData = result["charts.qcAgentDataOverview"] || {};
    		agentOverallAnalysisJs.qcDataOverView(qcAgentDataOverviewData);
    		
    		//坐席抽检分析
    		var agentExtractAnalysisData = result["charts.agentExtractAnalysis"] || {};
    		var callAnalysis = agentExtractAnalysisData && agentExtractAnalysisData.callAnalysis ? agentExtractAnalysisData.callAnalysis : [];
    		var mediaAnalysis = agentExtractAnalysisData && agentExtractAnalysisData.mediaAnalysis ? agentExtractAnalysisData.mediaAnalysis : [];
    		//语音抽检分析
    		agentOverallAnalysisJs.agentCallAnalysisView(callAnalysis);
    		//全媒体抽检分析
    		agentOverallAnalysisJs.agentMediaAnalysisView(mediaAnalysis);
    		
    		//平均分分析
    		var agentAvgScoreAnalysisData = result["charts.agentAvgScoreAnalysis"] || {};
    		var callAvgScoreAnalysis = agentAvgScoreAnalysisData && agentAvgScoreAnalysisData.callAvgScoreAnalysis ? agentAvgScoreAnalysisData.callAvgScoreAnalysis : [];
    		var mediaAvgScoreAnalysis = agentAvgScoreAnalysisData && agentAvgScoreAnalysisData.mediaAvgScoreAnalysis ? agentAvgScoreAnalysisData.mediaAvgScoreAnalysis : [];
    		//语音平均分分析
    		agentOverallAnalysisJs.agentCallAvgScoreAnalysisView(callAvgScoreAnalysis);
    		//全媒体平均分分析
    		agentOverallAnalysisJs.agentMediaAvgScoreAnalysisView(mediaAvgScoreAnalysis);
    		
    		//一票否决分析
    		var agentOneVoteItemAnalysisData = result["charts.agentOneVoteItemAnalysis"] || {};
    		var oneVoteItemAnalysis = agentOneVoteItemAnalysisData && agentOneVoteItemAnalysisData.oneVoteItemAnalysis ? agentOneVoteItemAnalysisData.oneVoteItemAnalysis : [];
    		agentOverallAnalysisJs.agentOneVoteItemAnalysisView(oneVoteItemAnalysis);
    	});
	}
	
	agentOverallAnalysisJs.qcDataOverView = function (data) {
		$("#agentAnalysis_qcAgentCount").html(data.QC_AGENT_COUNT || "0").parent().attr("title", data.QC_AGENT_COUNT || "");
		$("#agentAnalysis_vetoedAgentCount").html(data.VETOED_AGENT_COUNT || "0").parent().attr("title", data.QC_AGENT_COUNT || "");
		$("#agentAnalysis_vetoedCount").html(data.VETOED_COUNT || "0").parent().attr("title", data.VETOED_COUNT || "");
		$("#agentAnalysis_avgExtractCount").html(data.AVG_EXTRACT_COUNT || "0").parent().attr("title", data.AVG_EXTRACT_COUNT || "");
		$("#agentAnalysis_agentMaxExtract").html(data.AGENT_MAX_EXTRACT || "0").parent().attr("title", data.AGENT_MAX_EXTRACT || "");
		$("#agentAnalysis_agentNinExtract").html(data.AGENT_MIN_EXTRACT || "0").parent().attr("title", data.AGENT_MIN_EXTRACT || "");
		
		$("#agentAnalysis_avgRgCallScore").html(data.AVG_RG_CALL_4_SCORE || "0.00").parent().attr("title", data.AVG_RG_CALL_4_SCORE || "");
		$("#agentAnalysis_callAvgMaxScore").html(data.CALL_AVGMAX_SCORE || "0.00").parent().attr("title", data.CALL_AVGMAX_SCORE || "");
		$("#agentAnalysis_callAvgMinScore").html(data.CALL_AVGMIN_SCORE || "0.00").parent().attr("title", data.CALL_AVGMIN_SCORE || "");
		$("#agentAnalysis_avgRgMediaScore").html(data.AVG_RG_MEDIA_4_SCORE || "0.00").parent().attr("title", data.AVG_RG_MEDIA_4_SCORE || "");
		$("#agentAnalysis_mediaAvgMaxScore").html(data.MEDIA_AVGMAX_SCORE || "0.00").parent().attr("title", data.MEDIA_AVGMAX_SCORE || "");
		$("#agentAnalysis_mediaAvgMinScore").html(data.MEDIA_AVGMIN_SCORE || "0.00").parent().attr("title", data.MEDIA_AVGMIN_SCORE || "");
	}
	
	//语音抽检分析
	agentOverallAnalysisJs.agentCallAnalysisView = function (data) {
		var xAxisData = [];
		var seriesDatas = [];
		for(var item of data){
			xAxisData.push(item.name);
			seriesDatas.push(item);
		}
		option = {
				tooltip: {
			        trigger: 'axis',
			        axisPointer: {            // 坐标轴指示器，坐标轴触发有效
			            type: 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
			        }
			    },
			    legend: {
			        data: [getI18nValue('语音')]
			    },
			    grid: {
			        left: '3%',
			        right: '4%',
			        bottom: '3%',
			        containLabel: true
			    },
			    xAxis: {
			        type: 'category',
			        data: xAxisData
			    },
			    yAxis: {
			        type: 'value'
			    },
			    series: [{
			    	name: getI18nValue('语音'),
			        data: seriesDatas,
			        type: 'bar',
			        color: '#4A89EA',
			        showBackground: true,
			        backgroundStyle: {
			            color: 'rgba(220, 220, 220, 0.8)'
			        }
			    }]
			};
		agentOverallAnalysisJs.agentCallAnalysisMap = echarts.init(document.getElementById("agentOverallAnalysis_callAnalysis"));
		agentOverallAnalysisJs.agentCallAnalysisMap.setOption(option);
	}
	
	//全媒体抽检分析
	agentOverallAnalysisJs.agentMediaAnalysisView = function (data) {
		var xAxisData = [];
		var seriesDatas = [];
		for(var item of data){
			xAxisData.push(item.name);
			seriesDatas.push(item);
			
		}
		console.log(xAxisData)
		console.log(seriesDatas)
		option = {
				tooltip: {
			        trigger: 'axis',
			        axisPointer: {            // 坐标轴指示器，坐标轴触发有效
			            type: 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
			        }
			    },
			    legend: {
			        data: [getI18nValue('全媒体')]
			    },
			    grid: {
			        left: '3%',
			        right: '4%',
			        bottom: '3%',
			        containLabel: true
			    },
				xAxis: {
					type: 'category',
					data: xAxisData
				},
				yAxis: {
					type: 'value'
				},
				series: [{
					name: getI18nValue('全媒体'),
					data: seriesDatas,
					type: 'bar',
					color: '#293c55',
					showBackground: true,
					backgroundStyle: {
						color: 'rgba(220, 220, 220, 0.8)'
					}
				}]
		};
		agentOverallAnalysisJs.agentMediaAnalysisMap = echarts.init(document.getElementById("agentOverallAnalysis_mediaAnalysis"));
		agentOverallAnalysisJs.agentMediaAnalysisMap.setOption(option);
	}
	
	//语音平均分分析
	agentOverallAnalysisJs.agentCallAvgScoreAnalysisView = function (data) {
		var xAxisData = [];
		var seriesDatas = [];
		for(var item of data){
			xAxisData.push(item.name);
			seriesDatas.push(item);
		}
		option = {
				tooltip: {
			        trigger: 'axis',
			        axisPointer: {            // 坐标轴指示器，坐标轴触发有效
			            type: 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
			        }
			    },
			    legend: {
			        data: [getI18nValue('语音')]
			    },
			    grid: {
			        left: '3%',
			        right: '4%',
			        bottom: '3%',
			        containLabel: true
			    },
			    xAxis: {
			        type: 'category',
			        data: xAxisData
			    },
			    yAxis: {
			        type: 'value'
			    },
			    series: [{
			    	name: getI18nValue('语音'),
			        data: seriesDatas,
			        type: 'bar',
			        color: '#4A89EA',
			        showBackground: true,
			        backgroundStyle: {
			            color: 'rgba(220, 220, 220, 0.8)'
			        }
			    }]
			};
		agentOverallAnalysisJs.callAvgScoreAnalysisMap = echarts.init(document.getElementById("agentOverallAnalysis_callAvgScoreAnalysis"));
		agentOverallAnalysisJs.callAvgScoreAnalysisMap.setOption(option);
	}
	
	//全媒体平均分分析
	agentOverallAnalysisJs.agentMediaAvgScoreAnalysisView = function (data) {
		var xAxisData = [];
		var seriesDatas = [];
		for(var item of data){
			xAxisData.push(item.name);
			seriesDatas.push(item);
		}
		option = {
				tooltip: {
			        trigger: 'axis',
			        axisPointer: {            // 坐标轴指示器，坐标轴触发有效
			            type: 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
			        }
			    },
			    legend: {
			        data: [getI18nValue('全媒体')]
			    },
			    grid: {
			        left: '3%',
			        right: '4%',
			        bottom: '3%',
			        containLabel: true
			    },
				xAxis: {
					type: 'category',
					data: xAxisData
				},
				yAxis: {
					type: 'value'
				},
				series: [{
					name: getI18nValue('全媒体'),
					data: seriesDatas,
					type: 'bar',
					color: '#293c55',
					showBackground: true,
					backgroundStyle: {
						color: 'rgba(220, 220, 220, 0.8)'
					}
				}]
		};
		agentOverallAnalysisJs.mediaAvgScoreAnalysisMap = echarts.init(document.getElementById("agentOverallAnalysis_mediaAvgScoreAnalysis"));
		agentOverallAnalysisJs.mediaAvgScoreAnalysisMap.setOption(option);
	}
	
	//一票否决分析
	agentOverallAnalysisJs.agentOneVoteItemAnalysisView = function (data) {
		var xAxisData = [];
		var seriesDatas = [];
		for(var item of data){
			xAxisData.push(item.name);
			seriesDatas.push(item);
		}
		option = {
				tooltip: {
					trigger: 'axis',
					axisPointer: {            // 坐标轴指示器，坐标轴触发有效
						type: 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
					}
				},
				legend: {
					data: [getI18nValue('一票否决量')]
				},
				grid: {
					left: '3%',
					right: '4%',
					bottom: '3%',
					containLabel: true
				},
				xAxis: {
					type: 'category',
					data: xAxisData
				},
				yAxis: {
					type: 'value'
				},
				series: [{
					name: getI18nValue('一票否决量'),
					data: seriesDatas,
					type: 'bar',
					color: '#ff8547',
					showBackground: true,
					backgroundStyle: {
						color: 'rgba(220, 220, 220, 0.8)'
					}
				}]
		};
		agentOverallAnalysisJs.oneVoteItemAnalysisMap = echarts.init(document.getElementById("agentOverallAnalysis_oneVoteItemAnalysis"));
		agentOverallAnalysisJs.oneVoteItemAnalysisMap.setOption(option);
	}
	
	
	
	
	
	
	
	