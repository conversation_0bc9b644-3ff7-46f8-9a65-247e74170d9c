<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>公告管理</title>
	<link href="/cc-notes/static/lib/umeditor/themes/default/css/umeditor.css" type="text/css" rel="stylesheet">
	<script type="text/javascript" src="/cc-notes/static/lib/umeditor/third-party/jquery.min.js"></script>
	<script type="text/javascript" src="/cc-notes/static/lib/umeditor/third-party/template.min.js"></script>
	<script type="text/javascript" charset="utf-8" src="/cc-notes/static/lib/umeditor/umeditor.config.js"></script>
	<script type="text/javascript" charset="utf-8" src="/cc-notes/static/lib/umeditor/umeditor.min.js"></script>
	<script type="text/javascript" src="/cc-notes/static/lib/umeditor/lang/zh-cn/zh-cn.js"></script>
	
	<script src="/easitline-static/lib/umeditor/dialogs/link/link.js" type="text/javascript" defer="defer"></script>
	<script src="/easitline-static/lib/umeditor/dialogs/image/image.js" type="text/javascript" defer="defer"></script>
	<script src="/easitline-static/lib/umeditor/dialogs/video/video.js" type="text/javascript" defer="defer"></script>
	<script src="/easitline-static/lib/umeditor/dialogs/map/map.js" type="text/javascript" defer="defer"></script>
	<script src="/easitline-static/lib/umeditor/dialogs/formula/formula.js" type="text/javascript" defer="defer"></script>
	<script src="/easitline-static/lib/umeditor/dialogs/emotion/emotion.js" type="text/javascript" defer="defer"></script>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="editForm" data-mars="" data-pk="" method="post"  autocomplete="off" data-mars-prefix="" >
				  <table class="table  table-edit table-vzebra mt-10" >
	                    <tbody>
		                     <tr>
			                      <td class="required">收件人</td>
			                      <td colspan="3"><div class="input-group input-group-sm" style="width:100%">
		                             <input name="" readonly="readonly" class="form-control input-sm" type="text">
		                        	 <span class="input-group-addon" style="cursor: pointer;" onclick="">选择人员</span>
		                        	 <span class="input-group-addon" style="cursor: pointer;" onclick="">选择群组</span>
		                          </div></td>
		                     </tr>
		                     <tr>
			                      <td class="required">抄送</td>
			                      <td colspan="3"><div class="input-group input-group-sm"  style="width:100%">
		                             <input name="" readonly="readonly" class="form-control input-sm" type="text">
		                        	 <span class="input-group-addon" style="cursor: pointer;" onclick="">选择人员</span>
		                          </div></td>
		                     </tr>
		                     <tr>
			                      <td class="required">主题</td>
			                      <td colspan="3"><input name=""  class="form-control input-sm" type="text"></td>
			                 </tr>
			                  <tr>
			                      <td class="">发布时间</td>
			                      <td><input name=""  class="form-control input-sm" type="text"  onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',autoPickDate:true})"></td>
			                      <td class="">生效时间</td>
			                      <td><input name=""  class="form-control input-sm" type="text"  onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',autoPickDate:true})"></td>
			                 </tr>
		                     <tr>
			                       <td width="50px">公告类型</td>
			                       <td width="240px">
			                            <select name="" class="input-sm form-control">
			                                <option>普通公告</option>
			                            </select>
			                       </td>
			                       <td width="50px">选项</td>
			                       <td width="240px">
				                         <label class="checkbox-inline">
							                <input type="checkbox" value=""  name=""> 紧急公告
							             </label>
							             <label class="checkbox-inline">
							                <input type="checkbox" value=""  name=""> 重要公告
							             </label>
			                            <label class="checkbox-inline">
							                <input type="checkbox" value=""  name=""> 发布到网站
							             </label>
			                       </td>
		                     </tr>	
		                     <tr>
		                           <td>公告内容</td>
		                           <td colspan="3">
		                              <div id="myEditor" style="height:290px"></div>
		                           </td>
		                     </tr>
	                    </tbody>
	                  </table>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="">保存</button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" id="backbut" onclick="layer.closeAll();">关闭</button>
				    </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	    requreLib.setplugs('wdate');
	    
	    window.UMEDITOR_CONFIG.toolbar = [
	                                      'source | undo redo | bold italic underline strikethrough | superscript subscript | forecolor backcolor | removeformat |',
	                                      'insertorderedlist insertunorderedlist | selectall cleardoc paragraph | fontfamily fontsize' ,
	                                      '| justifyleft justifycenter justifyright justifyjustify ',
	                                      '| horizontal print preview fullscreen'
	                                  ];

	    var um = UM.getEditor('myEditor');
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>