<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>草稿箱</title>
	<link href="/cc-notes/static/lib/umeditor/themes/default/css/umeditor.css" type="text/css" rel="stylesheet">
	<script type="text/javascript" src="/cc-notes/static/lib/umeditor/third-party/template.min.js"></script>
	<script type="text/javascript" charset="utf-8" src="/cc-notes/static/lib/umeditor/umeditor.config.js"></script>
	<script type="text/javascript" charset="utf-8" src="/cc-notes/static/lib/umeditor/umeditor.min.js"></script>
	<script type="text/javascript" src="/cc-notes/static/lib/umeditor/lang/zh-cn/zh-cn.js"></script>
	<script src="/easitline-static/lib/umeditor/dialogs/link/link.js" type="text/javascript" defer="defer"></script>
	<script src="/easitline-static/lib/umeditor/dialogs/image/image.js" type="text/javascript" defer="defer"></script>
	<script src="/easitline-static/lib/umeditor/dialogs/video/video.js" type="text/javascript" defer="defer"></script>
	<script src="/easitline-static/lib/umeditor/dialogs/map/map.js" type="text/javascript" defer="defer"></script>
	<script src="/easitline-static/lib/umeditor/dialogs/formula/formula.js" type="text/javascript" defer="defer"></script>
	<script src="/easitline-static/lib/umeditor/dialogs/emotion/emotion.js" type="text/javascript" defer="defer"></script>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="editForm" data-mars="noticeInfo.getNoticeInfo" data-pk="${param.noticeId}" method="post"  autocomplete="off" data-mars-prefix="noticeInfo." >
	 	<input type="hidden" name="noticeInfo.NOTICE_ID" value="${param.noticeId}">		  
		<table class="table  table-vzebra mt-10" >
			<tbody>
                <tr>
                  <td width="60px">收件人：</td>
                  <td width="200px"><input type="text" disabled="disabled" class="form-control input-sm" name="noticeInfo.NOTICE_RECEIVER"/></td>
                  <td width="60px">抄送：</td>
                  <td width="200px"><input type="text" disabled="disabled" class="form-control input-sm" name="noticeInfo.COPY_PERSON"/></td>
                </tr>
                <tr>
                  <td>类型：</td>
                  <td>
		  			<select class="form-control input-sm" name="noticeInfo.TYPE_ID" disabled="disabled" data-mars="noticeType.noticeTypeDict">
                        <option value="" i18n-content="请选择"></option>
                        <!-- <option value="1">普通公告</option> -->
                    </select>
                  </td>
                  <td>发布时间：</td>
                  <td><input type="text" disabled="disabled" class="form-control input-sm" name="noticeInfo.PUBLISH_TIME"/></td>
             </tr>
             <tr>
                  <td>主题：</td>
                  <td colspan="3"><input type="text" disabled="disabled" class="form-control input-sm" name="noticeInfo.NOTICE_TITLE"/></td>
                </tr>
                <tr>
                     <td>内容：</td>
                  <td colspan="3" style="overflow:hidden;">
                      <div id="myEditor" name="noticeInfo.NOTICE_CONTENT" style="height:300px"></div>
                  </td>
                </tr>
                <tr>
                     <td>附件：</td>
                  <td colspan="3" style="overflow:hidden;">
                  	<button class="btn btn-sm btn-primary" type="button" onclick="maintenanceDetail.attachment();"><span class="glyphicon glyphicon-folder-close"></span> 附件管理</button>
                  </td>
                </tr>
              </tbody>
        </table>
        <div class="layer-foot text-c"></div>
	</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
		jQuery.namespace("maintenanceDetail");
	
		window.UMEDITOR_CONFIG.toolbar = [];
		
		var um = UM.getEditor('myEditor');
		um.setDisabled('fullscreen');
		$(function() {			
			$("#editForm").render({success:function(rder){
				var date = rder['noticeInfo.getNoticeInfo'];
			    var content = date.data.NOTICE_CONTENT;
				if(content){
					um.setContent(decodeURIComponent(content.replace(/\+/g, '%20')));
				}
				execI18n();
			}});
		});
		maintenanceDetail.attachment = function() {
	    	var busiId = '${param.noticeId}';
	    	var busiType = "01";
	    	var requestType= "download"
	    	popup.layerShow({
				type : 2,
				title : "文件管理",
				offset : '20px',
				shadeClose:false,
				area : [ '800px', '70%' ]
			}, "/cc-base/servlet/attachment?action=attachment", {busiId:busiId,requestType:requestType,busiType:busiType});
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>