<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page import = "com.yunqu.yc.quality.base.Constants" %>
<%
String runManual = Constants.RUN_MANUAL;
%>
<EasyTag:override name="head">
	<title i18n-content="结果修改"></title>
   		<style type="text/css">
		::-webkit-scrollbar {
			width: 8px;
			height: 8px;
			background: transparent;
		}
		::-webkit-scrollbar-track {
			background: transparent;
		}
		::-webkit-scrollbar-thumb {
			border-radius: 8px;
			background-color: #C1C1C1;
		}
		::-webkit-scrollbar-thumb:hover {
			background-color: #A8A8A8;
		}
		.shadow{
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-moz-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form class="form-inline" id="searchForm">
       	<input type="hidden" name="rgState" value="4">
           	<div class="ibox">
           		<div class="ibox-title clearfix">
           		  <div class="form-group">
           		   <h5 i18n-content="结果修改"></h5><br>
           		  </div>
           		   <hr style="margin: 5px -15px">
				    <div class="form-group" id="reset">
					  <div class="input-group input-group-sm const-width">
							<span class="input-group-addon required" i18n-content="渠道类型"></span> 
			               	<select name="channelType" class="form-control required" id="channelType" onchange="QcTaskRecord.channelChange(this)" data-mars="QcCommonDao.getDict('QC_CHANNEL_TYPE')" style="width: 130px;">
							</select>
					  </div>
					  <div class="input-group input-group-sm const-width">
							<span class="input-group-addon" i18n-content="任务名称"></span> 
	               			<select name="taskId" id="taskId" class="form-control" style="width: 130px;">
	               				<option value="" i18n-content="请选择"> </option>
							</select>
					 </div>
					  <div class="input-group input-group-sm oper-rate" id="agentId">
							<span class="input-group-addon" i18n-content="坐席"></span>	
							<input type="text" name="agentKey" id="agentKey" class="form-control input-sm" style="width:169px" i18n-placeholder="输入坐席账号、坐席姓名" value="">
					  </div>
					  <div class="input-group input-group-sm oper-rate" id="inspectorId">
							<span class="input-group-addon" i18n-content="质检员"></span>	
							<input type="text" name="inspectorKey" id="inspectorKey" class="form-control input-sm"  style="width:169px" i18n-placeholder="输入质检员账号,姓名" value="">
					  </div>
					  
					  <div class="input-group">
			 			<span class="input-group-addon" i18n-content="人工质检时间"></span>
			        	<input type="text" class="form-control input-sm" id="beginQcTime" name="beginQcTime" autocomplete="off" style="height:30px;width:142px" > 
	                	<span class="input-group-addon">~</span>
	                	<input type="text" class="form-control input-sm" id="endQcTime" name="endQcTime" autocomplete="off" style="height:30px;width:142px" > 
	                	<span class="input-group-addon">-</span>
	                	<select class="form-control input-sm" name="dateRange" id="dateRange" onchange="onCasecadeTime($(this), 'beginQcTime', 'endQcTime')">
	                   		<option value="" i18n-content="请选择"></option>
							<option value="today" i18n-content="今天"></option>
							<option value="yesterday" i18n-content="昨天"></option>
							<option value="thisWeek" i18n-content="本周"></option>
							<option value="RecentlyOneMonth" i18n-content="近一个月"></option>
							<option value="RecentlyThreeMonth" i18n-content="近三个月"></option>
						</select>
                	  </div>
					  
					  <div class="input-group input-group-sm hide">
	               			<select name="label" id="label" class="form-control" data-mars="QcLabelDao.labelTreeCodeDict" style="width: 130px;">
	               				<option value="" ></option>
							</select>
					 </div>
					  
					  <div class="input-group input-group-sm">
							<button type="button" class="btn btn-sm btn-default" id="btnSearch" onclick="QcTaskRecord.loadData()" i18n-content="查询"></button>
					  </div>
					  <div class="input-group ">
							<button type="button" class="btn btn-sm btn-default" onclick="toggleReset()" i18n-content="重置"></button>
					  </div>
				  </div>
				  <div id="params_div">

                  </div>
           	    </div>  
             	 <div class="ibox-content">
              		<table id="main" lay-size="sm">
              	    </table>
              	</div>  
        	</div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
<script src="${ctxPath}/static/lib/wavesurfer/wavesurfer.js"></script>
<script src="${ctxPath}/static/lib/wavesurfer/wavesurfer.timeline.min.js"></script>
<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/cc-quality/static/js/time.js"></script>
		
	<script type="text/javascript">
		jQuery.namespace("QcTaskRecord");
		//配置项，人工质检提交后允许修改时间
		var allowEditTime = '<%=Constants.getAllowEditTime()%>';
		$(function(){
			if(QcTaskRecord.isadmin == "" && QcTaskRecord.state == 1){
				$(".extractDiv").css('display', "");
			}
			$("#dateRange").val("thisWeek").trigger("change");
			$("#searchForm").render({success:function(result){
				$("#channelType").trigger("change");
				layui.use('laydate', function(){
					  var laydate = layui.laydate;
					  laydate.render({
                          elem: '#beginQcTime',
                          range: false,
                          type: 'datetime',
                          btns: ['confirm'],
                          lang:getDateLang()
                      });
                      laydate.render({
                          elem: '#endQcTime',
                          range: false,
                          type: 'datetime',
                          btns: ['confirm'],
                          lang:getDateLang()
                      });
				});
				QcTaskRecord.loadData();
			}});
		});
		var QcTaskRecord={
				channelChange : function (elem) {
					var mars = "QcCommonDao.getTaskByChannel(" + $(elem).val() + ")";
					$("#taskId").data("mars", mars).render({success:function(){
						$("#btnSearch").trigger("click");
					}});
				},
				examGroupId:'',
				loadData:function(data){
					var beginStartDate = $("#beginQcTime").val();
					var endStartDate = $("#endQcTime").val();
					if(!beginStartDate || !endStartDate){
						layer.msg(getI18nValue("日期参数不能为空"), {icon:5});
						return false;
					}
					var channelType = $("#channelType").val();
					if(channelType){
						$("#searchForm").initTableEx({
							mars:'QcTaskObjDao.changeMediaList',
							limit:15,
							limits:[15,25,50,100,200],
							height: 'full-160',
							id:"main",
							cols: [
									[{width:80, title: '序号',type:'checkbox', fixed: 'left'}
								    ,{width:80, title: getI18nValue('序号'),type:'numbers'}
								      ,{minWidth:160,field:'TASK_NAME',title:getI18nValue('任务名称')}
									,{width:110,field:'type',title:getI18nValue('渠道类型'),templet:function(row){
									      	return getDictTextByCode('QC_CHANNEL_TYPE', row.CHANNEL_TYPE);
									  }}
									 ,{minWidth:160,field:'CHANNEL',title:getI18nValue('渠道')}
									 ,{minWidth:130,field:'CUST_NAME', title: getI18nValue('客户')}
							         ,{minWidth:130,field:'DATE_ID', title: getI18nValue('日期'),templet:function(row){
								      	 return formatStringyyyyMMddToyyyy_MM_dd(row.DATE_ID)
								       }}
							         ,{minWidth:105,field:'BEGIN_TIME', title: getI18nValue('开始时间'),templet:function(row){
							        	 return fnTime(row.BEGIN_TIME);
							         }}
							         ,{minWidth:105,field:'END_TIME', title: getI18nValue('结束时间'),templet:function(row){
							        	 return fnTime(row.END_TIME);
							         }}
							         ,{minWidth:105,field:'SERVER_TIME', title: getI18nValue('服务时长'),templet:function(row){
							        	 var time=row.SERVER_TIME;
							        	 if(time){
							        		 return formatTimeBySecond(time);
							        	 }
							 			 return "0:00:00";
							         }}
							         ,{minWidth:110,field:'AGENT_ACC', title:getI18nValue('坐席账号')}
							         ,{minWidth:110,field:'AGENT_NAME', title:getI18nValue('坐席名称'),templet:function(row){
							        	 return row.AGENT_NAME || '';
							         }}
							         ,{minWidth:110,field:'INSPECTOR', title:getI18nValue('质检员账号')}
							         ,{minWidth:110,field:'INSPECTOR_NAME', title:getI18nValue('质检员名称'),templet:function(row){
							        	 return row.INSPECTOR_NAME || '';
							         }}
							         ,{minWidth:160,field:'CREATE_TIME', title: getI18nValue('人工抽检时间')}
							         ,{minWidth:160,field:'INSPECTOR_TIME', title: getI18nValue('分配时间')}
							         ,{minWidth:160,field:'RG_QC_TIME', title: getI18nValue('人工质检时间')}
							         ,{minWidth:160,field:'ZN_QC_SCORE', title: getI18nValue('智能质检分数'),templet:function(row){
							        	 var temp = '';
							        	 if(row.ZN_QC_SCORE || row.ZN_QC_SCORE == 0){
							        	 	temp = '<a href="javascript:void(0)" onclick="QcTaskRecord.znResultData(\''+row.QC_RESULT_ID+'\',\''+row.SERIAL_ID+'\',\''+row.CHANNEL_TYPE+'\',\''+row.ZN_CLASS_ID+'\',\''+row.CASE_STATUS+'\')"> ' + row.ZN_QC_SCORE + '</a>';;
							        	 }
							        	 return temp;
							         }}
							         ,{minWidth:160,field:'ZN_QC_SCORE', title: getI18nValue('人工质检分数'),templet:function(row){
							        	 var temp = '';
							        	 if(row.RG_QC_SCORE || row.RG_QC_SCORE == 0){
							        	 	temp = '<a href="javascript:void(0)" onclick="QcTaskRecord.rgResultData(\''+row.QC_RESULT_ID+'\',\''+row.SERIAL_ID+'\',\''+row.CHANNEL_TYPE+'\',\''+row.ZN_CLASS_ID+'\')"> ' + row.RG_QC_SCORE + '</a>';
							        	 }
							        	 return temp;
							         }}
							         ,{minWidth:160,field:'RECONSIDER_FLAG', title: getI18nValue('申诉状态'),templet:function(row){
							        	 var reconsiderFlag = row.RECONSIDER_FLAG || '';
							        	 if(reconsiderFlag == '0'){
							        		 return getI18nValue('未申诉');
							        	 }else if(reconsiderFlag == '1'){
							        		 return getI18nValue('申诉中');
							        	 }else if(reconsiderFlag == '2'){
							        		 return getI18nValue('申诉通过');
							        	 }else if(reconsiderFlag == '3'){
							        		 return getI18nValue('申诉不通过');
							        	 }else if(reconsiderFlag == '4'){
							        		 return getI18nValue('申诉完成');
							        	 }
							        	 return '';
							         }}
							         ,{minWidth:160,field:'EVALUATE', title: getI18nValue('评定结果'),templet:function(row){
							        	 var evaluate = row.EVALUATE || '';
							        	 var evaluate2 = row.EVALUATE2 || '';
							        	 var text1 = evaluate ? getText(evaluate, 'label') : '';
							        	 var text2 = evaluate2 ? getText(evaluate2, "label") : '';
							        	 var text = getI18nValue(text1);
							        	 if(text2){
							        		 text += ' / ' + getI18nValue(text2)
							        	 }
							        	 return text;
							         }}
							         ,{minWidth:150,field:'EXAM_GROUP_ID',fixed: 'right', title: getI18nValue('操作'),templet:function(row){
											var html = '<a href="javascript:void(0)" onclick="QcTaskRecord.rgResultData(\''+row.QC_RESULT_ID+'\',\''+row.SERIAL_ID+'\',\''+row.CHANNEL_TYPE+'\',\''+row.ZN_CLASS_ID+'\',\''+row.CASE_STATUS+'\')"> ' + getI18nValue('查看详情') + '</a><label class="inspector">';
											<EasyTag:res resId="cc-qc-rwzx-updatebtn">
												var rgQcTime = row.RG_QC_TIME;
												if(rgQcTime){
													var times = new Date().getTime() - new Date(rgQcTime).getTime();
													times = times/1000/60/60;
													allowEditTime = parseFloat(allowEditTime) || 1;
													if(times < allowEditTime){
														//修改	
														html += ' - <a href="javascript:void(0)" onclick="QcTaskRecord.editData(\''+row.EXAM_GROUP_ID+'\',\''+row.SERIAL_ID+'\',\''+row.QC_RESULT_ID+'\',\''+row.CHANNEL_TYPE+'\',\''+row.ID+'\',\''+row.ZN_CLASS_ID+'\')">' + getI18nValue('修改') + '</a></label>';
													}
												}
											</EasyTag:res>
											return html;
						         	}}
						         ]
							],
							done:function(res,curr,count){
								var data = res;
								for (var i = 0; i < res.data.length; i++) {
									if (res.data[i].ONE_VOTE_ITEM>0) {
									       $("table tbody tr").eq(i).css('background-color', 'rgb(239, 181, 181)')
					                   }
								}
					        } 
						});
					}else{
						layer.msg(getI18nValue("请先选择渠道类型！"), {icon : 5});
					}
				},
				searchData:function(){
					QcTaskRecord.loadData();
				},
				editData:function(groupId,serialId,qcResultId,channelType,objId,znClassId){
					var data = {groupId:groupId,serialId:serialId,qcResultId:qcResultId,callType:channelType,objId:objId,znClassId:znClassId};
					<%if("N".equals(runManual)){%>
						if(channelType == '1'){
							popup.layerShow({type:2,title:getI18nValue('质检结果修改'),offset:'20px',area:['80%','80%'],maxmin:true,full:true,shadeClose:false,moveOut:true}
								,'${ctxPath}/pages/result/qc-result-voice-checked.jsp',data);	
						}else{
							popup.layerShow({type:2,title:getI18nValue('质检结果修改'),offset:'20px',area:['80%','80%'],maxmin:true,full:true,shadeClose:false,moveOut:true}
								,'${ctxPath}/pages/result/qc-result-media-checked.jsp',data);
						}
					<%}else{%>
						popup.layerShow({type:2,title:getI18nValue('质检结果修改'),offset:'20px',area:['80%','80%'],maxmin:true,full:true,shadeClose:false,moveOut:true},'${ctxPath}/pages/result/qc-result-Input.jsp',data);
					<%}%>
				}
				,randomData: function (num) {
					num = num || 5;
					var data = {};
					data.taskNum = num;
				}
				,znResultData:function(qcResultId,serialId,channelType,znClassId,caseStatus){
					var data = {qcResultId:qcResultId,serialId:serialId,channelType:channelType,znClassId:znClassId,caseStatus:caseStatus};
					if(channelType == 1){
						popup.layerShow({type:1,title:getI18nValue('质检详情'),area:['80%','80%'],maxmin:true,full:true,shadeClose:false,moveOut:true,end:function(){
							if(typeof(wavesurfer) != 'undefined' && wavesurfer){
								wavesurfer.destroy()
							}
						}},'/cc-quality/pages/result/qc-znVoiceResult-info.jsp',data);
					}else{
						popup.layerShow({type:1,title:getI18nValue('质检详情'),offset:'20px',area:['80%','80%'],maxmin:true,full:true,shadeClose:false,moveOut:true}
						,'/cc-quality/pages/result/qc-znMediaResult-info.jsp',data);
					}
				}
				,rgResultData:function(qcResultId,serialId,channelType,znClassId){
					var data = {qcResultId:qcResultId,serialId:serialId,znClassId:znClassId};
					if(channelType == 1){
						popup.layerShow({type:2,title:getI18nValue('质检详情'),area:['80%','80%'],maxmin:true,full:true,shadeClose:false,moveOut:true,end:function(){
							if(typeof(wavesurfer) != 'undefined' && wavesurfer){
								wavesurfer.destroy()
							}
						}},'/cc-quality/pages/result/qc-result-voice-detail.jsp',data);
					}else{
						popup.layerShow({type:2,title:getI18nValue('质检详情'),offset:'20px',area:['80%','80%'],maxmin:true,full:true,shadeClose:false,moveOut:true},
								'/cc-quality/pages/result/qc-result-media-detail.jsp',data);
					}
				},
			}
		
		$.views.converters('fnTime',function(time){
			return formatTimeBySecond(time);
		});
		
		function formatTimeBySecond(time){
			if(time == undefined || time == null || time=='') return "0:00:00";
			time = parseInt(time);
			var h = Math.floor(time/3600);
			var m = Math.floor(time%3600/60);
			var s = time%60;
			m = m<10?'0'+m:m;
			s = s<10?'0'+s:s;
			return h+":"+m+":"+s;
		}

		function fnTime(time){
			if(!time)return '';
			return time.substring(11);
		}
		function createCauseName(val){
			//1,2,3,4,5,9,10,14
			if(val=='1'||val=='2'||val=='3'||val=='4'||val=='5'||val=='9'||val=='10'||val=='14'){
				return getI18nValue('呼入');
			}else{
				return getI18nValue('呼出');
			}
		}  
		function agentReleaseName(val){
			if(val=='1'){
				return getI18nValue('用户挂断');
			}else if(val=='2'){
				return getI18nValue('坐席挂断');
			}else if(val=='3'){
				return getI18nValue('系统挂断');
			}else{
				return '';
			}
		}
		function isTypeicalName(val){
			if(val=='01'){
				return getI18nValue('否');
			}else if(val=='02'){
				return getI18nValue('是');
			}else{
				return '';
			}
		}
		
		function toggleMore(){
			var btn = $("#moreBtn").find(".glyphicon");
			$("#more").slideToggle('fast');
			btn.toggleClass("glyphicon glyphicon-menu-down glyphicon glyphicon-menu-up")
		}
		function toggleReset(){
			//多选框单独重置
			$('#reset select').val("").trigger("change");
			//checkbox value不能清空，如果有checkbox应该是清楚选中
	    	$('#reset input').val("");
	    	$("#dateRange").val("thisWeek").trigger("change");
	    	$("#channelType option:first").prop('selected', 'selected');
	    	$("#channelType").trigger("change");
		}
		
		function reloadList(){
			QcTaskRecord.loadData();
		}
		
		function getCheckArr() {
			var arr = [];
			$.each(table.checkStatus("main").data,function(i,n){
				arr.push(n.OBJ_ID);
            });
			return arr;
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>