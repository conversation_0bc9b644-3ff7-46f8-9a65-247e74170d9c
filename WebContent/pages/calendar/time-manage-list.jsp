<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title i18n-content="工作时间"></title>
	<style>
		
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form action="" method="post" name="searchForm" class="form-inline"
		id="searchForm" data-toggle="render">
		<div class="ibox">
			<div class="ibox-title clearfix">
				<div class="form-group">
					<h5 i18n-content="工作时间"></h5>
					<div class="input-group btn-group pull-right">
						<button type="button" class="btn btn-sm btn-success btn-outline"
							onclick="incoming.editAll()" i18n-content="批量设置工作时间"></button>
						<button type="button" class="btn btn-sm btn-danger btn-outline"
							onclick="incoming.initCalendar()" i18n-content="工作日历"></button>
							<!-- <button type="button" class="btn btn-sm btn-danger btn-outline"
							onclick="incoming.toHoliday();" i18n-content="工作日转假期"></button> -->
					</div>
					<hr style="margin: 5px -15px">
				</div>
				<div class="form-group">
					<div class="input-group input-group-sm">
						<span class="input-group-addon" i18n-content="日期段"></span> <input
							type="text" name="DATE" id="DATE" class="form-control input-sm"
							style="width: 170px">
					</div>
					<div class="input-group input-group-sm">
						<span class="input-group-addon" i18n-content="日期类型" ></span>	
	                    <select class="form-control input-sm" name="DAY_TYPE1" data-mars="common.getDict(CALENDAR_DATE_TYPE)">
	                    	 <option value="" i18n-content="请选择"></option>
				        </select>
			        </div>
					<div class="input-group input-group-sm">
						<button type="button" class="btn btn-sm btn-default"
							onclick="incoming.initData()">
							<span class="glyphicon glyphicon-search" i18n-content="搜索"></span>
						</button>
					</div>
					<div class="input-group input-group-sm">
						<button type="button" class="btn btn-sm btn-default" onclick="incoming.reset()"><span class="glyphicon glyphicon-repeat"></span> 
							<span i18n-content="重置" ></span>
						</button>
					</div> 
				</div>
			</div>
			<div class="ibox-content">
				<table id="main"></table>
				<script type="text/html" id="operateTpl">
					<a class="layui-btn layui-btn-success layui-btn-xs" lay-event="incoming.editType" i18n-content="编辑"  ></a>
					<a class="layui-btn layui-btn-success layui-btn-xs" lay-event="incoming.edit" i18n-content="设置工作时间" ></a>
				</script>
			</div>
		</div>
	</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script src="${ctxPath}/static/js/time.js"></script>
	<script type="text/javascript">
		jQuery.namespace("incoming");
		$(function() {
			$("#searchForm").render({
				success : function() {
				}
			});
			var today = getTodayDate();//今日
			var yearLastDay = getThisYearEndDate();//今年最后一天
			var date = today + " - " + yearLastDay;
			$("#DATE").val(date);
			incoming.initData();
			layui.use(['laydate','dropdown'], function(){
				var laydate = layui.laydate;
				var dropdown = layui.dropdown
				laydate.render({
				  elem: '#DATE'
				  ,range: true
				  ,lang: getDateLang()
				});
			});
		})
		//表格渲染
		incoming.initData = function(){
			$("#searchForm").initTableEx({
				mars:'incomingTime.getList',
				id:'main',
				height: 'full-170',
				limit:15,
				limits:[15,50,70,100,200],
				cols: [
					[
						{type:'checkbox'}
					 ,{title: getI18nValue('序号'),type:'numbers'}
					 ,{minWidth:150,field:'DAY',align:'center', title: getI18nValue('日期')}
			         ,{minWidth:80,field:'WEEKDAY', title: getI18nValue('星期'),templet:function(row){
			        	 if(row.WEEKDAY == 1){
			        		 return getI18nValue('星期一');
			        	 }else if(row.WEEKDAY == 2){
			        		 return getI18nValue('星期二');
			        	 }else if(row.WEEKDAY == 3){
			        		 return getI18nValue('星期三');
			        	 }else if(row.WEEKDAY == 4){
			        		 return getI18nValue('星期四');
			        	 }else if(row.WEEKDAY == 5){
			        		 return getI18nValue('星期五');
			        	 }else if(row.WEEKDAY == 6){
			        		 return getI18nValue('星期六');
			        	 }else if(row.WEEKDAY == 7){
			        		 return getI18nValue('星期日');
			        	 }
			         }}
			         ,{minWidth:80,field:'DAY_TYPE',align:'center', title: getI18nValue('日期类型'),templet:function(row){
			        	 return  getDictTextByCode('CALENDAR_DATE_TYPE',row.DAY_TYPE);
			        	 //return getText(row.DAY_TYPE,'TYPE');
			         }}
			         ,{minWidth:80,field:'HOLIDAY_NAME',align:'center', title: getI18nValue('节假日名称')}
			         ,{minWidth:225,field:'CALL_IN_TS',align:'center',title:getI18nValue('语音呼入工作时间配置数')}
			         ,{minWidth:225,field:'MEDIA_IN_TS',align:'center',title:getI18nValue('在线接入工作时间配置数')}
			         ,{minWidth:200,fixed:'right',align:'center',title : getI18nValue('操作'),templet:'#operateTpl'}
			       ]
				],
				done:function(){
					
					layui.use(['form'], function () {
						var form = layui.form;
						execI18n();
					});
					
				}
			});
		}
		
		incoming.edit = function(obj){
			popup.openTab({url:'${ctxPath}/pages/calendar/time-manage-editList.jsp','title':getI18nValue('工作时间管理'),reload:true,data:{dayType:obj.DAY_TYPE,WEEKDAY:obj.WEEKDAY,DAY:obj.DAY,WORK_CALENDAR_ID:obj.CALENDAR_ID}});
		}
		incoming.editAll = function(){
			popup.openTab({url:'${ctxPath}/pages/calendar/time-manage-editList.jsp','title':getI18nValue('工作时间管理'),reload:true,data:{}});
		}
		incoming.editType = function(obj) {
			var dayId = obj.CALENDAR_ID;
			var holiDayName = obj.HOLIDAY_NAME;
			var DAY = obj.DAY;
			popup.layerShow({
				type : 2,
				title : getI18nValue("编辑"),
				offset : '20px',
				area : [ '580px', '450px' ]
			}, "${ctxPath}/pages/calendar/time-manage-editType.jsp", {
				ids:dayId,
				holiDayNames:holiDayName,
				DAY:DAY,
			});
		}
		
		incoming.initCalendar = function(){
			popup.openTab({url:'/cc-base/pages/calendar/workcalendar.jsp','title':getI18nValue('工作日历'),reload:true,data:{}});
		}
		
		incoming.toHoliday = function() {
			var arr = new Array();
			var checkStatus = table.checkStatus('main');
			if (checkStatus.data.length == 0) {
				layer.msg(getI18nValue("请选择需要更改的行"));
				return 0;
			} else {
				for (j = 0; j < checkStatus.data.length; j++) {
					arr.push(checkStatus.data[j].CALENDAR_ID);
				}
			}
			layer.confirm(getI18nValue("确定要将工作日转换成假期吗？"), {
				btn : [ getI18nValue("确认"), getI18nValue("取消") ],
				 title:getI18nValue('工作日转假日'),
				 offset:'20px'
			}, function(index) {
				ajax.remoteCall("${ctxPath}/servlet/incomingTime?action=toHoliday",
						{
							ids : arr
						}, function(result) {
							if (result.state == 1) {
								layer.msg(result.msg, {
									icon : 1,
									time : 1200
								}, function() {
									layer.closeAll();
									incoming.initData();
								});
							} else {
								layer.alert(result.msg, {
									icon : 5
								});
							}
						});
				return true;
			}, function(index) {
				layer.msg(getI18nValue("已取消"), {
					icon : 1
				});
				return false;
			});
		}
		//重置
		incoming.reset = function(){
			$(".input-group input").val("");
			$(".input-group select").val("");
			var today = getTodayDate();//今日
			var yearLastDay = getThisYearEndDate();//今年最后一天
			var date = today + " - " + yearLastDay;
			$("#DATE").val(date);
			incoming.initData();
		}
		function layTableAllChoose(){
			
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
