<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>部门类型管理</title>
</EasyTag:override>
<EasyTag:override name="content">
	<form  class="form-inline" id="searchForm">
		<div class="ibox">
			<div class="ibox-title clearfix">
       		    <div class="form-group">
       		        <h5> 部门类型管理</h5>
					<div class="input-group input-group-sm pull-right">
						<button type="button" class="btn btn-sm btn-success " onclick="NodeType.addData()">+添加类型</button>
				    </div>
	        	</div>
             </div> 
			 <div class="ibox-content">
				 <table  data-height="auto" id="gm"></table>
			 </div>
		 </div>
	 </form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">	
	var treeObj;
	jQuery.namespace("NodeType");
	
	$(function(){
		requreLib.setplugs('layui',function(){
			initGM();
		});
	});
	
	NodeType.searchData= function(){
		$("#searchForm").queryData();
    }
	
	function initGM(){
		$("#searchForm").initTable({
			url:'${ctxPath}/webcall?action=v1_nodeType.list',
			cols: [[
             {
        	 	field: 'NODE_TYPE_NAME',
				title: '类型名称',
				align:'left',
				template:function(val,row){
					return  '<a href="javascript:void(0)" onclick="NodeType.editData(\''+row.NODE_TYPE+'\',\''+val+'\')" > '+val+'</a>'
				}
			 },{
				field: 'CREATE_TIME',
				title: '创建时间',
				align:'left'
			},{
				field: 'NODE_TYPE',
				title: '操作',
				align:'center',
				templet:function(row){
					return '<a href="javascript:void(0)" onclick="NodeType.delData(\''+row.NODE_TYPE+'\',\''+row.NODE_TYPE_NAME+'\')" > 移除</a>';
				}
			}
			]]}
		);
	}
	
	NodeType.addData = function(){
		popup.layerShow({type:1,title:'添加类型',offset:'20px',area:['475px','240px']},"${ctxPath}/pages/v1/ecFramework/node-type-edit.jsp",null);
	}
	NodeType.editData = function(nodeTypeId,nodeTypeName){
		popup.layerShow({type:1,title:'修改类型',offset:'20px',area:['475px','240px']},"${ctxPath}/pages/v1/ecFramework/node-type-edit.jsp",{nodeTypeId:nodeTypeId});
	}
	NodeType.delData = function(nodeTypeId){
		layer.confirm('当前类型将要被删除，是否继续？',{icon: 3, title:'删除提示',offset:'20px'},  function(index){
			layer.close(index);
	    	var data = {nodeTypeId:nodeTypeId}
	  		ajax.remoteCall("${ctxPath}/servlet/v1_nodeType?action=delete", data, function(result) {
	  			if(result.state == 1){
				    layer.msg(result.msg,{icon: 1,time:1200,offset:'40px'},function(){
				    	NodeType.searchData();
				    });
				}else{
					layer.alert(result.msg,{icon: 5});
				}
  			});
		});
	}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>