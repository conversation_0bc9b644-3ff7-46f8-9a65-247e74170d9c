<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page import = "com.yunqu.yc.quality.base.Constants" %>
<%
String runManual = Constants.RUN_MANUAL;
%>
<EasyTag:override name="head">
	<title i18n-content="规则建议列表"></title>
   <link rel="stylesheet" href="/cc-base/static/layui/css/layui.css" media="all">
   <link rel="stylesheet" href="/cc-base/static/layuiAdmin/style/admin.css" media="all">
   <link rel="stylesheet" href="/cc-quality/static/css/chat-record.css">
	<style>
	.layer_notice {
	    background: #5FB878;
	    padding: 10px;
	    height: 75px;
    	width: 330px;
	    display: none;
	    color: white;
	}
	 .ztree{background-color: #fff!important;}
        .ztreeDiv {
			display:none;position: absolute;border:1px solid rgb(170,170,170);min-width: 150px;max-width: 300px; 
			max-height: 200px;z-index:10;overflow: auto;background-color: #F0F0F0;z-index:150
		}
	.input-group-sm .qt_success{
		background-color: #5cb85c;
    	color: white;
	}	
	.input-group-sm .qt_run{
		background-color: #5bc0de;
    	color: white;
	}	
	.input-group-sm .qt_wait{
		background-color: #f0ad4e;
    	color: white;
	}	
	#task_search input{height: 30px}
	
	.shadow{
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
		-moz-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
		-webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
	}
	
	.const-width{
		width: 257px;
	}
	.input-group-addon{
		width: 81px !important;
	}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form class="form-inline shadow" id="searchForm">
       		<input type="hidden" name="scope" id="scope" value="${param.scope}">
       		<input type="hidden" value="${param.classId}" name="classId" id="classId">
           	<div class="ibox">
             		<div class="ibox-title clearfix ref-hide" id="resetid">
             		  <div class="form-group">
						<h5 i18n-content="我的规划建议"></h5>
					  </div>
					  <hr style="margin: 5px -15px">
						<div class="form-group">
	             		  	  <div class="input-group input-group-sm const-width" style="height: 30px" id="task_search">
             		              <span class="input-group-addon" i18n-content="关键字"></span>	
							  	  <input type="text"  name="adviceName" id="adviceName" style="height: 30px;"  class="form-control input-sm" i18n-placeholder="提交人账号/提交人姓名" />
							  </div>
							  
							  <div class="input-group">
					 			<span class="input-group-addon" i18n-content="创建时间"></span>
					        	<input type="text" class="form-control input-sm" id="beginStartDate" name="beginStartDate" autocomplete="off" style="height:30px;width:142px" > 
			                	<span class="input-group-addon" style="width: 30px !important;">~</span>
			                	<input type="text" class="form-control input-sm" id="endStartDate" name="endStartDate" autocomplete="off" style="height:30px;width:142px" > 
			                	<span class="input-group-addon" style="width: 30px !important;">-</span>
			                	<select class="form-control input-sm" name="dateRange" id="dateRange" onchange="onCasecade($(this), 'beginStartDate', 'endStartDate')">
			                   		<option value="" i18n-content="请选择"></option>
									<option value="today" i18n-content="今天"></option>
									<option value="yesterday" i18n-content="昨天"></option>
									<option value="thisWeek" i18n-content="本周"></option>
									<option value="RecentlyOneMonth" i18n-content="近一个月"></option>
									<option value="RecentlyThreeMonth" i18n-content="近三个月"></option>
								</select>
		                	  </div>
							  
							  <div class="input-group input-group-sm">
			                        <span class="input-group-addon" i18n-content="处理状态"></span>
			                        <select id="adviceStatus" class="form-control input-sm" name="adviceStatus">
			                            <option value="" i18n-content="请选择"></option>
			                            <option value="0" data-class="label label-default" i18n-content="未处理"></option>
			                            <option value="1" data-class="label label-success" i18n-content="已处理"></option>
			                        </select>
			                    </div>
			                    <div class="input-group input-group-sm">
			                        <span class="input-group-addon" i18n-content="是否采纳"></span>
			                        <select id="isAdopt" class="form-control input-sm" name="isAdopt">
			                            <option value="" i18n-content="请选择"></option>
			                            <option value="Y" data-class="label label-success" i18n-content="是"></option>
			                            <option value="N" data-class="label label-default" i18n-content="否"></option>
			                        </select>
			                    </div>
							   <div class="input-group input-group-sm">
									  <button type="button" class="btn btn-sm btn-default" onclick="QcClassAdvice.searchData()"><span class="glyphicon glyphicon-search"></span> <span i18n-content="查询"></span></button>
							   </div>
							   <div class="input-group input-group-sm">
									<button type="button" class="btn btn-sm btn-default" onclick="repeat()">
									 <span class="glyphicon glyphicon-repeat"></span> <span i18n-content="重置"></span></button>
								</div>
						  </div>
             	    </div>  
	              	<div class="ibox-content">
	              		<table id="main" class="layui-table layui-form">
	              	    </table>
	              	</div>  
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script src="${ctxPath}/static/lib/wavesurfer/wavesurfer.js"></script>
	<script src="${ctxPath}/static/lib/wavesurfer/wavesurfer.timeline.min.js"></script>
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	
	<script type="text/javascript">
	jQuery.namespace("QcClassAdvice");
	layui.config({
		  base: '/cc-quality/static/js/assets/module/'
	})
	$(function(){
		$("#dateRange").val("thisWeek").trigger("change");
	   //渲染质检时间thisWeek
	   layui.use('laydate', function(){
			  var laydate = layui.laydate;
			  laydate.render({
				  elem: '#beginStartDate',
				  range: false,
				  lang:getDateLang()
				});
			  laydate.render({
				  elem: '#endStartDate',
				  range: false,
				  lang:getDateLang()
				});
		});
		QcClassAdvice.loadData(); 
	});
	
	var QcClassAdvice={
			loadData:function(data){
				$("#searchForm").initTableEx({
					mars:'QcClassDao.classAdviceList',
					height: 'full-180',
					limit:10,
					cellMinWidth:120,
					limit:15,
					id:'main',
					limits:[15,25,50,100,200],
					cols: [
						[
				         {width:80, title: '序号',type:'numbers', sort: true}
				         ,{field: 'CLASS_NAME',title: '规则名称',align: 'center',minWidth:160}
				         ,{minWidth:100,field:'ITEM_ID1_NAME', title: '一级质检项'}
				         ,{minWidth:120,field:'ITEM_ID2_NAME', title: '二级质检项'}
				         ,{minWidth:150,field:'CONTENT', title: '建议内容'}
				         ,{minWidth:100,field:'STATUS', title: '处理状态', templet:function(row){
				        	 return getDictTextByCode("STATE",row.STATUS);
				         }}
				         ,{width:100,field:'IS_ADOPT', title: '是否采纳', templet:function(row){
				        	 return getText(row.IS_ADOPT, 'isAdopt') || "";
				         }}
				         ,{width:160,field:'DEAL_RESULT', title: '处理结果'}
				         ,{minWidth:120,field:'CREATE_NAME', title: '提交人姓名'}
				         ,{minWidth:170,field:'CREATE_TIME', title: '创建时间', sort: true}
				         ,{minWidth:170,field:'DEAL_TIME', title: '处理时间', sort: true}
				         ,{minWidth:120,field:'DEAL_ACC', title: '处理人账号'}
				         ,{width:140,field:'EXAM_GROUP_ID',fixed: 'right',align:"center", title: '操作',templet:function(row){
							var handle = '';
							<EasyTag:res resId="cc-qc-zjqx-gzjycl">
				        	if(row.STATUS == '0'){
				        		handle = '<a href="javascript:void(0)" onclick="QcClassAdvice.handleAdvice(\''+row.ID+'\')"> ' + getI18nValue('处理') + '</a> - '; 
				        	 }
							</EasyTag:res>
							var temp = handle +'<a href="javascript:void(0)" onclick="QcClassAdvice.showDetail(\''+row.ID+'\')"> ' + getI18nValue('查看') + '</a>';
				        	 return temp;
				         }}
				         ]
					],
					done:function(res,curr,count){
						var data = res;
						for (var i = 0; i < res.data.length; i++) {
							if (res.data[i].ONE_VOTE_ITEM>0) {
							       $("table tbody tr").eq(i).css('background-color', 'rgb(239, 181, 181)')
			                   }
						}
						if(data && data != undefined){
							//是否是质检员
							if(data.isInspector){
								$(".inspector").show();
							}else{
								
							}
						}
			        } ,
	        		rowEvent: function (row, event){
					}
				});
			},
			searchData:function(){
				QcClassAdvice.loadData();
			},
			handleAdvice:function(id){
				popup.layerShow({type:2,title:getI18nValue('处理规则建议'),offset:'20px',area:['400px','516px']},"${ctxPath}/pages/class/qc-class-media-advice-handle.jsp",{ID:id});
			},
			showDetail:function(id){
				popup.layerShow({type:2,title:getI18nValue('规则建议详情'),offset:'20px',area:['400px','516px']},"${ctxPath}/pages/class/qc-class-media-advice-detail.jsp",{ID:id});
			}
		}
	function repeat() {
 		$("#resetid input").val("");
 		$("#resetid select").val("");
 		$("#dateRange").val("thisWeek").trigger("change");
 	}
	function moreQuery() {
		$('#more-query-content').toggle();
	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>