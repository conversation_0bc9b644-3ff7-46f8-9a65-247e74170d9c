<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title i18n-content="模块跳转配置"></title>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="form" data-mars="entModuleJump.edit" data-pk="" method="post"
		autocomplete="off" data-mars-prefix="entModuleJump.">
		
		<input type="hidden" name="entModuleJump.ID" value="${param.id}"/>
		<input type="hidden" name="entModuleJump.APP_ID" value="${param.appId}"/>
		
		<table class="table  table-edit table-vzebra mt-10">
			<tbody>
				<tr>
					<td  width="60px" i18n-content="标准URL"></td>
					<td><input value="${param.S_URL}" id="S_URL" data-rules="required" maxlength="100" name="entModuleJump.S_URL" class="form-control input-sm" type="text"></td>
				</tr>
				<tr>
					<td  width="60px" i18n-content="跳转URL"></td>
					<td><input id="N_URL"  maxlength="100" name="entModuleJump.N_URL" class="form-control input-sm" type="text"></td>
				</tr>
			</tbody>
		</table>
		<div class="layer-foot text-c">
			<button class="btn btn-sm btn-primary" type="button" onclick="entModuleJump.ajaxSubmitForm()" i18n-content="保存"></button>
			<button class="btn btn-sm btn-default ml-20" type="button"
				id="backbut" onclick="layer.closeAll();" i18n-content="关闭"></button>
		</div>
	</form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
		requreLib.setplugs('wdate');
		jQuery.namespace("entModuleJump");
		
		$(function(){
			$("#form").render({success : function(result){}}); 
		});
		entModuleJump.ajaxSubmitForm = function(){
				if(!form.validate("#form")){
					i18nTooltip();
					return;
				};
				if($("#S_URL").val() == $("#N_URL").val()){
					layer.alert(getI18nValue("标准URL与跳转URL不能相同"),{icon: 5});
					return;
				}
				
				 var data = form.getJSONObject("form");
					ajax.remoteCall("${ctxPath}/servlet/entModuleJump?action=save",data,function(result) { 
							if(result.state == 1){
								layer.msg(result.msg,{icon: 1});
								layer.closeAll();
								window.location.reload(); 

							}else{
								layer.alert(result.msg,{icon: 5});
							}
						}
					);
		 }
		 
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp"%>