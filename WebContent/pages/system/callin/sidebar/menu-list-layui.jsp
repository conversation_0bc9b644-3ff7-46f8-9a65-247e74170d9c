<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title i18n-content="工单侧边栏配置管理"></title>
	<style type="text/css">
		a:link{ color:#00adff;}
		.layui-table-view .layui-table{  
			position: relative;
		    width: 100%;  
		    margin: 0px;
    	}
    	.layui-table-cell, .layui-table-tool-panel li{overflow:hidden;}
    	.layui-table-header .layui-table-cell, .layui-table-tool-panel li{overflow: inherit!important;}
    	::-webkit-scrollbar {
			width: 8px;
			height: 8px;
			background: transparent;
		}
		::-webkit-scrollbar-track {
			background: transparent;
		}
		::-webkit-scrollbar-thumb {
			border-radius: 8px;
			background-color: #C1C1C1;
		}
		::-webkit-scrollbar-thumb:hover {
			background-color: #A8A8A8;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
		<ul class="layui-tab-title">
		    <li class="layui-this" i18n-content="基本配置"></li>	
		    <li i18n-content="主页面配置"></li>
		    <li i18n-content="侧边栏配置"></li>
		</ul>
		<div class="layui-tab-content" style="height: 100px;">
		<div class="layui-tab-item layui-show">
		    	<form action="" method="post" name="baseInfoForm" class="form-inline layui-form" id="baseInfoForm"
		    	 	data-mars="callIn.getCallInCfg" data-mars-prefix="baseInfo." >
					<div class="layui-row">
  						<div class="layui-col-md12 layui-col-sm12 layui-card" style="margin-bottom: 5px;padding-bottom: 10px">
      						<div class="layui-card-header" ><span i18n-content="弹屏参数配置"></span></div>
      						
      						<div class="layui-card-body">
      						<div class="layui-col-md12 layui-col-sm12" style="margin-top: 5px;">
      							<div class="layui-col-md1 layui-col-sm1" style="width: 140px;">
	        						<label class="layui-form-label layui-form-required" style="width: 140px;" i18n-content="标题"></label>
				        		</div>
	        					<div class="layui-col-md8 layui-col-sm8">
	      							<input name="baseInfo.title" data-rules="required" 
	      								lay-vertype="tips" lay-verify="required"
										class="layui-input" type="text"></input>
	    						</div>
      						</div>
      						<div class="layui-col-md12 layui-col-sm12 ">
      							<div class="layui-col-md1 layui-col-sm1" style="width: 140px;">
	        						<label class="layui-form-label layui-form-required" style="width: 140px;">URL<i id="alertMsg" class="layui-icon layui-icon-about layui-icon-tips" style="color: #1E9FFF;"></i>：</label>
				        		</div>
	        					<div class="layui-col-md8 layui-col-sm8">
	      							<textarea rows="3"name="baseInfo.url" data-rules="required" 
	      								lay-vertype="tips" lay-verify="required"
										class="layui-textarea" type="text"></textarea>
	    						</div>
      						</div>
      						
      						<div class="layui-col-md12 layui-col-sm12 ">
      							<div class="layui-col-md1 layui-col-sm1" style="width: 140px;">
	        						<label class="layui-form-label layui-form-required" style="width: 140px;" i18n-content="窗口打开方式"></label>
				        		</div>
	        					<div class="layui-col-md8 layui-col-sm8">
	        						<input type="radio" name="baseInfo.type" value="1" i18n-title="内置打开">
	        						<input type="radio" name="baseInfo.type" value="2" i18n-title="window.open打开">
									<input type="radio" name="baseInfo.type" value="3" i18n-title="a标签打开">
	    						</div>
      						</div>
      						
      						<div class="layui-col-md12 layui-col-sm12 ">
      							<div class="layui-col-md1 layui-col-sm1" style="width: 140px;">
	        						<label class="layui-form-label layui-form-required" style="width: 140px;" i18n-content="是否唯一弹屏"></label>
				        		</div>
	        					<div class="layui-col-md8 layui-col-sm8">
	        						<input type="radio" name="baseInfo.unique" value="1" i18n-title="是">
	        						<input type="radio" name="baseInfo.unique" value="2" i18n-title="否">
	    						</div>
      						</div>
      						
      						<div class="layui-col-md12 layui-col-sm12 ">
      							<div class="layui-col-md1 layui-col-sm1" style="width: 140px;">
	        						<label class="layui-form-label layui-form-required" style="width: 140px;" i18n-content="是否弹屏"></label>
				        		</div>
	        					<div class="layui-col-md8 layui-col-sm8">
	        						<input type="radio" name="baseInfo.open" value="0" i18n-title="是">
	        						<input type="radio" name="baseInfo.open" value="2" i18n-title="否">
	    						</div>
      						</div>
      						
      						<div class="layui-col-md12 layui-col-sm12" style="margin-top: 15px;">
      							<div class="layui-col-md1 layui-col-sm1" style="width: 140px;">
	        						<label class="layui-form-label" style="width: 140px;"></label>
				        		</div>
				        		<div class="layui-col-md8 layui-col-sm8">
	      							<button class="layui-btn" type="button" onclick="callSideMenu.upCallInCfg()" i18n-content="保存"></button>
	    						</div>
      						</div>
      						</div>
      					</div>
  					</div>
  					<div class="tipMsg" id="tipMsg" style="display: none;">
						<div style="background:#ffffff;color:#676A6C;padding: 5px;width: 540px;font-size: 14px;">
							<p>url格式：</p>
							<p>&nbsp;&nbsp;&nbsp;&nbsp;url?caller=$caller&called=$called</p>
							<p>url参数：</p>
							<p>&nbsp;&nbsp;&nbsp;&nbsp;$callSerialId：序列号</p>
							<p>&nbsp;&nbsp;&nbsp;&nbsp;$entId：企业ID</p>
							<p>&nbsp;&nbsp;&nbsp;&nbsp;$skillId：技能组Id</p>
							<p>&nbsp;&nbsp;&nbsp;&nbsp;$agentId：坐席Id</p>
							<p>&nbsp;&nbsp;&nbsp;&nbsp;$caller：主叫号码</p>
							<p>&nbsp;&nbsp;&nbsp;&nbsp;$called：被叫号码</p>
							<p>&nbsp;&nbsp;&nbsp;&nbsp;</p>
							<p>企业标准来电弹屏地址:/cc-base/pages/system/callin/call-index.jsp?callId=$callSerialId&&called=$called&caller=$caller&source=call</p>
							<p>&nbsp;&nbsp;&nbsp;&nbsp;</p>
							<p>如需使用云客服弹屏地址:/yc-agent/pages/portal/voice_portal.jsp</p>
							<p>&nbsp;&nbsp;&nbsp;&nbsp;</p>
							<p>如果已定制开发工单界面的项目,可以配置成定制的工单界面</p>
						</div>
					</div> 
				</form>
		    </div>
		    <div class="layui-tab-item">
		    	<form action="" method="post" name="searchExtendForm" class="form-inline" id="searchExtendForm" onsubmit="return false" data-toggle="">
					<input type="hidden" name="TYPE"  class="form-control input-sm" value="01">
					<input type="hidden" name="BUSI_ID"  class="form-control input-sm" value="">
					<input type="hidden" name="BUSI_TYPE"  class="form-control input-sm" value="01"/>
					<div class="ibox">
						<div class="ibox-title clearfix"  id="extendDivId">
			 				<div class="form-group">
								<h5 i18n-content="扩展菜单管理"></h5>
								<div class="input-group input-group-sm">
									<span class="input-group-addon" i18n-content="名称"></span> 
									<input name="menuName" class="form-control input-sm">						
								</div>
								<div class="input-group input-group-sm">
									<span class="input-group-addon" i18n-content="状态"></span> 
									<select name="menuStatus" class="form-control input-sm" data-mars="common.getDict(ENABLE_STATUS)" >
										<option value="" i18n-content="请选择"></option>
									</select>
									
								</div>
								<div class="input-group input-group-sm">
									<button type="button" data-event="enter" class="btn btn-sm btn-default" onclick="callSideMenu.searchExtendData('1')"><span class="glyphicon glyphicon-search"></span><span i18n-content="查询" ></span></button>
							   	</div>
							   	<div class="input-group ">
									<button type="button" class="btn btn-sm btn-default" onclick="callSideMenu.menuReset('extendDivId')"><span class="glyphicon glyphicon-repeat"></span><span i18n-content="重置" ></span></button>
								</div>
								<div class="input-group input-group-sm pull-right">
								<EasyTag:res resId="cc-base-system-xtpz-cspz-qycspz">
									<button class="btn btn-sm btn-success btn-outline " type="button"
										onclick="callSideMenu.addMenu('01')" i18n-content="新增"></button>
									
									<button class="btn btn-sm btn-info btn-outline " type="button"
										onclick="callSideMenu.preview()" style="margin-left: 5px" i18n-content="预览"></button>
								</EasyTag:res>
							</div>
			 				</div>
						</div>
						<div class="ibox-content">
							<table id="extendMain"></table>
						</div>
					</div>
				</form>
		    </div>
		    <div class="layui-tab-item">
		    	<form action="" method="post" name="searchMenuForm" class="form-inline" id="searchMenuForm" onsubmit="return false" data-toggle="">
					<input type="hidden" name="TYPE"  class="form-control input-sm" value="02">
					<input type="hidden" name="BUSI_ID"  class="form-control input-sm" value="">
					<input type="hidden" name="BUSI_TYPE"  class="form-control input-sm" value="01"/>
					<div class="ibox">
						<div class="ibox-title clearfix"  id="menuDivId">
			 				<div class="form-group">
								<h5 i18n-content="侧边菜单管理"></h5>
								<div class="input-group input-group-sm">
									<span class="input-group-addon" i18n-content="名称"></span> 
									<input name="menuName" class="form-control input-sm">						
								</div>
								<div class="input-group input-group-sm">
									<span class="input-group-addon" i18n-content="状态"></span> 
									<select name="menuStatus" class="form-control input-sm" data-mars="common.getDict(ENABLE_STATUS)" >
										<option value="" i18n-content="请选择"></option>
									</select>
									
								</div>
								<div class="input-group input-group-sm">
									<button type="button" data-event="enter" class="btn btn-sm btn-default" onclick="callSideMenu.searchMenuData('1')"><span class="glyphicon glyphicon-search"></span> <span i18n-content="查询" ></span></button>
							   	</div>
							   	<div class="input-group ">
									<button type="button" class="btn btn-sm btn-default" onclick="callSideMenu.menuReset('menuDivId')"><span class="glyphicon glyphicon-repeat"></span><span i18n-content="重置" ></span></button>
								</div>
								<div class="input-group input-group-sm pull-right">
									<EasyTag:res resId="cc-base-system-xtpz-cspz-qycspz">
										<button class="btn btn-sm btn-success btn-outline " type="button"
											onclick="callSideMenu.addMenu('02')" i18n-content="新增"></button>
										<button class="btn btn-sm btn-success btn-outline " type="button"
											onclick="callSideMenu.synMenu('02')" i18n-content="一键同步"></button>	
										<button class="btn btn-sm btn-info btn-outline " type="button"
											onclick="callSideMenu.preview()" style="margin-left: 5px" i18n-content="预览"></button>
									</EasyTag:res>
							</div>
			 				</div>
						</div>
						<div class="ibox-content">
							<table id="menuMain"></table>
						</div>
					</div>
				</form>
		    </div>
		</div>
	</div> 
</EasyTag:override>

<EasyTag:override name="script">
<script type="text/javascript">
	jQuery.namespace("callSideMenu");
	var layform;
	var element;
	function renderLayui(){
		if(layform){
			layform.render();
		}
		if(element){
			element.render();
		}
	}
	$(function(){
		layui.use(['form', 'element'], function(){
			layform = layui.form;
			element = layui.element;
			//监听提交
			layform.on('submit(submit0)', function(data){
			    return false;
			});
			renderLayui();
			$("#baseInfoForm").render({success : function(result){renderLayui();}});
		});
		
		$("#searchMenuForm").render({success:function(){
			callSideMenu.initMenuData("searchMenuForm", "menuMain", "02");
		}});
		
		$("#searchExtendForm").render({success:function(){
			callSideMenu.initMenuData("searchExtendForm", "extendMain", "01");
		}});
		
	 	//鼠标悬停提示特效
		$("#alertMsg").hover(function() {
			openMsg();
		}, function() {
			//layer.close(subtips);
		});
	});
	
	function openMsg() {
		var msg = $("#tipMsg").html();
		layer.tips("<span style='color:#676A6C'>"+msg+"</span>", '#alertMsg',{tips:[1,'#ffffff'],closeBtn:1,time:0,area: ['auto', 'auto']});
	}
	
	//修改弹屏配置
	callSideMenu.upCallInCfg = function(){
		var data = form.getJSONObject("#baseInfoForm");
	  	ajax.remoteCall("${ctxPath}/servlet/callIn?action=updateCfg", data, function(result) {
	  		if(result.state == 1){
			    layer.msg(result.msg,{icon: 1,time:1200,offset:'40px'},function(){
			    	window.location.reload();
			    });
			}else{
				layer.alert(result.msg,{icon: 5});
			}
  		});
	}
	
	layui.use('element', function(){
		var element = layui.element; //Tab的切换功能，切换事件监听等，需要依赖element模块
		element.on('tab(test)', function(elem){
			location.hash = 'test='+ $(this).attr('lay-id');
		});
	});
	//侧边栏tab搜索按钮
	callSideMenu.searchExtendData = function(flag) {
		if(flag=='1'){
			$("#searchExtendForm").queryData({id:'extendMain',page:{ curr: 1}});
		}else{
			$("#searchExtendForm").queryData({id:'extendMain'});
		}
	}
	
	//侧边栏tab搜索按钮
	callSideMenu.searchMenuData = function(flag) {
		if(flag=='1'){
			$("#searchMenuForm").queryData({id:'menuMain',page:{ curr: 1}});
		}else{
			$("#searchMenuForm").queryData({id:'menuMain'});
		}
	}
	//侧边栏tab重置筛选框
	callSideMenu.menuReset=function(domId){
		$("#" + domId + " select").val("");
	   	$("#" + domId + " input").val("");
	};
	//初始化加载侧边栏tab数据
	callSideMenu.initMenuData = function(formId, elemId, type){
		$("#"+formId).initTableEx({
			 mars:'sidebarMenu.list',
			 id: elemId,
			 height: 'full-170',
			 cols: [
				[
					{field:'MENU_ID', title: '菜单标志', templet : function(row){
						return '<a style=\'color:#0096e6\' href="javascript:void(0)" onclick="callSideMenu.upMenu(\''+row.ID+'\', \''+type+'\')">'+row.MENU_ID+'</a>';
					}}
					,{field:'MENU_NAME', title: '名称'}
					,{field:'MENU_URL', title: 'url'}
					,{field:'STATUS', title: '状态',templet : function(row){
						if(row.STATUS=="01"){
							var check='checked=""';
						}
						var html='<input type="checkbox" '+check+' name="open" lay-skin="switch" lay-filter="switchStatus" lay-text="'+getI18nValue('启用|禁用')+'" data-id="'+row.ID+'">';
						return html;
					}}
					,{field:'CREATE_TIME', title: '创建时间'}
					,{field:'ID', title: '操作',templet : function(row){
						var html= '<span style=\'color:#fff\' href="javascript:void(0)" class="layui-btn layui-btn-xs" onclick="callSideMenu.upMenu(\''+row.ID+'\', \''+type+'\')">'+getI18nValue('修改')+'</span >';
						if(row.BULIT_IN=="N"){
							html+='<span style=\'color:#fff\' href="javascript:void(0)" class="layui-btn layui-btn-xs layui-btn-danger" onclick="callSideMenu.delMenu(\''+row.ID+'\')">'+getI18nValue('删除')+'</span >';
						}
						return html;
					}}
		         ]
			],
			success:function(){
				//table.resize({id: elemId});
				layui.use('form', function(){
					var form = layui.form; 
					//监听状态开关
					form.on('switch(switchStatus)', function(data){
						var id=this.dataset.id;
						var status="02";
						if(this.checked==true){
							//启用
							status="01";
						}
						callSideMenu.upMenuStatus(id,status);
					});
					$(".layui-table-header th").css("min-width",'150px');
					$(".layui-table-body .layui-table-cell").css("min-width",'200px');
				});
			}
		});
	}
	//新增侧边栏菜单
	callSideMenu.addMenu = function(type){
		var BUSI_ID = "0";
		var BUSI_TYPE = "01";
		popup.layerShow({type:1,title:getI18nValue('新增'),offset:'rb',area:['450px','100%']}
			,'${ctxPath}/pages/system/callin/sidebar/menu-add-layui.jsp?type='+type, {BUSI_ID:BUSI_ID,BUSI_TYPE:BUSI_TYPE});
	}
	//修改侧边栏菜单
	callSideMenu.upMenu = function(id, type){
		var BUSI_ID = "0";
		var BUSI_TYPE = "01";
		popup.layerShow({type:1,title:getI18nValue('修改'),offset:'rb',area:['450px','100%']}
			,'${ctxPath}/pages/system/callin/sidebar/menu-edit-layui.jsp',{id:id,type:type,BUSI_ID:BUSI_ID,BUSI_TYPE:BUSI_TYPE});
	}
	//删除侧边栏菜单
	callSideMenu.delMenu = function(id){
		layer.confirm(getI18nValue('是否删除？'),{icon: 3, title:getI18nValue('提示'),offset:'20px',btn:[getI18nValue('确定'),getI18nValue('取消')]},  function(index){
			layer.close(index);
	  		ajax.remoteCall("${ctxPath}/servlet/sidebarMenu?action=menuDel", {id:id}, function(result) {
	  			if(result.state == 1){
				    layer.msg(result.msg,{icon: 1,time:1200,offset:'40px'},function(){
				    	callSideMenu.searchMenuData(1);
				    	callSideMenu.searchExtendData(1);
				    });
				}else{
					layer.alert(result.msg,{icon: 5});
				}
  			});
		});
	}
	//修改侧边栏菜单状态
	callSideMenu.upMenuStatus = function(id,status){
	  	ajax.remoteCall("${ctxPath}/servlet/sidebarMenu?action=menuStatusEdit", {id:id,status:status}, function(result) {
	  		if(result.state == 1){
			    layer.msg(result.msg,{icon: 1,time:1200,offset:'40px'},function(){
			    	callSideMenu.searchMenuData();
			    	callSideMenu.searchExtendData();
			    });
			}else{
				layer.alert(result.msg,{icon: 5});
			}
  		});
	}
	callSideMenu.preview = function () {
		popup.openTab({url:"${ctxPath}/pages/system/callin/call-index.jsp",title:getI18nValue("弹屏预览"),data:{}});
	}
	callSideMenu.synMenu=function(TYPE){
		layer.confirm(getI18nValue('是否同步系统管理侧边栏配置的来电弹屏信息'), {btn: [getI18nValue('确定'),getI18nValue('取消')]}, 			            
	            function(index){
				  callSideMenu.synMenuMeth(TYPE);
	              return true;
	            },
	            function(index){
	              return false;
	            }
	        );
	}
	callSideMenu.synMenuMeth=function(TYPE){
		var BUSI_ID = "0";
		var BUSI_TYPE = "01";
		var param={};
		param.TYPE=TYPE;
		param.BUSI_ID=BUSI_ID;
		param.BUSI_TYPE=BUSI_TYPE;
	 	ajax.remoteCall("${ctxPath}/servlet/sidebarMenu?action=synCblData", param, function(result) {
	  		if(result.state == 1){
			    layer.msg(result.msg,{icon: 1,time:1200,offset:'40px'},function(){
			    	callSideMenu.searchMenuData(1);
			    	callSideMenu.searchExtendData(1);
			    });
			}else{
				layer.alert(result.msg,{icon: 5});
			}
  		});
	}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>