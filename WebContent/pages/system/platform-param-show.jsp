<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="参数"></title>
	<style>
	    #updatedut{
	        display: none;
	    }
	    .layui-form-item .layui-input-inline{
	        width: 500px;
	        max-width: 800px;
	        min-width: 200px;
	    }
	    label.layui-form-item{
	        width: 200px;
	    }
	    .layui-form-label{width: 200px;}
	    .self-select{
	    	width: 100%;
	    	height: 37px;
	    }
	    .self-radio{
	    	margin: 15px 0 15px 10px!important;
	    }
	</style>
</EasyTag:override>
<EasyTag:override name="content">

	<div id="dataList"  data-template="list-template"></div>
    
	
    
    <script id="list-template" type="text/x-jsrender">
			{{for data}}
			  <div class="layui-form-item" title="{{:ITEM_NAME}}">
			    <label class="layui-form-label" title="{{:ITEM_NAME}}">{{:ITEM_NAME}}</label>
			    <div class="layui-input-inline">
				   {{if ITEM_TYPE=='radio'&&JSON_STRING}}
			      		{{call: fn='renderRadio'}}
				    {{else ITEM_TYPE=='select'&&JSON_STRING}}
			      		{{call: fn='renderSelect'}}
				    {{else ITEM_TYPE=='number'}}
						<input type="number" value="{{:ITEM_VALUE}}" class="layui-input">
				    {{else ITEM_TYPE=='textarea'}}
						<textarea placeholder="{{:ITEM_NAME}}" class="layui-textarea">{{:ITEM_VALUE}}</textarea>
				   {{else}}
						<input type="text" value="{{:ITEM_VALUE}}" class="layui-input">
				    {{/if}}
			    </div>
			  </div>
			{{/for}}
	  </script>
</EasyTag:override>
<EasyTag:override name="script">

<script type="text/javascript">
    $(function(){
	    var data = parent.currentConfig
	    for(var i in data){
	    	data[i] = JSON.parse(data[i]);
	    }
	    var template = $.templates("#list-template");
	    $("#dataList").html(template.render({list:data,data:data}));
	    //$("input").attr("disabled","disabled");
	    //$("select").attr("disabled","disabled");

    });
    String.prototype.replaceAll = function(s1,s2){
        return this.replace(new RegExp(s1,"gm"),s2);
    }
    function renderRadio(row){
        try{
            var v1=row['JSON_STRING'];
            var v3=row['ITEM_VALUE'];
            var v4=row['ITEM_NAME'];
            var html="";
            v1=v1.replaceAll("{","");
            v1=v1.replaceAll("}","");
            var array=v1.split(",");
            for(var index in array){
                var item=array[index];
                var itemArray=item.split(":");
                var val=itemArray[0];
                var checked="";
                if(val==v3)checked="checked";
                if(itemArray.length==1){
                    html+="<input class='self-radio' type='radio'  title='"+itemArray[0]+"' "+checked+" value='"+itemArray[0]+"'/>" + itemArray[0];
                }
                if(itemArray.length==2){
                    html+="<input class='self-radio' type='radio'  title='"+itemArray[1]+"' "+checked+" value='"+itemArray[0]+"'/>" + itemArray[1];
                }
            }
            return html;
        }catch(error){
            console.error(error);
            return '<input type="number" name="'+v2+'" value="'+v3+'" placeholder="'+v4+'" class="layui-input">';
        }
    }
    function renderSelect(row){
        try{
            var v1=row['JSON_STRING'];
            var v3=row['ITEM_VALUE'];
            var v4=row['ITEM_NAME'];
            var html="<select class='self-select'>";
            v1=v1.replaceAll("{","");
            v1=v1.replaceAll("}","");
            var array=v1.split(",");
            for(var index in array){
                var item=array[index];
                var itemArray=item.split(":");
                var val=itemArray[0];
                var selected="";
                if(val==v3)selected="selected";
                if(itemArray.length==1){
                    html+="<option "+selected+" value='"+itemArray[0]+"'>"+itemArray[0]+"</option>";
                }
                if(itemArray.length==2){
                    html+="<option "+selected+" value='"+itemArray[0]+"'>"+itemArray[1]+"</option>";
                }
            }
            html+="</select>";
            return html;
        }catch(error){
            console.error(error);
            return '<input type="number" value="'+v3+'" placeholder="'+v4+'" class="layui-input">';
        }
    }

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>