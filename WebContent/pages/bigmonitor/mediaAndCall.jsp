<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width initial-scale=1">
    <title i18n-lay-html="全媒体和话务指标"></title>
    <link rel="stylesheet" type="text/css" href="css/media-monitor.css?2020060501">
    <style>
        html,
        body {
            /* min-width: 1902px; */
            /* min-height: 1000px; */
            overflow: auto;
        }
    </style>
</head>
<style>
    .dimgray {
        color: dimgray;
        font-size: 12px;
    }

    .pull-right {
        float: right
    }

    .transparent {
        border-color: transparent;
        background-color: transparent;
    }

    #fullpage {
        cursor: pointer
    }

    *::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 4px;
        /*高宽分别对应横竖滚动条的尺寸*/
        height: 4px;
    }

    *::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius: 5px;
        -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        background: rgba(0, 0, 0, 0.2);
    }

    *::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        border-radius: 0;
        background: rgba(0, 0, 0, 0.1);
    }

    .monitor-page {
        min-width: 1700px;
        min-height: 700px
    }

    .callinfo-box .bb {
        width: 170px;
        height: 90px;
        display: inline-block;
        box-sizing: border-box;
        margin: 3px;
        position: relative;
    }

    .bar-box .bar-item[data-type="mini"] .bar-img {
        width: 53px;
        height: 53px;
    }

    .line-bar-box {
        padding-right: 10px;
    }

    .left10 {
        padding-left: 10px;
    }

    .bar-box .bar-item[data-type="mini"] .bar-img {
        width: 105px;
        height: 105px;
    }
</style>

<body class="monitor-page">
    <div class="monitor-page-header">
        <div class="title" i18n-lay-html="在线指标"></div>
    </div>
    <div class="monitor-page-content" style="height: calc(100% - 100px);">
        <div class="monitor-page-rows flex">
            <!-- <div class="monitor-title">
                <span i18n-lay-html="在线指标"></span>
                <span class="dimgray" i18n-lay-html="最近更新时间："></span><span id="mideaUpdateTime" class="dimgray"></span>
                <span id="fullpage" class="dimgray  left10" i18n-lay-html="[全屏]"></span>
            </div> -->
            <div class="flex-item" style="display: flex;flex-direction:column;">
                <div class="flex-row" style="flex: 1;">

                    <div class="flex-item box-m" style="flex: 5;">
                        <div class="monitor-box">
                            <div class="header" i18n-lay-html="当天服务水平"></div>
                            <div class="content">
                                <div class="flex">
                                    <div style="position: relative;margin: 0 auto;margin-top: 50px;">
                                        <div class="bar-box v-t">
                                            <div data-type="mini" class="bar-item">
                                                <div data-type="mini" class="bar-img levelCenter">
                                                    <div class="bar-count" id="agentOnLineNumber">0</div>
                                                </div>
                                                <div class="bar-text" i18n-lay-html="在线坐席数"></div>
                                            </div>
                                            <div data-type="mini" class="bar-item">
                                                <div class="bar-img levelCenter">
                                                    <div class="bar-count" id="avgAgentAnswer">0</div>
                                                </div>
                                                <div class="bar-text" i18n-lay-html="人均接待量"></div>
                                            </div>
                                            <div data-type="mini" class="bar-item">
                                                <div class="bar-img levelCenter">
                                                    <div class="bar-count" id="avgServiceTime">0/min</div>
                                                </div>
                                                <div class="bar-text" i18n-lay-html="平均会话时长"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex-item" style="width: 80%;margin: 0 auto;">
                                        <div class="flex flex-vac" style="padding-left: 20px">
                                            <div data-type="mini" class="line-bar-box">
                                                <div
                                                    style="display: inline-block;width: 20px;overflow: hidden;vertical-align: middle;margin-right: 10px;">
                                                    <img src="images/icon/zxzxs.png"
                                                        style="filter: drop-shadow(20px -3px  #FA4B28);transform: translateX(-20px);" />
                                                </div>
                                                <span i18n-lay-html="满意率"></span> <span class="count"
                                                    style="color: #ec5c19;font-weight: 700;"
                                                    id="satisfGoodRatioData">%</span>
                                                <div class="line">
                                                    <div class="line-persent" id="satisfGoodRatioline"
                                                        style="width: 0%"></div>
                                                </div>
                                            </div>
                                            <div data-type="mini" class="line-bar-box">
                                                <div
                                                    style="display: inline-block;width: 20px;overflow: hidden;vertical-align: middle;margin-right: 10px;">
                                                    <img src="images/icon/rjjdl.png"
                                                        style="filter: drop-shadow(20px -3px  #FA4B28);transform: translateX(-20px);" />
                                                </div>
                                                <span i18n-lay-html="参评率"></span> <span class="count"
                                                    style="color: #ec5c19;font-weight: 700;"
                                                    id="satisfRatioData">%</span>
                                                <div class="line">
                                                    <div class="line-persent" id="satisfRatioLine" style="width: 0%">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div style="flex: 3;" class="box-m">
                        <div class="monitor-box">
                            <div class="header" i18n-lay-html="总体会话情况"></div>
                            <div class="content">
                                <div class="bar-box" style="display: block;margin: 0 auto;">
                                    <div class="bar-item" style="margin-right: 50px;">
                                        <div class="bar-img">
                                            <div class="bar-count" id="todayServerNumber">0</div>
                                        </div>
                                        <div class="bar-text" i18n-lay-html="当日总会话量"></div>
                                    </div>
                                    <div class="bar-item">
                                        <div class="bar-img">
                                            <div class="bar-count" id="nowServerNumber">0</div>
                                        </div>
                                        <div class="bar-text" i18n-lay-html="当前总会话量"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex-item box-m" style="flex: 5;">
                        <div class="monitor-box">
                            <div class="header" i18n-lay-html="当天各渠道汇总"></div>
                            <div class="content">
                                <!-- 表格 -->
                                <table data-layout="fixed" data-align="left" data-border="none" data-zebra="true"
                                    class="monitor-table">
                                    <thead>
                                        <tr class="tabletitle">
                                            <th i18n-lay-html="渠道"></th>
                                            <th i18n-lay-html="会话量"></th>
                                            <th i18n-lay-html="参评率"></th>
                                            <th i18n-lay-html="满意率"></th>
                                        </tr>
                                    </thead>
                                    <tbody id="groupStatisticShow">

                                    </tbody>
                                    <script type="text/x-jsrender" id="groupStatistic">
									{{for}}
										<tr>
                                      	 	<td>{{:channelName}}</td>
                                         	<td>{{:mediaCount}}</td>
                                       	 	<td>{{:satisfRatio}}%</td>
                                       	 	<td>{{:satisfGoodRatio}}%</td>
                                   		 </tr>
									{{/for}}
                                 </script>
                                </table>
                            </div>
                        </div>
                    </div>

                </div>

                <div class="flex-row" style="flex: 1;margin-top: 15px;">
                    <div class="flex-item box-m" style="flex: 5">
                        <div class="monitor-box">
                            <div class="header" i18n-lay-html="坐席实时话务情况"></div>
                            <div class="content">
                                <div id="Mybar" class="chart-area"></div>
                            </div>
                        </div>
                    </div>

                    <div class="flex-item box-m" style="flex: 8">
                        <div class="monitor-box">
                            <div class="header" i18n-lay-html="各技能组在线人数"></div>
                            <div class="content">
                                <!-- 表格 -->
                                <table data-layout="fixed" data-align="left" data-border="none" data-zebra="true"
                                    class="monitor-table">
                                    <thead>
                                        <tr class="tabletitle">
                                            <th i18n-lay-html="技能组"></th>
                                            <th i18n-lay-html="在线人数"></th>
                                            <th i18n-lay-html="总会话量"></th>
                                            <th i18n-lay-html="当前会话量"></th>
                                            <th i18n-lay-html="人均会话量"></th>
                                            <th i18n-lay-html="平均会话时长"></th>
                                            <th i18n-lay-html="参评率"></th>
                                            <th i18n-lay-html="满意率"></th>
                                        </tr>
                                    </thead>
                                    <tbody id="agentSkillShow">

                                    </tbody>
                                    <script type="text/x-jsrender" id="agentSkill">
								{{for}}
									<tr>
                                        <td>{{:groupName}}</td>
                                        <td>{{:agentCount}}</td>
                                        <td>{{:mediaCount}}</td>
                                        <td>{{:mediaCountPresent}}</td>
                                        <td>{{:avgAgentCount}}</td>
                                        <td>{{:avgAgentTime}}</td>
                                        <td>{{:satisfRatio}}</td>
                                        <td>{{:satisfGoodRatio}}</td>
                                    </tr>
								{{/for}}
								</script>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <script src="https://cdn.bootcss.com/jquery/3.4.1/jquery.min.js"></script>
    <script type="text/javascript" src="/easitline-static/js/jsrender.min.js"></script>
    <script type="text/javascript" src="/cc-callmonitor/static/js/my_i18n.js?v=2020070"></script>
    <script type="text/javascript" src="/cc-base/static/js/i18n.js"></script>
    <script type="text/javascript" src="js/echarts.js"></script>
    <script type="text/javascript" src="js/china.js"></script>
    <script type="text/javascript" src="js/radialIndicator.js"></script>
    <script src="js/monitor.js?v=2020060707"></script>
    <script>
        $(window).resize(function () {
            location.reload()
        });
        $("#fullpage").on('click', function (event) {
            requestFullScreen(document.documentElement);
        });
        var t1;
        window.onload = function () {
            getMediaInfo();
            // callInfo();
        }
        function getMediaInfo() {
            $.ajax({
                url: "/cc-callmonitor/servlet/bigMonitor?action=mediaInfo", async: false,
                success: function (result) {
                    if (result.state == 1) {
                        mideaInCallCount(result.info.mediaInfo);// 全媒体指标
                        mediaInfoByChannel(result.info.mediaInfoByChannel);//当天各渠道汇总
                        mediaInfo(result.info.mediaSkillList);//各技能组在线人数
                        $("#mideaUpdateTime").html(result.info.updateTime);
                        getData1()
                    }
                    t1 = setTimeout(function () { getMediaInfo(); }, 2000);
                }, error: function () {
                    t1 = setTimeout(function () { getMediaInfo(); }, 1000);
                }
            });
        }
        //话务监控
        function getData1() {
            $.ajax({
                url: '/cc-callmonitor/servlet/callStat?action=CallMonitor',
                type: 'GET',
                dataType: 'json',
                cache: false,
                success: function (result) {
                    // console.log('/话务监控1111111', result.data.entMonitor)
                    // busyagentcount  置忙
                    // ent_id
                    // idleagentcount   置闲
                    // talk agent count  通话
                    // totalcount 总数
                    // work notreadyagentcount //话后
                    if (result.state) {
                        // $('#Mybar')
                        var xdata = ['置闲', '置忙', '通话', '话后'];
                        var ydata = [result.data.entMonitor.IDLEAGENTCOUNT, result.data.entMonitor.BUSYAGENTCOUNT, result.data.entMonitor.TALKAGENTCOUNT, result.data.entMonitor.WORKNOTREADYAGENTCOUNT];
                        TrafficTimeLeft(xdata, ydata)
                    }
                }
            });
        }

        //话务个时段统计 -- 左侧柱状图
        function TrafficTimeLeft(xdata, ydata) {
            var myChartLeft = echarts.init(document.getElementById('Mybar')); //左侧柱状图
            // 指定图表的配置项和数据
            var optionLeft = {
                title: {
                    // text: '话务各时段统计',
                    x: 'left',
                    textStyle: {//标题颜色
                        color: "#fff",
                        fontSize: '12px'
                    }
                },
                backgroundColor: 'rgba(4,27,76,0.43)',
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                        type: 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
                    }
                },
                grid: {
                    top: '18%',
                    left: '7%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: xdata,
                    //设置坐标轴字体颜色和宽度
                    axisLine: {
                        lineStyle: {
                            color: "#80B8D4",
                        }
                    },
                    axisLabel: {
                        interval: 0,
                        rotate: 30
                    },
                    axisTick: {
                        alignWithLabel: true
                    }
                },
                yAxis: {
                    type: 'value',
                    //设置坐标轴字体颜色和宽度
                    axisLine: {
                        lineStyle: {
                            color: "#80B8D4",
                        }
                    },
                    splitLine: { show: false },
                    // name: '话务各时段统计'
                },
                series: [
                    {
                        // name: '话务统计',
                        type: 'bar',
                        barWidth: '45%',
                        data: ydata,
                        itemStyle: {
                            normal: {
                                color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                                    offset: 0,
                                    color: "#1268f3" // 0% 处的颜色
                                }, {
                                    offset: 0.6,
                                    color: "#08a4fa" // 60% 处的颜色
                                }, {
                                    offset: 1,
                                    color: "#01ccfe" // 100% 处的颜色
                                }], false),
                                label: {
                                    show: true, //开启显示
                                    position: 'top', //在上方显示
                                    textStyle: { //数值样式
                                        color: '#fff',
                                        fontSize: 12
                                    }
                                }
                            },
                        },
                    }
                ],
            };
            // 使用刚指定的配置项和数据显示图表。
            myChartLeft.setOption(optionLeft);
        }
        function mideaInCallCount(mediaInfo) {
            $("#agentOnLineNumber").html(mediaInfo.agentCount + getI18nValue("人"));
            $("#avgAgentAnswer").html(mediaInfo.avgAgentCount);
            $("#avgServiceTime").html(Math.round(mediaInfo.avgAgentTime / 60) + "/min");
            $("#todayServerNumber").html(mediaInfo.mediaCount);
            $("#nowServerNumber").html(mediaInfo.mediaCountPresent);

            $("#satisfGoodRatioData").html(mediaInfo.satisfGoodRatio + '%');
            $("#satisfGoodRatioline").css('width', mediaInfo.satisfGoodRatio + '%');
            $("#satisfRatioData").html(mediaInfo.satisfRatio + '%');
            $("#satisfRatioLine").css('width', mediaInfo.satisfRatio + '%');
        }
        function mediaInfoByChannel(mediaInfoByChannel) {
            var groupStatistic = $.templates("#groupStatistic");
            var html = groupStatistic.render(mediaInfoByChannel);
            $("#groupStatisticShow").html(html);
        }
        function mediaInfoListByAgent(mediaInfoListByAgent) {
            var agentAnswer = $.templates("#agentAnswer");
            var html = agentAnswer.render(mediaInfoListByAgent);
            $("#agentAnswerShow").html(html);
        }
        function mediaInfo(mediaInfo) {
            var agentSkill = $.templates("#agentSkill");
            var html = agentSkill.render(mediaInfo);
            $("#agentSkillShow").html(html);
        }
        function requestFullScreen(element) {
            var requestMethod = element.requestFullScreen || //W3C
                element.webkitRequestFullScreen ||    //Chrome
                element.mozRequestFullScreen || //FireFox
                element.msRequestFullScreen; //IE11
            if (requestMethod) {
                requestMethod.call(element);
            }
            else if (typeof window.ActiveXObject !== "undefined") {//for Internet Explorer
                var wscript = new ActiveXObject("WScript.Shell");
                if (wscript !== null) {
                    wscript.SendKeys("{F11}");
                }
            }
        }



        var skillCallInfotEmplates = $.templates("#skillCallInfo");
        var callInfoListByAgentEmplates = $.templates("#callInfoListByAgent");
        var chartTime = ['00:00', '01:00', '02:00', '03:00', '04:00', '05:00', '06:00', '07:00', '08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00', '21:00', '22:00', '23:00'];
        var t;
        var connSucc15 = $('#connSucc15').radialIndicator({
            barColor: '#FF4917',
            barBgColor: '#06225f',
            barWidth: 7,
            radius: 36,
            initValue: 0,
            roundCorner: true,
            percentage: true,
        });

        var satisfRatio = $("#satisfRatio").radialIndicator({
            barColor: '#00FFE3',
            barBgColor: '#06225f',
            barWidth: 7,
            radius: 36,
            initValue: 0,
            roundCorner: true,
            percentage: true
        });
        var goodSatisfRatio = $("#goodSatisfRatio").radialIndicator({
            barColor: '#FE199E',
            barBgColor: '#06225f',
            barWidth: 7,
            radius: 36,
            initValue: 0,
            roundCorner: true,
            percentage: true
        });


        function callInfo() {
            $.ajax({
                url: "/cc-callmonitor/servlet/bigMonitor?action=callInfo", async: false,
                success: function (result) {
                    if (result.state == 1) {
                        // setInCallCount(result.info.totalCall);//左上 总体话务
                        // serviceLevel(result.info.serviceLevel);//左中 当天服务水平
                        // callInfoByHour(result.info.callInfoByHour);//左下角 当天各时段话务量和接通率
                        // skillCallInfo(result.info.skillCallInfo);//中下 各技能组话务情况
                        // callInfoListByAgent(result.info.callInfoListByAgent);//中下 话务量排行榜
                        // callInfoByQueue(result.info.callInfoByQueue);//右上 排队时长
                        // callInfoByBill(result.info.callInfoByBill);//右中 通话时长
                        // agentStateInfo(result.info.agentStateInfo);//右下 状态分布
                        //provinceCallInfo(result.info.provinceCallInfo)//中上 地图
                        // $("#callUpdateTime").html(result.info.updateTime);
                    }
                    t = setTimeout(function () { callInfo(); }, 2000);
                }, error: function () {
                    t = setTimeout(function () { callInfo(); }, 1000);
                }
            });
        }
        function setInCallCount(totalCall) {
            $("#talk_rate").html(totalCall.connSuccRatio + "%");//接通率	
            $("#talk_call").html(totalCall.inCallCount);//呼入量
            $("#out_talk").html(totalCall.outCallCount);//外呼量
            $("#avg_call_time").html(totalCall.avgCallTime + "S");//平均通话时长
            $("#on_line_agent").html(totalCall.agentCount);//在线人数
        }
        function serviceLevel(serviceLevel) {
            $("#queue_user").html(serviceLevel.queueCount + getI18nValue('人'));//排队人数
            $("#avg_queue_time").html(serviceLevel.avgQueueTime + "S");//平均排队时长
            $("#ave_ring_time").html(serviceLevel.avgAlertingTime + "S");//平均振铃时长
            $("#avg_work_time").html(serviceLevel.avgWorkreadyTime + "S");//平均话后整理时长
            connSucc15.animate(parseFloat(serviceLevel.connSuccRatio15));
            satisfRatio.animate(parseFloat(serviceLevel.satisfRatio));
            goodSatisfRatio.animate(parseFloat(serviceLevel.goodSatisfRatio));
        }
        //话务量和接通率
        var chartTimeData = {
            title: chartTime,
            //接通率
            line: { name: getI18nValue('接通率'), value: [] },
            //接通数
            bar: { name: getI18nValue('接通数'), value: [] }
        }
        function callInfoByHour(callInfoByHour) {
            var callCounts = [];
            var connSuccCounts = [];
            for (var key in callInfoByHour) {
                callCounts[key] = callInfoByHour[key].callCount;
                if (callInfoByHour[key].callCount != 0) {
                    connSuccCounts[key] = (callInfoByHour[key].connSuccCount / callInfoByHour[key].callCount).toFixed(2);
                } else {
                    connSuccCounts[key] = 0;
                }
            }
            chartTimeData.line.value = connSuccCounts;
            chartTimeData.bar.value = callCounts;
            monitorCharts.lineBar('chart-line-bar', chartTimeData);
        }

        function skillCallInfo(data) {
            var html = skillCallInfotEmplates.render(data);
            $("#skillCallInfoShow").html(html);
        }
        function callInfoListByAgent(data) {
            var html = callInfoListByAgentEmplates.render(data);
            $("#callInfoListByAgentShow").html(html);
        }
        //排队时长
        var callInfoByQueueData = {
            title: ['0-5s', '5-10s', '10-15s', '15-20s', '20s' + getI18nValue('以上')],
            value: [
                { name: getI18nValue('排队时长'), value: [0, 0, 0, 0, 0] }
            ]
        };
        function callInfoByQueue(callInfoByQueue) {
            var callCounts = [];
            for (var key in callInfoByQueue) {
                callCounts[key] = callInfoByQueue[key].callCount;
            }
            callInfoByQueueData.value[0] = { name: getI18nValue('排队时长'), value: callCounts };
            monitorCharts.bar('chart-pdsc', callInfoByQueueData);
        }
        //通话时长
        var callInfoByBillData = {
            title: ['0-15s', '15-30s', '30-60s', '1-2min', '2-3min', '3-5min', '5min' + getI18nValue('以上')],
            value: [
                { name: getI18nValue('呼入排队时长'), value: [0, 0, 0, 0, 0, 0, 0] },
                { name: getI18nValue('呼入排队时长'), value: [0, 0, 0, 0, 0, 0, 0] }
            ]
        };
        function callInfoByBill(callInfoByBill) {
            var inConnSuccCounts = [];
            var outConnSuccCounts = [];
            for (var key in callInfoByBill) {
                inConnSuccCounts[key] = callInfoByBill[key].inConnSuccCount;
                outConnSuccCounts[key] = callInfoByBill[key].outConnSuccCount;
            }
            callInfoByBillData.value[0] = { name: getI18nValue('呼入排队时长'), value: inConnSuccCounts };
            callInfoByBillData.value[1] = { name: getI18nValue('呼出通话时长'), value: outConnSuccCounts };
            monitorCharts.bar('chart-thsc', callInfoByBillData);
        }
        function agentStateInfo(agentStateInfo) {
            var agentStateInfoData = [];
            for (var key in agentStateInfo) {
                agentStateInfoData[key] = { name: getI18nValue(agentStateInfo[key].agentState), value: agentStateInfo[key].agentCount };
            }
            monitorCharts.pie('chart-zxzt', agentStateInfoData);
        }
        function provinceCallInfo(provinceCallInfo) {
            var mapData = []
            for (var key in provinceCallInfo) {
                var provinceName = provinceCallInfo[key].provinceName.replace('省', '')
                mapData[key] = {
                    name: provinceName,
                    value: provinceCallInfo[key].connSuccCount,
                }
            }
            monitorCharts.map('chart-map', mapData, true);
        }
    </script>
</body>

</html>