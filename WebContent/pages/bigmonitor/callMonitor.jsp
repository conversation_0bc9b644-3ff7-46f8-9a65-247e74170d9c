<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width initial-scale=1">
    <title i18n-lay-html="话务指标"></title>

    <link rel="stylesheet" type="text/css" href="css/media-monitor.css">
</head>
<style>
    .monitor-page1 {
        min-width: 1800px;
        min-height: 900px
    }

    .dimgray {
        color: dimgray;
        font-size: 12px;
    }

    .pull-right {
        float: right
    }

    .transparent {
        border-color: transparent;
        background-color: transparent;
    }

    #fullpage {
        cursor: pointer
    }

    .monitor-box .content1 {
        overflow: inherit
    }

    .monitor-title {
        position: absolute;
        top: 6px;
        left: 15px;
    }

    .monitor-clock {
        position: absolute;
        top: 15px;
        right: 15px;
    }

    @media screen and (max-width: 1500px) {

        .monitor-table[data-align="left"] td,
        .monitor-table[data-align="left"] th {
            font-size: 12px
        }
    }


    .callinfo-box .bb {
        width: 45%;
        height: 130px;
    }

    .callinfo-box .b-c {
        width: 23%;
        height: 130px;
    }
    #box2 .b-c .count {
      /* position: relative;
      left: 0;
      top: 0;
      transform: none; */
    }
</style>

<body class="monitor-page">
    <div class="flex">
        <div class="monitor-page-header">
            <div class="monitor-title">
                <!-- <span i18n-lay-html="话务指标"></span> -->
                <span class="dimgray" i18n-lay-html="最近更新时间："></span><span id="updateTime" class="dimgray"></span>
                <span id="fullpage" class="dimgray " i18n-lay-html="[全屏]"></span>
            </div>
            <div class="title" i18n-lay-html="话务指标"></div>
            <div id="clockTime" class="clock monitor-clock"></div>
        </div>
        <div class="monitor-page-content flex-item">
            <div class="monitor-page-rows flex">
                <div class="flex-item" style="flex: 2">
                    <div class="flex-row">
                        <div class="flex-item">
                            <div style="display: flex;height: 100%;">

                                <div class="flex-item box-m" style="flex: 2;">
                                    <div class="monitor-box">
                                        <div class="header" style="display: flex;align-items: center;justify-content: space-between;">
                                          <span i18n-lay-html="总体话务" id="text" data-type="1"></span>
                                          <img src="images/switch.png" id="switchBtn" alt="" style="width: 16px;height: 16px;cursor: pointer;">
                                        </div>
                                        <div class="content">
                                            <div class="flex flex-vac"
                                                style="width: 80%;margin: 0 auto;min-width: 284px;" id="box1">
                                                <div class="callinfo-box zoomBox" style="position: relative;">
                                                    <div class="b-c">
                                                        <div class="count">
                                                            <span id="talk_rate"
                                                                style="color:red;font-size:24px;"></span>
                                                            <label i18n-lay-html="接通率"></label>
                                                        </div>
                                                    </div>
                                                    <div class="bb b-1">
                                                        <div class="count">
                                                            <span class="titleTraffic" id="talk_call"></span>
                                                            <label i18n-lay-html="呼入量"></label>
                                                        </div>
                                                    </div>
                                                    <div class="bb b-2">
                                                        <div class="count">
                                                            <span class="titleTraffic" id="out_talk"></span>
                                                            <label i18n-lay-html="呼出量"></label>
                                                        </div>
                                                    </div>
                                                    <div class="bb b-4">
                                                        <div class="count">
                                                            <span class="titleTraffic" id="avg_call_time"></span>
                                                            <label i18n-lay-html="平均通话时长(秒)"></label>
                                                        </div>
                                                    </div>
                                                    <div class="bb b-3">
                                                        <div class="count">
                                                            <span class="titleTraffic" id="on_line_agent"></span>
                                                            <label i18n-lay-html="在线坐席数(人)"></label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>


                                            <div class="flex flex-vac"
                                                style="width: 80%;margin: 0 auto;min-width: 284px;" id="box2">
                                                <div class="callinfo-box zoomBox" style="position: relative;">
                                                    <div class="b-c">
                                                        <div class="count">
                                                            <span id="CALL_COUNT_RATE"
                                                                style="color:red;font-size:24px;">0.0</span>
                                                            <label i18n-lay-html="接通率"></label>
                                                        </div>
                                                        <!-- <div class="count">
                                                          <span id="SATISF_COUNT_RATE"
                                                              style="color:red;font-size:24px;">0.0</span>
                                                          <label i18n-lay-html="满意率"></label>
                                                        </div> -->
                                                    </div>
                                                    <div class="bb b-1">
                                                        <div class="count">
                                                            <span class="titleTraffic" id="CALL_COUNT"></span>
                                                            <label i18n-lay-html="呼入量"></label>
                                                        </div>
                                                    </div>
                                                    <div class="bb b-2">
                                                        <div class="count">
                                                            <span class="titleTraffic" id="CONN_SUCC_COUNT"></span>
                                                            <label i18n-lay-html="接通量"></label>
                                                        </div>
                                                    </div>
                                                    <div class="bb b-4">
                                                        <div class="count">
                                                            <span class="titleTraffic" id="SATISF_COUNT"></span>
                                                            <label i18n-lay-html="评价量"></label>
                                                        </div>
                                                    </div>
                                                    <div class="bb b-3">
                                                        <div class="count">
                                                            <span class="titleTraffic" id="GOOD_COUNT"></span>
                                                            <label i18n-lay-html="满意量"></label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="flex-item box-m" style="flex: 5">
                                    <div class="monitor-box">
                                        <div class="header" i18n-lay-html="当天各时段话务量和接通率"></div>
                                        <div class="content">
                                            <div id="chart-line-bar" class="chart-area"></div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>

                    </div>
                </div>



                <div class="flex-item" style="flex: 2;display: flex;">

                    <div class="flex-item box-m" style="flex: 2;">
                        <div class="monitor-box">
                            <div class="header" i18n-lay-html="当天服务水平"></div>
                            <div class="content">
                                <div class="flex flex-vac">
                                    <div class="zoomBox" style="width: 80%;margin: 0 auto;">
                                        <div class="s-line-box" style="text-align: center;">
                                            <div data-type="mini" class="line-bar-box">
                                                <img src="images/icon/zxzxs.png" /><span i18n-lay-html="当前排队人数"></span>
                                                <span class="count" id="queue_user">0人</span>
                                            </div>
                                            <div data-type="mini" class="line-bar-box">
                                                <img src="images/icon/pdsc.png" /><span i18n-lay-html="平均排队时长"></span>
                                                <span class="count" id="avg_queue_time">0s</span>
                                            </div>
                                            <div data-type="mini" class="line-bar-box line-bar-box-down">
                                                <img src="images/icon/zlsc.png" /><span i18n-lay-html="平均振铃时长"></span>
                                                <span class="count" id="ave_ring_time">0s</span>
                                            </div>
                                            <div data-type="mini" class="line-bar-box line-bar-box-down">
                                                <img src="images/icon/hhzlsc.png" /><span i18n-lay-html="平均整理时长"></span>
                                                <span class="count" id="avg_work_time">0s</span>
                                            </div>
                                        </div>
                                        <div class="bar-box v-t" style="width: 100%;display:block;margin: 0 auto;">
                                            <div data-type="mini" class="bar-item">
                                                <div class="">
                                                    <div class="bar-count" id="connSucc15"></div>
                                                </div>
                                                <div class="bar-text" i18n-lay-html="15s接通率"></div>
                                            </div>
                                            <div data-type="mini" class="bar-item">
                                                <div class="">
                                                    <div class="bar-count" id="satisfRatio"></div>
                                                </div>
                                                <div class="bar-text" i18n-lay-html="参评率"></div>
                                            </div>
                                            <div data-type="mini" class="bar-item">
                                                <div class="">
                                                    <div class="bar-count" id="goodSatisfRatio"></div>
                                                </div>
                                                <div class="bar-text" i18n-lay-html="好评率"></div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="flex-item box-m" style="flex: 5;">
                        <div class="monitor-box">
                            <div class="header" i18n-lay-html="坐席实时话务情况"></div>
                            <div class="content">
                                <div id="Mybar" class="chart-area"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="${ctxPath}/static/js/jquery-1.9.1.min.js"></script>
    <script type="text/javascript" src="/easitline-static/js/jsrender.min.js"></script>
    <script type="text/javascript" src="/cc-callmonitor/static/js/my_i18n.js?v=2020070"></script>
    <script type="text/javascript" src="/cc-base/static/js/i18n.js"></script>
    <script type="text/javascript" src="js/echarts.js"></script>
    <script type="text/javascript" src="js/china.js"></script>
    <script type="text/javascript" src="js/radialIndicator.js"></script>

    <script src="js/monitor.js"></script>
    <script type="text/javascript">
        //时间
        // $(function(){
        //     clockTime()
        //     $(window).on('resize',pageResize)
        //     pageResize()
        // })

        $(window).resize(function () {
            location.reload()
        });

        $('#switchBtn').on('click', function() {
          console.log('切换')
          var type = $('#text').data('type')
          if (type == 1) {
            $('#text').data('type', 2)
            $('#text').html('客服专线')
            $('#box1').hide()
            $('#box2').show()
          } else {
            $('#text').data('type', 1)
            $('#text').html('总体话务')
            $('#box2').hide()
            $('#box1').show()
          }
        })

        //屏幕大小变动
        function pageResize() {
            var pageWidth = $(window).width()
            if (pageWidth >= 1800) {
                $('.zoomBox').css('zoom', 1)
            } else {
                var zoom = 0.9 * ((pageWidth / 1800).toFixed(1))
                $('.zoomBox').css('zoom', zoom)
            }
        }

        $("#fullpage").on('click', function (event) {
            requestFullScreen(document.documentElement);
        });
        var skillCallInfoEmplates = $.templates("#skillCallInfo");
        var callInfoListByAgentEmplates = $.templates("#callInfoListByAgent");
        var chartTime = ['00:00', '01:00', '02:00', '03:00', '04:00', '05:00', '06:00', '07:00', '08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00', '21:00', '22:00', '23:00'];
        var t;
        var t2;
        var connSucc15 = radialIndicator('#connSucc15', {
            barColor: '#FF4917',
            barBgColor: '#06225f',
            barWidth: 7,
            radius: 36,
            initValue: 0,
            roundCorner: true,
            percentage: true,
        });

        var satisfRatio = radialIndicator('#satisfRatio', {
            barColor: '#00FFE3',
            barBgColor: '#06225f',
            barWidth: 7,
            radius: 36,
            initValue: 0,
            roundCorner: true,
            percentage: true,
        });
        var goodSatisfRatio = radialIndicator('#goodSatisfRatio', {
            barColor: '#FE199E',
            barBgColor: '#06225f',
            barWidth: 7,
            radius: 36,
            initValue: 0,
            roundCorner: true,
            percentage: true,
        });


        window.onload = function () {
            callInfo();
            agentSpecial();
        }
        function callInfo() {
            $.ajax({
                url: "/cc-callmonitor/servlet/bigMonitor?action=callInfo", async: false,
                success: function (result) {
                    if (result.state == 1) {
                        setInCallCount(result.info.totalCall);//左上
                        serviceLevel(result.info.serviceLevel);//左下
                        callInfoByHour(result.info.callInfoByHour);//右上

                        $("#updateTime").html(result.info.updateTime);
                        getData1()
                    }
                    t = setTimeout(function () { callInfo(); }, 2000);
                }, error: function () {
                    t = setTimeout(function () { callInfo(); }, 1000);
                }
            });
        }

        function agentSpecial() {
          $.ajax({
                url: "/cx-mix/servlet/monitorEx?action=agentSpecial", async: false,
                success: function (result) {
                    if (result.state == 1) {
                        var data = result.specialInfo
                        $('#CALL_COUNT').html(data.CALL_COUNT)
                        $('#CONN_SUCC_COUNT').html(data.CONN_SUCC_COUNT)
                        $('#SATISF_COUNT').html(data.SATISF_COUNT)
                        $('#GOOD_COUNT').html(data.GOOD_COUNT)
                        var CALL_COUNT_RATE = '0.0'
                        var SATISF_COUNT_RATE = '0.0'
                        if (Number(data.CALL_COUNT) === 0 || Number(data.CONN_SUCC_COUNT) === 0) {
                          CALL_COUNT_RATE = '0.0'
                        } else {
                          CALL_COUNT_RATE = (Number(data.CONN_SUCC_COUNT) * 100 / Number(data.CALL_COUNT)).toFixed(2)
                        }
                        if (Number(data.GOOD_COUNT) === 0 || Number(data.SATISF_COUNT) === 0) {
                          SATISF_COUNT_RATE = '0.0'
                        } else {
                          SATISF_COUNT_RATE = (Number(data.GOOD_COUNT) * 100 / Number(data.SATISF_COUNT)).toFixed(2)
                        }
                        $('#CALL_COUNT_RATE').html(CALL_COUNT_RATE)
                        $('#SATISF_COUNT_RATE').html(SATISF_COUNT_RATE)
                    } else {
                        $('#CALL_COUNT').html(0)
                        $('#CONN_SUCC_COUNT').html(0)
                        $('#SATISF_COUNT').html(0)
                        $('#GOOD_COUNT').html(0)
                        $('#CALL_COUNT_RATE').html('0.0')
                        $('#SATISF_COUNT_RATE').html('0.0')
                    }
                    if ($('#text').data('type') == 2) {
                      t2 = setTimeout(function () { agentSpecial(); }, 2000);
                    } else {
                      clearTimeout(t2)
                    }
                }, error: function () {
                    if ($('#text').data('type') == 2) {
                      t2 = setTimeout(function () { agentSpecial(); }, 2000);
                    } else {
                      clearTimeout(t2)
                    }
                }
            });
        }

        //话务监控
        function getData1() {
            $.ajax({
                url: '/cc-callmonitor/servlet/callStat?action=CallMonitor',
                type: 'GET',
                dataType: 'json',
                cache: false,
                success: function (result) {
                    // console.log('/话务监控1111111', result.data.entMonitor)
                    // busyagentcount  置忙
                    // ent_id
                    // idleagentcount   置闲
                    // talk agent count  通话
                    // totalcount 总数
                    // work notreadyagentcount //话后
                    if (result.state) {
                        // $('#Mybar')
                        var xdata = ['置闲', '置忙', '通话', '话后'];
                        var ydata = [result.data.entMonitor.IDLEAGENTCOUNT, result.data.entMonitor.BUSYAGENTCOUNT, result.data.entMonitor.TALKAGENTCOUNT, result.data.entMonitor.WORKNOTREADYAGENTCOUNT];
                        TrafficTimeLeft(xdata, ydata)
                    }
                }
            });
        }

        //话务个时段统计 -- 左侧柱状图
        function TrafficTimeLeft(xdata, ydata) {
            var myChartLeft = echarts.init(document.getElementById('Mybar')); //左侧柱状图
            // 指定图表的配置项和数据
            var optionLeft = {
                title: {
                    // text: '话务各时段统计',
                    x: 'left',
                    textStyle: {//标题颜色
                        color: "#fff",
                        fontSize: '12px'
                    }
                },
                backgroundColor: 'rgba(4,27,76,0.43)',
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                        type: 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
                    }
                },
                grid: {
                    top: '18%',
                    left: '7%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: xdata,
                    //设置坐标轴字体颜色和宽度
                    axisLine: {
                        lineStyle: {
                            color: "#80B8D4",
                        }
                    },
                    axisLabel: {
                        interval: 0,
                        rotate: 30
                    },
                    axisTick: {
                        alignWithLabel: true
                    }
                },
                yAxis: {
                    type: 'value',
                    //设置坐标轴字体颜色和宽度
                    axisLine: {
                        lineStyle: {
                            color: "#80B8D4",
                        }
                    },
                    splitLine: { show: false },
                    // name: '话务各时段统计'
                },
                series: [
                    {
                        // name: '话务统计',
                        type: 'bar',
                        barWidth: '45%',
                        data: ydata,
                        itemStyle: {
                            normal: {
                                color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                                    offset: 0,
                                    color: "#1268f3" // 0% 处的颜色
                                }, {
                                    offset: 0.6,
                                    color: "#08a4fa" // 60% 处的颜色
                                }, {
                                    offset: 1,
                                    color: "#01ccfe" // 100% 处的颜色
                                }], false),
                                label: {
                                    show: true, //开启显示
                                    position: 'top', //在上方显示
                                    textStyle: { //数值样式
                                        color: '#fff',
                                        fontSize: 12
                                    }
                                }
                            },
                        },
                    }
                ],
            };
            // 使用刚指定的配置项和数据显示图表。
            myChartLeft.setOption(optionLeft);
        }


        function setInCallCount(totalCall) {
            $("#talk_rate").html(totalCall.connSuccRatio);//接通率	
            $("#talk_call").html(totalCall.inCallCount);//呼入量
            $("#out_talk").html(totalCall.outCallCount);//外呼量
            $("#avg_call_time").html(totalCall.avgCallTime + "S");//平均通话时长
            $("#on_line_agent").html(totalCall.agentCount);//在线人数
        }
        function serviceLevel(serviceLevel) {
            $("#queue_user").html(parseInt(serviceLevel.queueCount == null ? 0 : serviceLevel.queueCount) + getI18nValue("人"));//排队人数
            $("#avg_queue_time").html(serviceLevel.avgQueueTime + "S");//平均排队时长
            $("#ave_ring_time").html(serviceLevel.avgAlteringTime + "S");//平均振铃时长
            $("#avg_work_time").html(serviceLevel.avgWorkreadyTime + "S");//平均话后整理时长
            connSucc15.animate(parseFloat(serviceLevel.connSuccRatio15));
            satisfRatio.animate(parseFloat(serviceLevel.satisfRatio));
            goodSatisfRatio.animate(parseFloat(serviceLevel.goodSatisfRatio));
        }
        //话务量和接通率
        var chartTimeData = {
            title: chartTime,
            //接通率
            line: { name: getI18nValue('接通率'), value: [] },
            //接通数
            bar: { name: getI18nValue('接通数'), value: [] }
        }
        function callInfoByHour(callInfoByHour) {
            var callCounts = [];
            var connSuccCounts = [];
            for (var key in callInfoByHour) {
                callCounts[key] = callInfoByHour[key].inCallCount;
                if (callInfoByHour[key].inCallCount != 0) {
                    connSuccCounts[key] = (callInfoByHour[key].inConnSuccCount / callInfoByHour[key].inCallCount).toFixed(2);;
                } else {
                    connSuccCounts[key] = 0;
                }
            }
            chartTimeData.line.value = connSuccCounts;
            chartTimeData.bar.value = callCounts;
            monitorCharts.lineBar('chart-line-bar', chartTimeData);
        }


        function skillCallInfo(data) {
            var html = skillCallInfoEmplates.render(data);
            $("#skillCallInfoShow").html(html);
        }
        function callInfoListByAgent(data) {
            var html = callInfoListByAgentEmplates.render(data);
            $("#callInfoListByAgentShow").html(html);
        }
        //排队时长
        var callInfoByQueueData = {
            title: ['0-5s', '5-10s', '10-15s', '15-20s', '20s' + getI18nValue('以上')],
            value: [
                { name: getI18nValue('排队时长'), value: [0, 0, 0, 0, 0] }
            ]
        };
        function callInfoByQueue(callInfoByQueue) {
            var callCounts = [];
            for (var key in callInfoByQueue) {
                callCounts[key] = callInfoByQueue[key].callCount;
            }
            callInfoByQueueData.value[0] = { name: getI18nValue('排队时长'), value: callCounts };
            monitorCharts.bar('chart-pdsc', callInfoByQueueData);
        }
        //通话时长
        var callInfoByBillData = {
            title: ['0-15s', '15-30s', '30-60s', '1-2min', '2-3min', '3-5min', '5min' + getI18nValue('以上')],
            value: [
                { name: getI18nValue('呼入排队时长'), value: [0, 0, 0, 0, 0, 0, 0] },
                { name: getI18nValue('呼出通话时长'), value: [0, 0, 0, 0, 0, 0, 0] }
            ]
        };
        function callInfoByBill(callInfoByBill) {
            var inConnSuccCounts = [];
            var outConnSuccCounts = [];
            for (var key in callInfoByBill) {
                inConnSuccCounts[key] = callInfoByBill[key].inConnSuccCount;
                outConnSuccCounts[key] = callInfoByBill[key].outConnSuccCount;
            }
            callInfoByBillData.value[0] = { name: getI18nValue('呼入排队时长'), value: inConnSuccCounts };
            callInfoByBillData.value[1] = { name: getI18nValue('呼出通话时长'), value: outConnSuccCounts };
            monitorCharts.bar('chart-thsc', callInfoByBillData);
        }
        function agentStateInfo(agentStateInfo) {
            var agentStateInfoData = [];
            for (var key in agentStateInfo) {
                agentStateInfoData[key] = { name: getI18nValue(agentStateInfo[key].agentState), value: agentStateInfo[key].agentCount };
            }
            monitorCharts.pie('chart-zxzt', agentStateInfoData);
        }
        function provinceCallInfo(provinceCallInfo) {
            var mapData = []
            for (var key in provinceCallInfo) {
                var provinceName = provinceCallInfo[key].provinceName.replace('省', '')
                mapData[key] = {
                    name: provinceName,
                    value: provinceCallInfo[key].connSuccCount,
                }
            }
            monitorCharts.map('chart-map', mapData, true);
        }
        function requestFullScreen(element) {
            var requestMethod = element.requestFullScreen || //W3C
                element.webkitRequestFullScreen ||    //Chrome
                element.mozRequestFullScreen || //FireFox
                element.msRequestFullScreen; //IE11
            if (requestMethod) {
                requestMethod.call(element);
            }
            else if (typeof window.ActiveXObject !== "undefined") {//for Internet Explorer
                var wscript = new ActiveXObject("WScript.Shell");
                if (wscript !== null) {
                    wscript.SendKeys("{F11}");
                }
            }
        }
    </script>
</body>

</html>