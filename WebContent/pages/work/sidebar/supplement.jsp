<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>弹屏</title>
	<style type="text/css">
		.container-fluid {
			height: 100%;
			padding: 20px;
		}

		.clearfix:after {
			content: '';
			height: 0;
			line-height: 0;
			display: block;
			visibility: hidden;
			clear: both;
		}

		.shadow {
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-moz-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
		}
	</style>
	<link rel="stylesheet" type="text/css" href="/easitline-static/lib/layui/css/layui.css" />
</EasyTag:override>
<EasyTag:override name="content">
	<div style="width: 100%;height: 100%;">
		<form id="supplementForm" pclass="layui-form"  method="post">
			<input type="hidden"  name="contractNumber" id="contractNumber" value="${param.contractNumber}">
			<div class="layui-form-item">
				<label class="layui-form-label" style="width:100px;">绿本编号</label>
				<div class="layui-input-block" style="margin-left:100px;width:70%;">
					<input type="text" disabled="disabled" name="VRC_NO" required lay-verify="required" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label" style="width:100px;">产证地区</label>
				<div class="layui-input-block" style="margin-left:100px;width:70%;">
					<input type="text" disabled="disabled" name="PRODUCING_AREA" required lay-verify="required" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label" style="width:100px;">车牌号</label>
				<div class="layui-input-block" style="margin-left:100px;width:70%;">
					<input type="text" disabled="disabled" name="NUMBER_PLATE" required lay-verify="required" autocomplete="off" class="layui-input">
				</div>
			 </div> 
			<div class="layui-form-item">
				<label class="layui-form-label" style="width:100px;">发动机号</label>
				<div class="layui-input-block" style="margin-left:100px;width:70%;">
					<input type="text" disabled="disabled" name="ENGINENO" required lay-verify="required" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label" style="width:100px;">车架号</label>
				<div class="layui-input-block" style="margin-left:100px;width:70%;">
					<input type="text" disabled="disabled" name="VINNO" required lay-verify="required" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label" style="width:100px;">收件公司</label>
				<div class="layui-input-block" style="margin-left:100px;width:70%;">
					<input type="text" disabled="disabled" name="EXP_COMPANY" required lay-verify="required" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label" style="width:100px;">收件人</label>
				<div class="layui-input-block" style="margin-left:100px;width:70%;">
					<input type="text" disabled="disabled" name="EXP_LIAISON" required lay-verify="required" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label" style="width:100px;">收件地址</label>
				<div class="layui-input-block" style="margin-left:100px;width:70%;">
					<input type="text" disabled="disabled" name="EXP_ADDRESS" required lay-verify="required" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label" style="width:100px;">收件人电话</label>
				<div class="layui-input-block" style="margin-left:100px;width:70%;">
					<input type="text" disabled="disabled" name="EXP_PHONE_NUM" required lay-verify="required" autocomplete="off" class="layui-input">
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label" style="width:100px;">快递号</label>
				<div class="layui-input-block" style="margin-left:100px;width:70%;">
					<input type="text" disabled="disabled" name="EXP_NUM" required lay-verify="required" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label" style="width:100px;">邮递日期</label>
				<div class="layui-input-block" style="margin-left:100px;width:70%;">
					<input type="text" disabled="disabled" name="EXP_DATE_ID" required lay-verify="required" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label" style="width:100px;">未入库核查</label>
				<div class="layui-input-block" style="margin-left:100px;width:70%;">
					<input type="text" disabled="disabled" name="STORAGE_FLAG" required lay-verify="required" autocomplete="off" class="layui-input">
				</div>
			</div>
		</form>
	</div>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/layui/layui.js"></script>
	<script type="text/javascript">
		layui.use(['form','element'], function() {
			var form = layui.form;
			var element = layui.element;
		});
	
		$(function(){
			supplementInfoFun.initData();
		});
		
		var supplementInfoFun = {
			contractNumber: '${param.contractNumber}',
			initData: function() {
				if(top.contractArr) {
					var data = top.contractArr['${param.chatSessionId}'];
					if(data) {
						supplementInfoFun.contractNumber = data.CONTRACT_NUMBER;
					}
				}
				ajax.remoteCall("/cx-mix/servlet/supplementServlet?action=supplementInfo", {contractNumber:supplementInfoFun.contractNumber, queryType: "3"}, function(result) {
					if (result.state == 1) {
						fillRecord(result.data);
					} else {
						console.log(result);
					}
				});
			}
		}
		function reloadInfo(data) {
			console.log('刷新数据补录侧边栏数据:' + data);
			supplementInfoFun.contractNumber = data.CONTRACT_NUMBER;
			supplementInfoFun.initData();
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
