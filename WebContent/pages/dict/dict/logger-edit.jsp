<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title><span i18n-title="审核" ></span></title>
	<style>
		input:-webkit-autofill {  
	    -webkit-box-shadow: 0 0 0px 1000px white inset;  
		}  
		.select2-selection__rendered{text-align: left;}
		.select2-container--bootstrap{width: inherit!important;z-index: 100000000}
		.select2-container--bootstrap .select2-selection{font-size: 13px;}
		.select2-selection{background-color: #fff!important;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="easyform"  method="post" data-pk="${param.idfiles}">
				  <input type="hidden" name="idfiles" value="${param.idfiles}">
				 
	                   <table class="table table-edit table-vzebra mt-10" >
	                    <tbody>
	                         <tr>
			                      <td width="60px" i18n-content="审核通过"></td>
			                      <td>
			                        	 <label class="radio radio-info radio-inline">
				                        	<input type="radio" value="Y" checked="checked" name="AUDIT_RESULT" ><span i18n-content="通过"></span>
				                        </label>
				                        <label class="radio radio-info radio-inline">
				                        	<input type="radio" value="N" name="AUDIT_RESULT" ><span i18n-content="不通过"></span>
				                        </label>
			                      </td>
		                     </tr>
			                 <tr>
			                      <td i18n-content="备注"></td>
			                      <td colspan="3">
			                        	<textarea rows="3" maxlength="4000" data-rules="required" class="form-control input-sm" name="AUDIT_DESC"></textarea>
			                      </td>
		                     </tr>
			                  
	                    </tbody>
	                </table>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="loggerEdit.ajaxSubmitForm()" i18n-content="保存"></button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)" i18n-content="关闭"></button>
				   </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	
	jQuery.namespace("loggerEdit");
	
	
	$(function(){
		$("#easyform").render();
		execI18n();
	})
	loggerEdit.ajaxSubmitForm = function(){
		 if(form.validate("#easyform")){
			loggerEdit.insertData(); 
		 }else{
			 i18nTooltip();
		 }
	}
	loggerEdit.insertData = function() {
			var data = form.getJSONObject("#easyform");
			ajax.remoteCall("${ctxPath}/servlet/logger?action=add",data,function(result) { 
				if(result.state == 1){
					loggerList.loadData();
					popup.layerClose("#easyform");
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			  }
			);
		}
	
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>