<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title i18n-content="红名单管理"></title>
</EasyTag:override>
<EasyTag:override name="content">
	<form action="" method="post" name="searchForm" class="form-inline"
		id="searchForm" onsubmit="return false" data-toggle="">
		<div class="ibox">
			<div class="ibox-title clearfix" id="divId">
				<div class="form-group">
					<h5 i18n-content="红名单管理"></h5>
					
					<div class="input-group input-group-sm pull-right btn-group ">
						<EasyTag:res resId="cc-base-system-hbmd-red">
						<button  id="red_min_add"  type="button" class="btn btn-sm btn-success btn-outline" onclick="red.add()" i18n-content="新增"></button>
						<button  id="red_min_delete" type="button" class="btn btn-sm btn-danger btn-outline" onclick="red.dels()" i18n-content="删除"></button>
						<button  id="red_min_input"  type="button" class="btn btn-sm btn-info btn-outline" onclick="red.importData()" i18n-content="导入"></button>
						<button  id="red_min_output"  type="button" class="btn btn-sm btn-info btn-outline" onclick="red.downloadExl()" i18n-content="导出"></button>
						</EasyTag:res>
					</div>
				</div>
				<div class="form-group">
					<div class="input-group input-group-sm">
						<span class="input-group-addon" i18n-content="用户号码"></span> <input type="text"
							name="PHONENUM" class="form-control input-sm" style="width: 120px"
							value="">
					</div>
					<div class="input-group input-group-sm">
						<span class="input-group-addon" i18n-content="红名单等级"></span> <select
							class="form-control input-sm" name="LEVEL_CODE"  data-mars="common.getDict(RED_LIST_LEVEL)" onchange="">
							<option value="" i18n-content="请选择"></option>
						</select>
					</div>
					<div class="input-group input-group-sm">
						<span class="input-group-addon" i18n-content="创建时间"></span> 
						<input type="text" class="form-control input-sm" id="getStartDate" name="getStartDate" style="width:140px" autocomplete="off"> 
						<span class="input-group-addon">~</span> 
						<input type="text" class="form-control input-sm" id="getEndDate" name="getEndDate" style="width:140px" autocomplete="off"> 
					</div>
					
					<div class="input-group input-group-sm">
						<button type="button" class="btn btn-sm btn-default" onclick="red.searchData('1')"><span class="glyphicon glyphicon-search"></span> <span i18n-content="查询" ></span> </button>
					</div>
					<div class="input-group ">
						<button type="button" class="btn btn-sm btn-default" onclick="red.reset()"><span class="glyphicon glyphicon-repeat"></span> <span i18n-content="重置" ></span></button>
					</div>
				</div>
			</div>
			<div class="ibox-content">
				<table id="main"></table>
			</div>
		</div>
	</form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		jQuery.namespace("red");
		requreLib.setplugs('wdate')
		$(function() {
			$("#searchForm").render({
				success : function() {
				//加载时间控件
				layui.use('laydate', function(){
			  		var laydate = layui.laydate;
			  		laydate.render({ elem: '#getStartDate' ,type: 'datetime',lang:getDateLang()});
			  		laydate.render({ elem: '#getEndDate' ,type: 'datetime',lang:getDateLang()});
				});
				}
			});
			red.initData();
		})
		
		//初始化加载数据
		red.initData = function(){
			$("#searchForm").initTableEx({
				 mars:'red.redlist',
				 id:'main',
				 limit:15,
				 limits:[15,30,50,70,100],
				 height:'full-180',
				 cols: [
					[
					  {minWidth:60,align:'center',field:'ID', title: '选择', type:'checkbox'}
			         ,{minWidth:60,align:'center',field:'ID', title: '序号', type:'numbers'}
			         ,{minWidth:120,align:'center',field:'LIST_CLASS', title: '名单类型',templet:function(row){
			        	 return  getDictTextByCode('LIST_CLASS',row.LIST_CLASS);
			         }}
			         ,{minWidth:180,align:'center',field:'PHONENUM', title: '号码'}
			         ,{minWidth:200,align:'center',field:'MEMO', title: '备注'}, {
							width: 100,
							field: 'TIMEOUT_STATE',
							title: '是否过期',
							sort: true,
							templet: function(row) {
								var html = "<span class='layui-badge-dot layui-bg-gray' style='background-color:red !important;'></span> "+getI18nValue("过期")+" ";
								if (row.TIMEOUT_STATE == "Y") { 
									html = "<span class='layui-badge-dot layui-bg-green'></span> "+getI18nValue("有效")+" ";
								}
								return html;

							}
						}
			         ,{minWidth:120,align:'center',field:'LIMIT_TIME', title: '过期时间',sort: true}
			         ,{minWidth:180,align:'center',field:'CREATE_TIME', title: '创建时间',sort: true}
			         ,{minWidth:120,align:'center',field:'CREATOR', title: '创建人'}
			         ,{minWidth:130,align:'center',field:'LEVEL_CODE', title: '红名单等级标识',templet:function(row){
			        	 return  getDictTextByCode('RED_LIST_LEVEL',row.LEVEL_CODE);
			         }}
			         ,{minWidth:160,align:'center',field:'ID',fixed:'right', title: '操作', templet:function(row){
			        	 	var val = row.PHONENUM;
			        		var rq="";
							rq = " <a href='javascript:red.edit(\""+val+"\");' class='layui-btn layui-btn-success layui-btn-xs'>"+ getI18nValue("编辑") +"</a>&nbsp;"
							rq = rq+"<a href='javascript:red.del(\""+val+"\");' class='layui-btn layui-btn-danger  layui-btn-xs'>" + getI18nValue("删除") +"</a>"
							return rq;
			         }}
			         ]
				]
			});
		}
		
		//重置
		red.reset=function(){
			$("#divId select").val("");
	    	$("#divId input").val("");
		};
		
		red.searchData = function(flag) {
			if(flag=='1'){
				$("#searchForm").queryData({id:'main',page:{ curr: 1}});
			}else{
				$("#searchForm").queryData({id:'main'});
			}
		}
		red.add = function() {
			popup.layerShow({
				type : 2,
				title : getI18nValue("新增"),
				offset : '20px',
				area : [ '400px', '480px' ],
				shadeClose:false
			},"${ctxPath}/pages/black/red-edit.jsp",null);
		}

		red.edit = function(phoneNum) {
			popup.layerShow({
				type : 2,
				title : getI18nValue("修改"),
				offset : '20px',
				area : [ '400px', '480px' ],
				shadeClose:false
			},"${ctxPath}/pages/black/red-edit.jsp",{phoneNum:phoneNum}); // =${ctxPath}/pages/black/red-edit.jsp?phoneNum=
		}
		red.dels=function(){
			var checkStatus = table.checkStatus('main');
			if(checkStatus.data.length==0){
				layer.msg(getI18nValue("请选择需要删除的行！"),{icon:0});
				return ;
			}
			layer.confirm(getI18nValue('是否删除？'),{icon: 3, title:getI18nValue('提示'),offset:'20px',btn:[getI18nValue('确定'),getI18nValue('取消')]},  function(index){
	        	var arr=new Array();
				for(var i=0;i<checkStatus.data.length;i++){
					arr.push(checkStatus.data[i].PHONENUM);
				}
				red.deleteData(arr);
	        });
		}
		red.del=function(id){
			layer.confirm(getI18nValue('是否删除？'),{icon: 3, title:getI18nValue('提示'),offset:'20px',btn:[getI18nValue('确定'),getI18nValue('取消')]},  function(index){
	        	var arr=new Array();
				arr.push(id);
				red.deleteData(arr);
	        });
		}
		red.deleteData = function(arr){
			var data={};
			data.ids=arr;
			ajax.remoteCall("${ctxPath}/servlet/red?action=delete",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon: 1,time:1200},function(){
						red.searchData();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		//导出
		red.downloadExl=function(){
			 location.href ="${ctxPath}/servlet/red?action=export&"+$("#searchForm").serialize();
		}
		//导入
		red.importData = function() {
			popup.layerShow({
				type : 2,
				title : getI18nValue("导入"),
				offset : '20px',
				area : [ '420px', '200px' ]
			}, "${ctxPath}/pages/black/red-import.jsp", null);
		}
		
	
		
</script>
	<script type="text/javascript">
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>