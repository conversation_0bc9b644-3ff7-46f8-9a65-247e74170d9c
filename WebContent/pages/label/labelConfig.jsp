<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="标签配置"></title>
	<style type="text/css">
		#dataList tr td{white-space:nowrap;min-width:100px;max-width:300px;text-overflow:ellipsis;overflow:hidden}
		a:link{ color:#00adff;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form class="form-inline" id="searchForm">
		<div class="ibox">
			<div class="ibox-title clearfix">
				<div class="form-group">
                    <h5 i18n-content="结果评定等级"></h5>
					<div class="input-group pull-right">
						<button type="button" class="btn btn-sm btn-success btn-outline" onclick="BusiType.add('','')"><span i18n-content="新增"></span></button>
					</div>
                </div>
                <hr style="margin: 3px -15px">
				<div class="form-group">
					<div class="input-group input-group-sm"> 
						<span class="input-group-addon" i18n-content="标签名称"></span>
						<input type="text" name="NAME" id="name" class="form-control input-sm" style="width:152px;">
					</div>
					<div class="input-group input-group-sm">
						<button type="button" class="btn btn-sm btn-default" onclick="BusiType.query()"><span class="glyphicon glyphicon-search"></span><span i18n-content="查询"></span></button>
					</div> 
				</div>
			</div>
			<div class="ibox-content">
				<table class="layui-table layui-form" id="tree-table" lay-size="sm"></table>
			</div>
		</div>
	</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		//var treeTable;
		var BusiType = {
			query : function() {
				$(".layui-card-body").empty();
				$(".layui-card-body").append('<table class="layui-table layui-form" id="tree-table" lay-size="sm"></table>');
				renderTable();
			},
			add : function(pid,pName,code) {
				popup.layerShow({type:1,title:getI18nValue('新增标签'),offset:'20px',area:['530px;','350px']},"${ctxPath}/pages/label/labelConfig-edit.jsp",{P_ID:pid,PNAME:pName,CODE:code});
			},
			edit : function(id) {
				popup.layerShow({type:1,title:getI18nValue('编辑标签'),offset:'20px',area:['530px;','350px']},"${ctxPath}/pages/label/labelConfig-edit.jsp",{ID:id});
			},
			delRecord : function(obj) {
				if(obj) {
					layer.confirm(getI18nValue('是否确定删除当前数据？'),{icon: 3, title:getI18nValue('确定提示'),offset:'20px', btn: [getI18nValue('确认'), getI18nValue('取消')]}, function(index){
						var url = "${ctxPath}/servlet/QcLabel?action=delRecord";
						ajax.remoteCall(url,{ID:obj},function(result) {
							var state = result.state;
							if(state=='1') {
								layer.msg(result.msg,{icon: 1,time:1200});
								renderTable();
							} else {
								layer.alert(result.msg,{icon: 5});
								return;
							}
						});
					});
				}
			}
		}

		layui.config({
			base: '/cc-base/static/js/',
		});
		var ASSESS_GRADE ; 
		var renderTable = function() {
			ajax.daoCall({controls: ['QcLabelDao.labelTree'], params: {NAME:$("#name").val()}}, function (result) {
				var data = result['QcLabelDao.labelTree'] ? result['QcLabelDao.labelTree'].data : {};
				ASSESS_GRADE = result['QcLabelDao.labelTree'].ASSESS_GRADE ? result['QcLabelDao.labelTree'].ASSESS_GRADE : '3';
				data = formartData(data);
				if($("#name").val()){
					for(var i=0;i<data.length;i++){
						data[i].pid="0"
					}
				}
				
				if('1'==ASSESS_GRADE){
					ASSESS_GRADE=1;
				} else if('2'==ASSESS_GRADE){
					ASSESS_GRADE=3;
				}else if('3'==ASSESS_GRADE){
					ASSESS_GRADE=6;
				}
				
				
				loadTable(data);
			});
		}
		var loadTable = function(datas) {
			layui.use(['treeTable','layer','code','form'],function(){
				var o = layui.$,
					form = layui.form,
					layer = layui.layer,
					treeTable = layui.treeTable;
				var	re = treeTable.render({
					elem: '#tree-table',
					data:datas,
					height: 'full-180',
					icon_key: 'NAME',
					end: function(e){
						form.render();
					},
					cols: [
						/* {width:80, title: '序号',type:'numbers'}, */
						{
							key: 'NAME',
							title: getI18nValue('名称'),
							template: function(item){
								return '<span>'+getI18nValue(item.NAME)+'</span>';
							}
						},
						{
							key: 'CODE',
							title: getI18nValue('编号'),
							template: function(item){
								return '<span>'+item.CODE+'</span>';
							}
						},
						{
							key: 'DESCRIPTION',
							title: getI18nValue('描述'),
							align: 'center',
						},
						{
							key: 'CREATE_ACC',
							title: getI18nValue('创建人'),
							width:'150px',
							align: 'center',
						},
						{
							key: 'CREATE_TIME',
							title: getI18nValue('创建时间'),
							width:'150px',
							align: 'center',
						},
						{
							title: getI18nValue('操作'),
							align: 'center',
							width:'200px',
							template: function(item){
								var id = item.ID;
								var pid = item.P_ID;
								var name = item.NAME;
								var btn1 = '<a href="javascript:void(0)" onclick="BusiType.add(\''+id+'\',\''+name+'\',\''+item.CODE+'\')">'+getI18nValue("添加")+'</a>';
								var btn2 = '<a href="javascript:void(0)" onclick="BusiType.edit(\''+id+'\')">'+getI18nValue("编辑")+'</a>';
								var btn3 = '<a href="javascript:void(0)" onclick="BusiType.delRecord(\''+id+'\')">'+getI18nValue("删除")+'</a>';
								 if(item.CODE.length>ASSESS_GRADE){
									//第2层没有新增
									return btn2 + " - " + btn3;
								} 
								return btn1 + " - " + btn2 + " - " + btn3;
							}
						}
					],
				});
			})
		}

		var formartData = function(data) {
			var array = [];
			if(data) {
				for (var i = 0; i < data.length; i++) {
					var d = data[i];
					d['id'] = d.ID;
					d['pid'] = d.P_ID;
					array.push(d);
				}
			}
			return array;
		}

		$(function(){
			renderTable();
		});
		
	
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>