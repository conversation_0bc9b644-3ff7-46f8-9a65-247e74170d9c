<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="编辑质检标签"></title>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="easyform" class="form-horizontal form-inline" data-mars="QcLabelDao.labelRecord" data-mars-prefix="ebt.">
		<input type="hidden" name="ebt.ID" id="id" value="${param.ID}">
		<input type="hidden" name="ebt.P_ID" id="pid" value="${param.P_ID}">
		<input type="hidden" name="ebt.CODE" value="${param.CODE}">
		<table class="table table-edit table-vzebra mt-20">
			<tbody>
				<tr>
					<td width="100px" i18n-content="上级"></td>
					<td>
						<input type="text" name="ebt.PNAME" class="form-control input-sm" style="width:100%;" value="${param.PNAME }" readonly="readonly">
					</td>
				</tr>
				<tr>
					<td width="100px" class="required" i18n-content="名称"></td>
					<td>
						<input type="text" name="ebt.NAME" id="NAME" class="form-control input-sm" style="width:100%;" data-rules="required">
					</td>
				</tr>
				<tr>
					<td width="100px" i18n-content="序号"></td>
					<td>
						<input type="number" name="ebt.SORT_NUM" id="SORT_NUM" class="form-control input-sm" style="width:100%;">
					</td>
				</tr>
				<tr>
					<td width="100px" i18n-content="描述"></td>
					<td>
						<textarea name="ebt.DESCRIPTION" class="form-control input-sm" style="width:100%;" rows="3"></textarea>
					</td>
				</tr>
			</tbody>
		</table>
		<div class="layer-foot text-c">
			<button type="button" id="saveBtn" class="btn btn-primary btn-sm" onclick="BusiTypeEdit.ajaxSubmitForm()" id="saveBtn" i18n-content="保存">  </button>
			<button type="button" class="btn btn-default btn-sm ml-20" onclick="popup.layerClose(this)" i18n-content="关闭">  </button>
		</div>
	</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		var BusiTypeEdit = {
			ajaxSubmitForm : function () {
				if(form.validate("easyform")){
					var id =$("#id").val();
					var url = "${ctxPath}/servlet/QcLabel?action=addRecord";
					if(id) {
						url = "${ctxPath}/servlet/QcLabel?action=updateRecord";
					}
					var data = form.getJSONObject("easyform");
					delete data["ebt.PNAME"];
					ajax.remoteCall(url, data, function (result) {
						if(result.state == 1){
							layer.msg(result.msg,function(){
								popup.layerClose("#easyform");
								BusiType.query();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				}
			},
		}
		$(function(){
			var pid = '${param.P_ID}';
			if(!'${param.P_ID}') {
				$("#pid").val("0");
			}			
			$("#easyform").render();
			execI18n();
		});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>