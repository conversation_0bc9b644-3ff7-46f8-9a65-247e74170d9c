<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="客户标签管理"></title>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="CustLabelmngform"  style="margin-top: 10px;"  autocomplete="off" data-template="custlabeladd-template" data-container="CustLabellist" data-pk="${param.id}" data-mars-prefix="label.">
		 <input type="text"  class="hidden" id="userid" name="label.ID" value="${param.id }" >
		  <table  class="table table-auto table-bordered table-hover table-condensed">
		  		<thead>
		  			<tr>
		  				<th i18n-content="序号"></th>
		  				<th i18n-content="标签"></th>
		  				<th i18n-content="操作"></th>
		  			</tr>
		  		</thead>
                 <tbody id="CustLabellist">
                </tbody>
                 
                <tbody>
                	  <tr>
                        <td class="required" i18n-content="新增标签"></td>
                        <td>
                        	<div class="row">
                        	<div class="col-xs-6" style="padding-right:3px;">
                        		<div class="input-group input-group-sm">
								      <span class="input-group-addon required" i18n-content="标签类型"></span>	
									  <select data-rules="required" id="userlabelTypeId" name="label.userlabelType" onchange="labelTemp.custLabel()" data-mars="user.getLabelType" class="form-control input-sm">
									        <option value="" i18n-content="请选择"></option>
									  </select>
							    </div>
							    </div><div class="col-xs-6" style="padding-left:0;">
							    <div class="input-group input-group-sm">
								      <span class="input-group-addon required" i18n-content="待选标签"></span>	
									  <select data-rules="required" id="usercustLabelId" name="label.usercustLabel"  data-mars="user.getLabel" class="form-control input-sm">
									        <option value="" i18n-content="请选择"></option>
									  </select>
							    </div>
							    </div>
							    </div>
                       </td>
                       <td><button type="button" class="btn btn-sm btn-default" onclick="labelTemp.addTr()"><span class="glyphicon glyphicon-plus-sign"></span><span i18n-content="新增"></span></button></td>
                    </tr>
                </tbody>
	      </table>
	      <script id="custlabeladd-template" type="text/x-jsrender">
								   {{for  data}}
									  <tr>
										<td>{{:#index+1}}</td>
											<td>{{:NAME}}</td>
											<td>
			                                     <a href="javascript:void(0)" onclick="labelTemp.userlabelDel('{{:TAG_ID}}')" i18n-content="删除"></a> 
                                            </td> 
										</tr> 
								    {{/for}}					         
							 </script>
		  <div class="layer-foot text-c">
		   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="labelTemp.addCustLabel();" i18n-content="确定"></button>
		   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="layer.closeAll();" i18n-content="关闭"></button>
		  </div>
	</form>				
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		var addNotClose = '${param.addNotClose}';
		jQuery.namespace("labelTemp");
		requreLib.setplugs("wdate");
		var custLabelList = [];
		function renderCustlabelList(){
			console.log(custLabelList);
			var temp = $.templates("#custlabeladd-template");
			$("#CustLabellist").html(temp.render({data:custLabelList}));
		}
		$(function(){
			var paramCustLabelList = '${param.addCustLabelList}';
			if(paramCustLabelList && paramCustLabelList != 'undefined'){
				custLabelList = $.parseJSON(paramCustLabelList);
			}
			renderCustlabelList();
			$("#CustLabelmngform").render();
			$("#userlabelTypeId").render();
			execI18n();
		});
		labelTemp.custLabel = function(){
			var labelTypeId = $("#userlabelTypeId").val();
			var data = new Object();
			data.labelTypeId = labelTypeId;
			$("#usercustLabelId").render({data:{"labelTypeId":labelTypeId}});
	
		} 
		labelTemp.loadData = function(){
			$("#CustLabelmngform").render({success:function(){
				execI18n();
			}});
		}
		labelTemp.addCustLabel = function () {
			if(parent && parent.setCustLabelList){
				parent.setCustLabelList(custLabelList);
			}else if(setCustLabelList){
				setCustLabelList(custLabelList);
			}
			layer.closeAll();
		}
		
		labelTemp.userlabelDel=function(tagId){
			var isExist = false;
			var index = -1;
			for(index in custLabelList){
				if(custLabelList[index].TAG_ID == tagId){
					isExist = true;
					break;
				}
			}
			if(index >= 0 && index < custLabelList.length){
				custLabelList.splice(index, 1);
			}
			renderCustlabelList();
			/*
			var data = new Object();
			data.id = id;
			console.log(id);
	    	ajax.remoteCall("${ctxPath}/servlet/user?action=custLabelDelete",data,function(result) { 
				if(result.state == 1){
					$("#CustLabelmngform").render();
					CustTemp.loadData();
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			}); */
		}
		
		function isExistTag(tagId){
			var isExist = false;
			for(var index in custLabelList){
				if(custLabelList[index].TAG_ID == tagId){
					isExist = true;
					break;
				}
			}
			return isExist;
		}
		  
		labelTemp.addTr=function(){
			var custId = $("#userid").val();
			var custLabelId = $("#usercustLabelId").val();
			var custLabelTypeId = $("#userlabelTypeId").val();
			
			console.log(custId);
			if(!form.validate("#CustLabelmngform")){
				return;
			};
			var data = new Object();
			
			if(isExistTag(custLabelId)){
				layer.alert("该客户已存在该标签!",{icon: 5});
				return ;
			}else{
				//data.custId = custId;
				//data.custLabelId= custLabelId;
				//data.custLabelTypeId= custLabelTypeId;
				data.ID = "";
				data.CUSTOMER_ID = custId;
				data.TAG_ID = custLabelId;
				data.TAG_DIR_ID = custLabelTypeId;
				data.NAME = $("#usercustLabelId").find("option:selected").text();
				if(!custLabelList){
					custLabelList = [];
				}
				custLabelList.push(data);
				renderCustlabelList();
			}
			/*
			var url = "${ctxPath}/servlet/user?action=custLabelAdd";
			ajax.remoteCall(url,data,function(result) {
				if(result.state == 1){
					labelTemp.loadData();
					if(addNotClose != 'true'){
						popup.layerClose();
					}
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			}
			); */
			execI18n();
		}
    
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>