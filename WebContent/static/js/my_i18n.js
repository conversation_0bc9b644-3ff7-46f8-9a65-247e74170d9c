var my_di18n = {
	CN : {
		"邮件质检数据":"邮件质检数据",
		"邮件质检":"邮件质检",
		"处理编号":"处理编号",
		"邮件发送号码":"邮件发送号码",
		"邮件接收号码":"邮件接收号码",
		"接收方式":"接收方式",
		"邮件满意度":"邮件满意度",
		"是否解决":"是否解决",
		"是否解决":"是否解决",
		"首次处理人姓名":"首次处理人姓名",
		"首次处理时长":"首次处理时长",
		"解决时间":"解决时间",
		"关闭原因":"关闭原因",
		"发送":"发送",
		"抄送":"抄送",
		"密送":"密送",
		"正常解决":"正常解决",
		"系统关闭":"系统关闭",
		"合并到其他会话":"合并到其他会话",
		"坐席主动提交":"坐席主动提交",
		"质检员一键质检":"质检员一键质检",
		"质检员主动抽取":"质检员主动抽取",
		"系统分配":"系统分配",
		"手工分配":"手工分配",
		"随机抽取":"随机抽取",
		"退回修改":"退回修改",
		"申诉修改":"申诉修改",
		"邮件会话是否自动分配":"邮件会话是否自动分配",
		"坐席录音质检统计":"坐席录音质检统计",
		"质检员录音质检统计":"质检员录音质检统计",
		"坐席全媒体质检统计":"坐席全媒体质检统计",
		"质检员是否质检自身数据":"质检员是否质检自身数据",
		"质检员全媒体质检统计":"质检员全媒体质检统计",
		"" : "",
		"质检员整体质检分析" : "质检员整体质检分析",
		"评分细项名称不能为空" : "评分细项名称不能为空",
		"案例申请" : "案例申请",
		"呼叫方式" : "呼叫方式",
		"上周" : "上周",
		"坐席账号" : "坐席账号",
		"隐藏坐席和客户信息" : "隐藏坐席和客户信息",
		"操作失败!" : "操作失败!",
		"8、申诉数：对应发起申诉的数量" : "8、申诉数：对应发起申诉的数量",
		"请选择需要删除的坐席" : "请选择需要删除的坐席！",
		"挂断类型" : "挂断类型",
		"人工质检规则" : "人工质检规则",
		"每周最多" : "每周最多",
		"质检员已抽取" : "质检员已抽取",
		"已完成" : "已完成",
		"全媒体抽检分析：使用折线图，显示该坐席在统计时间内，每天的全媒体会话记录总量、抽取量、人工质检量；" : "全媒体抽检分析：使用折线图，显示该坐席在统计时间内，每天的全媒体会话记录总量、抽取量、人工质检量；",
		"质检申诉,结果配置" : "质检申诉,结果配置",
		"是否采纳" : "是否采纳",
		"语音抽检分析：使用折线图，显示该坐席在统计时间内，每天的语音通话记录总量、抽取量、人工质检量；" : "语音抽检分析：使用折线图，显示该坐席在统计时间内，每天的语音通话记录总量、抽取量、人工质检量；",
		"等级(三级数量)" : "等级(三级数量)",
		"转移结束" : "转移结束",
		"该数据已质检完成" : "该数据已质检完成",
		"未申诉" : "未申诉",
		"语音质检数据" : "语音质检数据",
		"1、质检数量趋势：统计每日新增的质检总数、每日进行的质检总数，人工质检统计部分会统计每日一票否决数" : "1、质检数量趋势：统计每日新增的质检总数、每日进行的质检总数，人工质检统计部分会统计每日一票否决数",
		"为空默认31天" : "为空默认31天",
		"待处理量均衡" : "待处理量均衡",
		"评定" : "评定",
		"质检员名称" : "质检员名称",
		"是否确定完成当前申诉" : "是否确定完成当前申诉？",
		"当前登录坐席不是质检员" : "当前登录坐席不是质检员",
		"质检组名称已经存在" : "质检组名称已经存在",
		"人工质检目标(%)" : "人工质检目标(%)",
		"该记录已经被质检成功" : "该记录已经被质检成功。",
		"质检总数：定时任务抽取到质检池中的任务总数" : "质检总数：定时任务抽取到质检池中的任务总数",
		"完成二次质检" : "完成二次质检",
		"坐席评分项报表" : "坐席评分项报表",
		"我已质检量" : "我已质检量",
		"用户姓名" : "用户姓名",
		"无数据" : "无数据",
		"批量调配" : "批量调配",
		"非常好" : "非常好",
		"等级(二级数量)" : "等级(二级数量)",
		"评论" : "评论",
		"标准工单" : "标准工单",
		"编辑任务" : "编辑任务",
		"11、申诉数：对应发起申诉的数量" : "11、申诉数：对应发起申诉的数量",
		"每个坐席" : "每个坐席",
		"人工质检统计" : "人工质检统计",
		"评定等级报表" : "评定等级报表",
		"话务质检数" : "话务质检数",
		"不插话、不抢话" : "不插话、不抢话",
		"12、申诉数：对应发起申诉的数量" : "12、申诉数：对应发起申诉的数量",
		"页" : "页",
		"已质检数" : "已质检数",
		"新增细项" : "新增细项",
		"评分细项" : "评分细项",
		"请输入退回原因：" : "请输入退回原因：",
		"质检规则配置" : "质检规则配置",
		"坐席发起" : "坐席发起",
		"全媒体质检平均分：人工已质检的全媒体的平均得分" : "全媒体质检平均分：人工已质检的全媒体的平均得分",
		"质检" : "质检",
		"说明" : "说明",
		"智能质检率(%)" : "智能质检率(%)",
		"最近7天" : "最近7天",
		"质检抽取分析" : "质检抽取分析",
		"仅抽取有效会话" : "仅抽取有效会话",
		"备注：如果如果账号修改过统计会出现同一个账号有多条数据的情况" : "备注：如果如果账号修改过统计会出现同一个账号有多条数据的情况",
		"质检时间" : "质检时间",
		"一般合格11" : "一般合格11",
		"语音平均分分析" : "语音平均分分析",
		"手工分配" : "手工分配",
		"申诉成功数" : "申诉成功数",
		"待人工质检数据" : "待人工质检数据",
		"请输入正确的抽取数量" : "请输入正确的抽取数量",
		"统计" : "统计",
		"质检员抽检分析" : "质检员抽检分析",
		"一级" : "一级",
		"耗时" : "耗时",
		"被叫" : "被叫",
		"质检员数：质检数据的质检员总数" : "质检员数：质检数据的质检员总数",
		"全媒体质检数" : "全媒体质检数",
		"填写建议内容，字数上限255字" : "填写建议内容，字数上限255字",
		"人均抽检数据：抽检数据总数/质检坐席数" : "人均抽检数据：抽检数据总数/质检坐席数",
		"默认选中一级，此时只按一级统计，二级、三级显示 “--”；选中二级，此时按一级、二级统计，三级显示“--”" : "默认选中一级，此时只按一级统计，二级、三级显示 “--”；选中二级，此时按一级、二级统计，三级显示“--”",
		"12、人工一票否决量：人工质检结果命中一票否决的记录" : "12、人工一票否决量：人工质检结果命中一票否决的记录",
		"来电地区" : "来电地区",
		"一般:" : "一般:",
		"1、质检数量趋势：统计每日新增的质检总数，和每日进行的智能质检总数" : "1、质检数量趋势：统计每日新增的质检总数，和每日进行的智能质检总数",
		"该质检规则已经正在使用中，不能删除" : "该质检规则已经正在使用中，不能删除！",
		"渠道名称" : "渠道名称",
		"条/页" : "条/页",
		"规则名称" : "规则名称",
		"抽取方式" : "抽取方式",
		"其它" : "其它",
		"质检员:" : "质检员:",
		"全媒体质检数：人工质检的全媒体总数" : "全媒体质检数：人工质检的全媒体总数",
		"一般般合格一点点" : "一般般合格一点点",
		"合格:" : "合格:",
		"回访问卷目的" : "回访问卷目的",
		"请先配置工单路径" : "请先配置工单路径",
		"客户昵称" : "客户昵称",
		"规则编号" : "规则编号",
		"坐席" : "坐席",
		"已有质检员" : "已有质检员",
		"时间查询范围不能为空" : "时间查询范围不能为空",
		"随机抽取5条数据" : "随机抽取5条数据",
		"语音申诉量：所选质检员在抽检日期范围内质检的语音被坐席发起申诉的量" : "语音申诉量：所选质检员在抽检日期范围内质检的语音被坐席发起申诉的量",
		"请选择字段" : "请选择字段",
		"人工质检总分" : "人工质检总分",
		"一票否决分析" : "一票否决分析",
		"人工一票否决指标量" : "人工一票否决指标量",
		"下一页" : "下一页",
		"无申诉信息" : "无申诉信息!",
		"全媒体质检分析：使用折线图，显示该质检员统计日期段内每天的全媒体质检数量" : "全媒体质检分析：使用折线图，显示该质检员统计日期段内每天的全媒体质检数量",
		"总量" : "总量",
		"良好:" : "良好:",
		"确认" : "确认",
		"确认申诉结果" : "确认申诉结果",
		"分数等级" : "分数等级",
		"话务平均分最高：话务质检记录平均分最高的坐席、分数" : "话务平均分最高：话务质检记录平均分最高的坐席、分数",
		"请选择分类" : "请选择分类",
		"质检项" : "质检项",
		"认真倾听，重复客户问题" : "认真倾听，重复客户问题",
		"添加质检对象" : "添加质检对象",
		"统计层级" : "统计层级",
		"用于区分该评分项的重要等级。一般：黑色，关键：红色，非关键：蓝色" : "用于区分该评分项的重要等级。一般：黑色，关键：红色，非关键：蓝色",
		"存在已质检数据!无法使用批量质检" : "存在已质检数据!无法使用批量质检",
		"统一的、或时令性的开场白" : "统一的、或时令性的开场白",
		"公共常用语导入" : "公共常用语导入",
		"质检比例" : "质检比例",
		"坐席整体质检分析" : "坐席整体质检分析",
		"全媒体一票否决量" : "全媒体一票否决量",
		"新增评分项" : "新增评分项",
		"完成任务" : "完成任务",
		"9、申诉成功数：对应申诉成功的数量" : "9、申诉成功数：对应申诉成功的数量",
		"审核通过，修改分数" : "审核通过，修改分数",
		"天" : "天",
		"服务结束时间" : "服务结束时间",
		"目标质检率" : "目标质检率",
		"评分细项名称" : "评分细项名称",
		"上次运行时间" : "上次运行时间",
		"能力" : "能力",
		"抽取规则配置" : "抽取规则配置",
		"待我质检量" : "待我质检量",
		"10、动态列：除了上述固定列，其余未动态类，即是所选质检任务质检规则中的评分细项信息" : "10、动态列：除了上述固定列，其余未动态类，即是所选质检任务质检规则中的评分细项信息",
		"删除提示" : "删除提示",
		"时间范围不能大于31天，请选择正确的时间范围" : "时间范围不能大于31天，请选择正确的时间范围",
		"质检申诉次数配置" : "质检申诉次数配置",
		"修改抽取规则" : "修改抽取规则",
		"新增质检总数" : "新增质检总数",
		"系统" : "系统",
		"标准URL" : "标准URL",
		"质检数据总览" : "质检数据总览",
		"未使用服务禁语" : "未使用服务禁语",
		"输入坐席账号、姓名" : "输入坐席账号、姓名",
		"人均质检数：每个质检员的平均质检数，已质检的数据/质检员总数" : "人均质检数：每个质检员的平均质检数，已质检的数据/质检员总数",
		"坐席工号" : "坐席工号",
		"质检任务数：开始时间再所选时间范围内的任务数" : "质检任务数：开始时间再所选时间范围内的任务数",
		"申诉时间" : "申诉时间",
		"质检申诉分析" : "质检申诉分析",
		"语音平均分分析：使用柱状图统计抽检语音平均分最高的前10个坐席，显示每个坐席的平均分" : "语音平均分分析：使用柱状图统计抽检语音平均分最高的前10个坐席，显示每个坐席的平均分",
		"统计类型" : "统计类型",
		"语音质检" : "语音质检",
		"11、人工质检合格率(%)：人工质检合格的数量/任务质检的总数据量" : "11、人工质检合格率(%)：人工质检合格的数量/任务质检的总数据量",
		"2、质检项排行：统计所有的质检项之中平均得分最高的前十个质检项" : "2、质检项排行：统计所有的质检项之中平均得分最高的前十个质检项",
		"掌握通话的主动权" : "掌握通话的主动权",
		"服务时长(秒)" : "服务时长(秒)",
		"坐席数" : "坐席数",
		"申诉审核通过量" : "申诉审核通过量",
		"任务执行" : "任务执行",
		"抽取规则" : "抽取规则",
		"质检结果描述" : "质检结果描述",
		"质检员质检统计" : "质检员质检统计",
		"最近一月" : "最近一月",
		"暂停任务" : "暂停任务",
		"共" : "共",
		"同步获取结果时失败" : "同步获取结果时失败",
		"话单总数" : "话单总数",
		"最近三个月" : "最近三个月",
		"全媒体平均分分析" : "全媒体平均分分析",
		"不能为空" : "不能为空",
		"修改评分项" : "修改评分项",
		"无申诉信息！" : "无申诉信息！",
		"1、仅显示智能质检结果。" : "1、仅显示智能质检结果。",
		"好" : "好",
		"质检对象数" : "质检对象数",
		"话务平均分：质检的坐席的话务平均分" : "话务平均分：质检的坐席的话务平均分",
		"配置优化" : "配置优化",
		"首页" : "首页",
		"人工抽检时间" : "人工抽检时间",
		"二级" : "二级",
		"坐席被质检员抽检数(为空不限制)" : "坐席被质检员抽检数(为空不限制)",
		"质检申诉时间配置" : "质检申诉时间配置",
		"是否一票否决" : "是否一票否决",
		"坐席挂断" : "坐席挂断",
		"系统挂断" : "系统挂断",
		"请输入完整的时间范围" : "请输入完整的时间范围",
		"5、及格数：已人工抽取并且进行人工质检合格的数据" : "5、及格数：已人工抽取并且进行人工质检合格的数据",
		"智能质检率" : "智能质检率",
		"对结果不满意" : "对结果不满意",
		"未处理" : "未处理",
		"2、质检总数：各个任务质检数据的总和" : "2、质检总数：各个任务质检数据的总和",
		"转移接入" : "转移接入",
		"人工质检完成数量" : "人工质检完成数量",
		"坐席主动提交" : "坐席主动提交",
		"语音质检分析：使用柱状图统计质检语音最多的前10个质检员的质检数量分布" : "语音质检分析：使用柱状图统计质检语音最多的前10个质检员的质检数量分布",
		"请选择时间范围" : "请选择时间范围!",
		"质检组新增" : "质检组新增",
		"语音" : "语音",
		"三级" : "三级",
		"只抽取符合抽取规则并且坐席在质检组里的话单" : "只抽取符合抽取规则并且坐席在质检组里的话单",
		"打分项" : "打分项",
		"是否开启结果发布功能" : "是否开启结果发布功能",
		"今日已质检" : "今日已质检",
		"用户排队结束" : "用户排队结束",
		"典型录音" : "典型录音",
		"请求体为空" : "请求体为空",
		"修改质检规则" : "修改质检规则",
		"话务平均分最低" : "话务平均分最低",
		"上级" : "上级",
		"安抚客户" : "安抚客户",
		"动态时间" : "动态时间",
		"正在加载" : "正在加载",
		"话务平均分最低：话务质检记录平均分最低的坐席、分数" : "话务平均分最低：话务质检记录平均分最低的坐席、分数",
		"二级质检项" : "二级质检项",
		"任务编辑" : "任务编辑",
		"评定等级分布：使用柱状图，显示话务、全媒体一级评定等级的分布对比" : "评定等级分布：使用柱状图，显示话务、全媒体一级评定等级的分布对比",
		"常用语目录" : "常用语目录",
		"15秒内准确理解客户问题" : "15秒内准确理解客户问题",
		"操作成功" : "操作成功",
		"录音质检" : "录音质检",
		"评分项统计" : "评分项统计",
		"案例跟进" : "案例跟进",
		"能快速使用知识库解决客服问题" : "能快速使用知识库解决客服问题",
		"是否允许同一坐席属于同一类型下的不同质检分组" : "是否允许同一坐席属于同一类型下的不同质检分组",
		"人工质检比率" : "人工质检比率",
		"没有过多的口语" : "没有过多的口语",
		"任务抽检" : "任务抽检",
		"一级质检项" : "一级质检项",
		"分" : "分",
		"最大规则细则不能超过15个" : "最大规则细则不能超过15个",
		"结果发布" : "结果发布",
		"目标质检率(%)" : "目标质检率(%)",
		"周六" : "周六",
		"标准URL与跳转URL不能相同" : "标准URL与跳转URL不能相同",
		"关键" : "关键",
		"添加成功" : "添加成功",
		"沒有反问用户" : "沒有反问用户",
		"暂停" : "暂停",
		"最多" : "最多",
		"评分项内容" : "评分项内容",
		"请选择要修改的质检对象" : "请选择要修改的质检对象",
		"减分项数" : "减分项数",
		"文件名" : "文件名",
		"条件时才质检" : "条件时才质检",
		"工单" : "工单",
		"任务调配" : "任务调配",
		"查找对象账号" : "查找对象账号",
		"有礼貌请客户重复" : "有礼貌请客户重复",
		"多选" : "多选",
		"不及格率" : "不及格率",
		"话务质检平均分：人工已质检的话务的平均得分" : "话务质检平均分：人工已质检的话务的平均得分",
		"全媒体抽检量：所选坐席在抽检日期范围内被抽检的全媒体数量" : "全媒体抽检量：所选坐席在抽检日期范围内被抽检的全媒体数量",
		"一票否决命中次数排行" : "一票否决命中次数排行",
		"值包含" : "值包含",
		"（系统抽取记录到质检库里后，只放到当前任务对应的质检组里，当前组里的所有状态为启用的质检员可以通过随机抽取、指定条件抽取方式，主动领取质检记录）" : "（系统抽取记录到质检库里后，只放到当前任务对应的质检组里，当前组里的所有状态为启用的质检员可以通过随机抽取、指定条件抽取方式，主动领取质检记录）",
		"导入成功" : "导入成功",
		"坐席抽检分析" : "坐席抽检分析",
		"质检员评分统计" : "质检员评分统计",
		"智能质检规则" : "智能质检规则",
		"运行时间配置" : "运行时间配置",
		"在质检该数据, 请联系该质检员或者重新调配" : "在质检该数据, 请联系该质检员或者重新调配",
		"坐席评分细项报表" : "坐席评分细项报表",
		"一般合格9" : "一般合格9",
		"人工质检结果表" : "人工质检结果表",
		"一般合格8" : "一般合格8",
		"是否导出整体话务质检表" : "是否导出整体话务质检表",
		"一般合格5" : "一般合格5",
		"批量移除" : "批量移除",
		"一般合格4" : "一般合格4",
		"一般合格7" : "一般合格7",
		"一般合格6" : "一般合格6",
		"一般合格1" : "一般合格1",
		"4、已质检数：已人工抽取并且进行人工质检的数据,若开启发布就是已发布的数据" : "4、已质检数：已人工抽取并且进行人工质检的数据,若开启发布就是已发布的数据",
		"一般合格2" : "一般合格2",
		"申诉量" : "申诉量",
		"执行机器MAC" : "执行机器MAC",
		"时间段集合" : "时间段集合",
		"智能质检平均分" : "智能质检平均分",
		"结果评定等级" : "结果评定等级",
		"修改分类" : "修改分类",
		"配置时间段" : "配置时间段",
		"超时结束" : "超时结束",
		"质检抽取方式" : "质检抽取方式",
		"统计JSON" : "统计JSON",
		"小结描述" : "小结描述",
		"1、坐席数：任务的质检组中的质检坐席数" : "1、坐席数：任务的质检组中的质检坐席数",
		"质检结果修改" : "质检结果修改",
		"结束挂断类型" : "结束挂断类型",
		"审核不通过" : "审核不通过",
		"良" : "良",
		"输入坐席账号、坐席姓名" : "输入坐席账号、坐席姓名",
		"语音抽检分析：使用柱状图统计抽检语音最多的前10个坐席的数量分布" : "语音抽检分析：使用柱状图统计抽检语音最多的前10个坐席的数量分布",
		"6、人工质检平均分：人工质检平均分每个任务中每个人工质检结果得分的平均分" : "6、人工质检平均分：人工质检平均分每个任务中每个人工质检结果得分的平均分",
		"明细" : "明细",
		"话务" : "话务",
		"规则描述" : "规则描述",
		"质检数量分析" : "质检数量分析",
		"抽取日期" : "抽取日期",
		"通话日期" : "通话日期",
		"计费开始时间" : "计费开始时间",
		"按条件抽取" : "按条件抽取",
		"类型" : "类型",
		"执行时间" : "执行时间",
		"随机抽取" : "随机抽取",
		"智能质检结果明细表" : "智能质检结果明细表",
		"常用语" : "常用语",
		"二级评定" : "二级评定",
		"智能质检统计" : "智能质检统计",
		"天之前的数据" : "天之前的数据",
		"质检坐席数：被质检的坐席数量" : "质检坐席数：被质检的坐席数量",
		"质检统计图表" : "质检统计图表",
		"质检项排行" : "质检项排行",
		"坐席质检统计" : "坐席质检统计",
		"评分细则" : "评分细则",
		"语音申诉量" : "语音申诉量",
		"启动任务" : "启动任务",
		"取值范围：0~10000" : "取值范围：0~10000",
		"确认更换质检员？" : "确认更换质检员？",
		"审核通过" : "审核通过",
		"预拨号呼出" : "预拨号呼出",
		"抽取数" : "抽取数",
		"重新抽取" : "重新抽取",
		"7、人工一票否决量：人工质检结果命中一票否决的记录" : "7、人工一票否决量：人工质检结果命中一票否决的记录",
		"评分细项描述" : "评分细项描述",
		"重新统计" : "重新统计",
		"工号" : "工号",
		"服务类型" : "服务类型",
		"申诉是否需要组长审核" : "申诉是否需要组长审核",
		"IVR转入" : "IVR转入",
		"备注:" : "备注:",
		"抽取规则信息" : "抽取规则信息",
		"质检坐席" : "质检坐席",
		"质检评论" : "质检评论",
		"设为组长" : "设为组长",
		"服务时长(s)" : "服务时长(s)",
		"语音抽检量：所选坐席在抽检日期范围内被抽检的语音数量" : "语音抽检量：所选坐席在抽检日期范围内被抽检的语音数量",
		"特别合格" : "特别合格",
		"关闭" : "关闭",
		"人工质检比率(%)" : "人工质检比率(%)",
		"人工质检平均分" : "人工质检平均分",
		"一票否决量：一票否决的质检项的命中数量" : "一票否决量：一票否决的质检项的命中数量",
		"没有选中数据，请刷新页面重新操作" : "没有选中数据，请刷新页面重新操作",
		"及格分数0~10000，不能为空" : "及格分数0~10000，不能为空",
		"已暂停" : "已暂停",
		"人工质检结果" : "人工质检结果",
		"任务基本信息" : "任务基本信息",
		"选择质检对象" : "选择质检对象",
		"操作成功!" : "操作成功!",
		"查看详情" : "查看详情",
		"请最少选择一个评定项" : "请最少选择一个评定项",
		"角色" : "角色",
		"全媒体一票否决量：所选质检员在抽检日期范围内质检的全媒体一票否决量" : "全媒体一票否决量：所选质检员在抽检日期范围内质检的全媒体一票否决量",
		"及格分数" : "及格分数",
		"数据总览" : "数据总览",
		"反馈描述" : "反馈描述",
		"全媒体抽检数" : "全媒体抽检数",
		"是否允许二次申诉" : "是否允许二次申诉",
		"系统分配" : "系统分配",
		"很好" : "很好",
		"通过" : "通过",
		"已重新抽取，请刷新页面" : "已重新抽取，请刷新页面",
		"推送引擎数" : "推送引擎数",
		"质检组名称" : "质检组名称",
		"服务态度不好" : "服务态度不好",
		"修改成功" : "修改成功",
		"待质检列表" : "待质检列表",
		"通过申诉" : "通过申诉",
		"一般合格" : "一般合格",
		"是否释放选中数据？" : "是否释放选中数据？",
		"任务" : "任务",
		"全媒体质检分析：使用柱状图统计质检全媒体最多的前10个质检员的质检数量分布" : "全媒体质检分析：使用柱状图统计质检全媒体最多的前10个质检员的质检数量分布",
		"时间范围" : "时间范围",
		"挂机原因" : "挂机原因",
		"通话时长" : "通话时长",
		"录音质检统计" : "录音质检统计",
		"如果是多选数据请选择15个以内的数据,动态时间类型仅在每条录音只质检一次的情况下生效" : "如果是多选数据请选择15个以内的数据,动态时间类型仅在每条录音只质检一次的情况下生效",
		"条件抽取" : "条件抽取",
		"抽取时间" : "抽取时间",
		"按日期" : "按日期",
		"结果" : "结果",
		"任务管理" : "任务管理",
		"加分项数" : "加分项数",
		"灵活推荐/采用合适的解决方案" : "灵活推荐/采用合适的解决方案",
		"退回成功" : "退回成功",
		"坐席姓名" : "坐席姓名",
		"质检评定" : "质检评定",
		"+添加关联" : "+添加关联",
		"优秀录音" : "优秀录音",
		"质检组" : "质检组",
		"本周总量均衡" : "本周总量均衡",
		"业务能力突出" : "业务能力突出",
		"任务自动分配最大值" : "任务自动分配最大值",
		"历时" : "历时",
		"数据操作重新统计确认" : "重新统计该段时间的数据，会先删除原有统计的数据，如果统计失败，可能会造成数据丢失,确认是否继续?",
		"全媒体申诉量：所选质检员在抽检日期范围内质检的全媒体被坐席发起申诉的量" : "全媒体申诉量：所选质检员在抽检日期范围内质检的全媒体被坐席发起申诉的量",
		"人工一票否决量" : "人工一票否决量",
		"转派" : "转派",
		"5、智能质检率：任务中发起智能质检的数据占比：智能质检数/总数" : "5、智能质检率：任务中发起智能质检的数据占比：智能质检数/总数",
		"最近一天" : "最近一天",
		"如果是多选请选择15个以内的数据!" : "如果是多选请选择15个以内的数据!",
		"右键菜单操作" : "右键菜单操作",
		"网页" : "网页",
		"组长审核申诉" : "组长审核申诉",
		"保存成功" : "保存成功",
		"全媒体质检数据" : "全媒体质检数据",
		"抽检最多：抽检话务全媒体最多的坐席名称、质检数" : "抽检最多：抽检话务全媒体最多的坐席名称、质检数",
		"任务结束时间" : "任务结束时间",
		"质检抽取分析：再下面使用折线图，显示选择区间内，每天的抽检总量" : "质检抽取分析：再下面使用折线图，显示选择区间内，每天的抽检总量",
		"加分" : "加分",
		"请先选择质检任务！" : "请先选择质检任务！",
		"（计算当前质检组里所有状态为启用的质检员本月在该质检任务里的总量（包含已质检的、待质检的总量），按总量进行均衡分配，优先分配给总量少的质检员）" : "（计算当前质检组里所有状态为启用的质检员本月在该质检任务里的总量（包含已质检的、待质检的总量），按总量进行均衡分配，优先分配给总量少的质检员）",
		"已质检" : "已质检",
		"统计查询" : "统计查询",
		"任务明细数据" : "任务明细数据",
		"是否确定退回当前申诉" : "是否确定退回当前申诉？",
		"修改失败" : "修改失败",
		"用户挂断" : "用户挂断",
		"是否确定删除当前数据？" : "是否确定删除当前数据？",
		"通话日期" : "通话日期",
		"质检运行时段" : "质检运行时段",
		"每天指定时间段" : "每天指定时间段",
		"1、坐席：统计的坐席" : "1、坐席：统计的坐席",
		"待抽取" : "待抽取",
		"语音一票否决量：所选质检员在抽检日期范围内质检的语音一票否决量" : "语音一票否决量：所选质检员在抽检日期范围内质检的语音一票否决量",
		"抽检最少" : "抽检最少",
		"待审核" : "待审核",
		"录音文件" : "录音文件",
		"请进行任务管理配置" : "请进行任务管理配置",
		"添加失败" : "添加失败",
		"合格分数" : "合格分数",
		"个人常用语导入" : "个人常用语导入",
		"质检对象" : "质检对象",
		"每周指定时间段" : "每周指定时间段",
		"静音" : "静音",
		"质检任务:" : "质检任务:",
		"全选" : "全选",
		"会话接入" : "会话接入",
		"最近一个月" : "最近一个月",
		"坐席:" : "坐席:",
		"是否导出质检任务统计表" : "是否导出质检任务统计表？",
		"导入失败" : "导入失败",
		"请不要输入<>这些字符" : "请不要输入<>这些字符",
		"我熟人" : "我熟人",
		"选择质检坐席" : "选择质检坐席",
		"满意度" : "满意度",
		"语音一票否决量" : "语音一票否决量",
		"聊天记录" : "聊天记录",
		"全媒体平均分最高：包含全媒体所有质检记录平均分最高的坐席、分数" : "全媒体平均分最高：包含全媒体所有质检记录平均分最高的坐席、分数",
		"待质检数" : "待质检数",
		"条件" : "条件",
		"非关键" : "非关键",
		"我的规划建议" : "我的规划建议",
		"质检项排行：统计所有的质检项之中平均得分最高的前十个质检项" : "质检项排行：统计所有的质检项之中平均得分最高的前十个质检项",
		"一票否决数" : "一票否决数",
		"来源" : "来源",
		"评定等级分布" : "评定等级分布",
		"质检对象维护" : "质检对象维护",
		"是否有一票否决" : "是否有一票否决",
		"质检员工作分析" : "质检员工作分析",
		"7、人工质检未完成数：质检员已抽取但未质检的数量" : "7、人工质检未完成数：质检员已抽取但未质检的数量",
		"结案" : "结案",
		"立即执行" : "立即执行",
		"人工质检完成量" : "人工质检完成量",
		"保存失败" : "保存失败",
		"渠道类型" : "渠道类型",
		"质检结果隐藏质检员" : "质检结果隐藏质检员",
		"统计口径" : "统计口径",
		"智能质检数" : "智能质检数",
		"对此质检项作出建议" : "对此质检项作出建议",
		"人工质检分数" : "人工质检分数",
		"话务质检数：人工质检的话务总数" : "话务质检数：人工质检的话务总数",
		"智能质检结果数" : "智能质检结果数",
		"优秀率" : "优秀率",
		"通话保持开始提醒" : "通话保持开始提醒",
		"全部规则建议" : "全部规则建议",
		"13、人工一票否决量：人工质检结果命中一票否决的记录" : "13、人工一票否决量：人工质检结果命中一票否决的记录",
		"请选择质检员！" : "请选择质检员！",
		"处理时间" : "处理时间",
		"第二次申诉" : "第二次申诉",
		"尾页" : "尾页",
		"人工质检未完成数" : "人工质检未完成数",
		"二次申诉修改" : "二次申诉修改",
		"二次待确认" : "二次待确认",
		"人工质检合格率(%)" : "人工质检合格率(%)",
		"质检结果" : "质检结果",
		"状态" : "状态",
		"外部模块质检规则地址" : "外部模块质检规则地址",
		"通话时长(s)" : "通话时长(s)",
		"部门:" : "部门:",
		"全媒体抽检分析：使用柱状图统计抽检全媒体最多的前10个坐席的数量分布" : "全媒体抽检分析：使用柱状图统计抽检全媒体最多的前10个坐席的数量分布",
		"礼貌性结束用语" : "礼貌性结束用语",
		"是否导出录音质检统计表" : "是否导出录音质检统计表？",
		"扣分" : "扣分",
		"近n天" : "近n天",
		"处理结果" : "处理结果",
		"全媒体质检量：所选质检员在抽检日期范围内质检的全媒体数量" : "全媒体质检量：所选质检员在抽检日期范围内质检的全媒体数量",
		"4、分数等级：质检数据的得分等级,根据'系统管理'>>'参数配置'>>'质检参数配置'  里的'质检分数配置' 决定优秀,良好,一般,不及格的范围" : "4、分数等级：质检数据的得分等级,根据'系统管理'>>'参数配置'>>'质检参数配置'  里的'质检分数配置' 决定优秀,良好,一般,不及格的范围",
		"质检组管理" : "质检组管理",
		"优秀评定等级编号" : "优秀评定等级编号",
		"分数" : "分数",
		"质检任务" : "质检任务",
		"操作" : "操作",
		"没有通话录音文件" : "没有通话录音文件！",
		"一般" : "一般",
		"否" : "否",
		"请选择" : "请选择",
		"不满意" : "不满意",
		"下载导入模板" : "下载导入模板",
		"任务名称" : "任务名称",
		"按质检员" : "按质检员",
		"本日" : "本日",
		"查找对象" : "查找对象",
		"话务平均分最高" : "话务平均分最高",
		"结束原因" : "结束原因",
		"暂无数据" : "暂无数据",
		"坐席名称" : "坐席名称",
		"当天" : "当天",
		"质检员账号" : "质检员账号",
		"同时满足以下" : "同时满足以下",
		"质检申诉分析：使用两条折线图，统计每天的发起申诉量、申诉审核通过量" : "质检申诉分析：使用两条折线图，统计每天的发起申诉量、申诉审核通过量",
		"抽取规则使用中" : "该抽取规则已经正在使用中，不能删除",
		"日期范围" : "日期范围",
		"已有任务在质检该数据，您还不是任务" : "已有任务在质检该数据，您还不是任务",
		"评论内容" : "评论内容",
		"非常满意" : "非常满意",
		"发布" : "发布",
		"语气不耐烦" : "语气不耐烦",
		"抽取人" : "抽取人",
		"我已质检" : "我已质检",
		"呼入" : "呼入",
		"统计名称" : "统计名称",
		"规则建议" : "规则建议",
		"等于" : "等于",
		"质检规则评分项" : "质检规则评分项",
		"人工质检整体分析" : "人工质检整体分析",
		"语音抽检分析" : "语音抽检分析",
		"10、动态列：除了上述固定列，其余未动态类，即是所选质检任务质检规则中的评分项信息" : "10、动态列：除了上述固定列，其余未动态类，即是所选质检任务质检规则中的评分项信息",
		"1、话单总数：对应坐席、质检员或者按日期统计的话单总数" : "1、话单总数：对应坐席、质检员或者按日期统计的话单总数",
		"数据权限" : "数据权限",
		"质检统计分析" : "质检统计分析",
		"取消" : "取消",
		"整体全媒体质检" : "整体全媒体质检",
		"1、仅显示人工质检结果。" : "1、仅显示人工质检结果。",
		"主动发起" : "主动发起",
		"质检分数配置" : "质检分数配置",
		"抽检最多" : "抽检最多",
		"人很好看" : "人很好看",
		"全部" : "全部",
		"建议内容" : "建议内容",
		"3、得分分布：所有质检项的的得分分布" : "3、得分分布：所有质检项的的得分分布",
		"（计算当前质检组里所有状态为启用的质检员本周在该质检任务里的总量（包含已质检的、待质检的总量），按总量进行均衡分配，优先分配给总量少的质检员）" : "（计算当前质检组里所有状态为启用的质检员本周在该质检任务里的总量（包含已质检的、待质检的总量），按总量进行均衡分配，优先分配给总量少的质检员）",
		"姓名" : "姓名",
		"质检比例：人工质检量所占质检总数的比例" : "质检比例：人工质检量所占质检总数的比例",
		"输入质检员账号,姓名" : "输入质检员账号,姓名",
		"总数" : "总数",
		"针对该质检规则提出建议" : "针对该质检规则提出建议",
		"正在抽取，稍后请刷新页面查看" : "正在抽取，稍后请刷新页面查看",
		"二次申诉" : "二次申诉",
		"自定义区间" : "自定义区间",
		"释放" : "释放",
		"请选择要抽取的坐席！" : "请选择要抽取的坐席！",
		"任务分配" : "任务分配",
		"二次申请" : "二次申请",
		"全媒体质检修改" : "全媒体质检修改",
		"抽取状态" : "抽取状态",
		"选择文件" : "选择文件",
		"只能查看:" : "只能查看:",
		"全媒体申诉量" : "全媒体申诉量",
		"及格数" : "及格数",
		"是否确定" : "是否确定",
		"先智能质检再人工质检" : "先智能质检再人工质检",
		"结果修改" : "结果修改",
		"所有" : "所有",
		"数据统计结束时间" : "数据统计结束时间",
		"整体话务质检" : "整体话务质检",
		"质检及格=质检得分>及格分数" : "质检及格=质检得分>及格分数",
		"全媒体平均分最高" : "全媒体平均分最高",
		"上一页" : "上一页",
		"质检员选择" : "质检员选择",
		"未质检" : "未质检",
		"按坐席" : "按坐席",
		"汇总" : "汇总",
		"修改常用语" : "修改常用语",
		"质检员" : "质检员",
		"质检平均分：已质检数据的平均得分" : "质检平均分：已质检数据的平均得分",
		"7、人工质检总分：每个任务中每个人工质检结果得分的总和" : "7、人工质检总分：每个任务中每个人工质检结果得分的总和",
		"申诉状态" : "申诉状态",
		"人工抽检率(%)" : "人工抽检率(%)",
		"一键质检时手动选择质检任务" : "一键质检时手动选择质检任务",
		"全媒体平均分分析：使用柱状图统计抽检全每天平均分最高的前10个坐席，显示每个坐席的平均分" : "全媒体平均分分析：使用柱状图统计抽检全每天平均分最高的前10个坐席，显示每个坐席的平均分",
		"智能质检比率" : "智能质检比率",
		"语调亲切、委婉" : "语调亲切、委婉",
		"请选择上传的文件" : "请选择上传的文件！",
		"退回原因不能为空" : "退回原因不能为空",
		"表现良好" : "表现良好",
		"呼出" : "呼出",
		"会话编号" : "会话编号",
		"规则名称不能为空" : "规则名称不能为空",
		"外线呼入" : "外线呼入",
		"申诉" : "申诉",
		"提交人姓名" : "提交人姓名",
		"的质检员，无法操作该数据" : "的质检员，无法操作该数据",
		"系统自动结案" : "系统自动结案",
		"良好" : "良好",
		"质检进度" : "质检进度",
		"语音质检量：所选质检员在抽检日期范围内质检的语音数量" : "语音质检量：所选质检员在抽检日期范围内质检的语音数量",
		"新增质检规则" : "新增质检规则",
		"客户姓名" : "客户姓名",
		"一票否决命中次数排行：统计一票否决评分细项中出现次数最多的前十项" : "一票否决命中次数排行：统计一票否决评分细项中出现次数最多的前十项",
		"评定结果" : "评定结果",
		"来源说明" : "来源说明",
		"系统提示" : "系统提示",
		"申诉内容" : "申诉内容",
		"清空" : "清空",
		"主叫" : "主叫",
		"添加质检员" : "添加质检员",
		"暂存" : "暂存",
		"最少" : "最少",
		"质检结果明细" : "质检结果明细",
		"情绪异常" : "情绪异常",
		"时间段" : "时间段",
		"知识标题" : "知识标题",
		"待质检录音记录" : "待质检录音记录",
		"不合格" : "不合格",
		"人工质检数量趋势" : "人工质检数量趋势",
		"6、未及格数：人工质检不合格的数据，已质检数 - 及格数" : "6、未及格数：人工质检不合格的数据，已质检数 - 及格数",
		"质检数量" : "质检数量",
		"合格" : "合格",
		"一般合格101" : "一般合格101",
		"展开" : "展开",
		"查看抽取记录" : "查看抽取记录",
		"本月总量均衡" : "本月总量均衡",
		"业务名称" : "业务名称",
		"质检组:" : "质检组:",
		"组长审批申诉" : "组长审批申诉",
		"不及格" : "不及格",
		"本月" : "本月",
		"全媒体质检" : "全媒体质检",
		"质检员姓名" : "质检员姓名",
		"查看工单" : "查看工单",
		"处理人账号" : "处理人账号",
		"该质检分组存在质检员或坐席，不允许删除!" : "该质检分组存在质检员或坐席，不允许删除!",
		"日期范围:" : "日期范围:",
		"申诉成功" : "申诉成功!",
		"快进" : "快进",
		"分配质检员" : "分配质检员",
		"任务执行日志" : "任务执行日志",
		"创建原因" : "创建原因",
		"客户姓名/主叫" : "客户姓名/主叫",
		"人工质检合格率" : "人工质检合格率",
		"请选择要释放的数据！" : "请选择要释放的数据！",
		"不文明用语" : "不文明用语",
		"通话记录是否自动分配" : "通话记录是否自动分配",
		"质检最多" : "质检最多",
		"全媒体平均分：所选质检员在抽检日期范围内质检的全媒体平均分" : "全媒体平均分：所选质检员在抽检日期范围内质检的全媒体平均分",
		"班组长审批申诉" : "班组长审批申诉",
		"全媒体平均分" : "全媒体平均分",
		"话务平均分" : "话务平均分",
		"不出现冷场" : "不出现冷场",
		"全媒体质检平均分" : "全媒体质检平均分",
		"录音质检修改" : "录音质检修改",
		"任务开始时间" : "任务开始时间",
		"6、人工质检完成数：已经完成人工质检的数量" : "6、人工质检完成数：已经完成人工质检的数量",
		"设置坐席质检数(为空不限制)" : "设置坐席质检数(为空不限制)",
		"合格率" : "合格率",
		"结束时间" : "结束时间",
		"评分细项内容" : "评分细项内容",
		"已处理" : "已处理",
		"日期参数不能为空" : "日期参数不能为空",
		"优秀" : "优秀",
		"呼入时长(秒)" : "呼入时长(秒)",
		"最小1，最大31" : "最小1，最大31",
		"（查询出当前质检组里的状态为启用的质检员，将本次的抽取量，平均分配给各个坐席）" : "（查询出当前质检组里的状态为启用的质检员，将本次的抽取量，平均分配给各个坐席）",
		"请选择要修改的质检员" : "请选择要修改的质检员",
		"良好率" : "良好率",
		"分数等级：质检数据的得分等级,根据 系统管理 >> 参数配置 >> 质检参数配置里的质检分数配置 决定优秀,良好,一般,不及格的范围" : "分数等级：质检数据的得分等级,根据 系统管理 >> 参数配置 >> 质检参数配置里的质检分数配置 决定优秀,良好,一般,不及格的范围",
		"启动中" : "启动中",
		"运行时间规则" : "运行时间规则",
		"字数不超过200" : "字数不超过200",
		"及格" : "及格",
		"后退" : "后退",
		"评论列表" : "评论列表",
		"得分分布：所有质检项的的得分分布" : "得分分布：所有质检项的的得分分布",
		"全媒体平均分：质检的坐席的全媒体平均分" : "全媒体平均分：质检的坐席的全媒体平均分",
		"找不到对应的质检结果" : "找不到对应的质检结果",
		"质检数量趋势：统计每日新增的质检总数、每日进行的质检总数" : "质检数量趋势：统计每日新增的质检总数、每日进行的质检总数",
		"二次待审核" : "二次待审核",
		"计费结束时间" : "计费结束时间",
		"小结内容" : "小结内容",
		"表现一般" : "表现一般",
		"标注" : "标注",
		"全媒体申诉量：所选坐席在抽检日期范围内发起的全媒体申诉量" : "全媒体申诉量：所选坐席在抽检日期范围内发起的全媒体申诉量",
		"语音申诉量：所选坐席在抽检日期范围内发起的语音申诉量" : "语音申诉量：所选坐席在抽检日期范围内发起的语音申诉量",
		"日期" : "日期",
		"值存在于" : "值存在于",
		"是否允许二次申诉：" : "是否允许二次申诉：",
		"删除任务" : "删除任务",
		"申诉修改" : "申诉修改",
		"语音平均分：所选坐席在抽检日期范围内被质检的语音平均得分" : "语音平均分：所选坐席在抽检日期范围内被质检的语音平均得分",
		"8、人工质检平均分每个任务中每个人工质检结果得分的平均分" : "8、人工质检平均分每个任务中每个人工质检结果得分的平均分",
		"5、未及格数：人工质检不合格的数据，已质检数 - 及格数" : "5、未及格数：人工质检不合格的数据，已质检数 - 及格数",
		"删除分类" : "删除分类",
		"智能质检总分" : "智能质检总分",
		"呼叫结果" : "呼叫结果",
		"全媒体任务数据最大查找天数" : "全媒体任务数据最大查找天数",
		"人均抽检数据" : "人均抽检数据",
		"差" : "差",
		"质检评论列表" : "质检评论列表",
		"质检详情" : "质检详情",
		"是否确定审核通过该数据申诉，并修改上次质检结果" : "是否确定审核通过该数据申诉，并修改上次质检结果？",
		"执行时长秒" : "执行时长(秒)",
		"提交人账号" : "提交人账号",
		"录音信息" : "录音信息",
		"微信" : "微信",
		"8、目标质检率(%)：新建任务时的人工质检目标" : "8、目标质检率(%)：新建任务时的人工质检目标",
		"新增抽取规则" : "新增抽取规则",
		"该质检组存在质检员，不能删除" : "该质检组存在质检员，不能删除！",
		"结果评定" : "结果评定",
		"抽取已存在" : "该抽取规则名称已经存在，请重新命名！",
		"全媒体质检统计" : "全媒体质检统计",
		"人工质检时间" : "人工质检时间",
		"坐席被抽检数(为空不限制)" : "坐席被抽检数(为空不限制)",
		"选择质检员" : "选择质检员",
		"批量分配" : "批量分配",
		"描述" : "描述",
		"最近一周" : "最近一周",
		"该质检组存在质检对象，不能删除" : "该质检组存在质检对象，不能删除！",
		"人工质检目标" : "人工质检目标",
		"服务日期" : "服务日期",
		"单次均衡" : "单次均衡",
		"地址" : "地址",
		"质检任务统计" : "质检任务统计",
		"坐席原始数据抽取率(为空不限制)" : "坐席原始数据抽取率(为空不限制)",
		"质检项类型" : "质检项类型",
		"（计算当前质检组里所有状态为启用的质检员在该质检任务里待处理的总量（只包含待质检的总量），按待处理总量进行均衡分配，优先分配给待处理总量少的质检员）" : "（计算当前质检组里所有状态为启用的质检员在该质检任务里待处理的总量（只包含待质检的总量），按待处理总量进行均衡分配，优先分配给待处理总量少的质检员）",
		"点击新增该评分项的评分细项" : "点击新增该评分项的评分细项",
		"第二次申诉说明" : "第二次申诉说明",
		"质检顺序" : "质检顺序",
		"二次申诉审核" : "二次申诉审核",
		"全媒体一票否决量：所选坐席在抽检日期范围内被一票否决的全媒体量" : "全媒体一票否决量：所选坐席在抽检日期范围内被一票否决的全媒体量",
		"呼叫类型" : "呼叫类型",
		"是" : "是",
		"按模块抽取" : "按模块抽取",
		"质检评定 总分:" : "质检评定 总分:",
		"请选择正确的时间范围，时间不能大于30天" : "请选择正确的时间范围，时间不能大于30天",
		"合理使用欢迎语" : "合理使用欢迎语",
		"会话记录是否自动分配" : "会话记录是否自动分配",
		"专业能力一般" : "专业能力一般",
		"统计日期" : "统计日期",
		"人工质检完成数" : "人工质检完成数",
		"是否导出坐席质检统计表" : "是否导出坐席质检统计表",
		"播放" : "播放",
		"不自动分配" : "不自动分配",
		"渠道" : "渠道",
		"每月指定日期和时间段" : "每月指定日期和时间段",
		"2、类型：渠道类型" : "2、类型：渠道类型",
		"开始时间" : "开始时间",
		"语速快慢适中" : "语速快慢适中",
		"删除成功" : "删除成功",
		"9、智能质检总分：每个任务中每个智能质检结果得分的总和" : "9、智能质检总分：每个任务中每个智能质检结果得分的总和",
		"在区间内" : "在区间内",
		"质检申诉" : "质检申诉",
		"隐藏显示" : "隐藏显示",
		"10、智能质检平均分：每个任务中每个智能质检结果得分的平均分" : "10、智能质检平均分：每个任务中每个智能质检结果得分的平均分",
		"添加质检坐席" : "添加质检坐席",
		"3、总数：任务质检的总数据量" : "3、总数：任务质检的总数据量",
		"请输入备注信息" : "请输入备注信息",
		"人工质检率(%)" : "人工质检率(%)",
		"一级评定" : "一级评定",
		"全媒体抽检量" : "全媒体抽检量",
		"次数" : "次数",
		"每月最多" : "每月最多",
		"播放录音" : "播放录音",
		"质检任务数" : "质检任务数",
		"技能组" : "技能组",
		"服务时长" : "服务时长",
		"质检规则预览" : "质检规则预览",
		"已存在当前质检结果的申诉，请不要重复申诉" : "已存在当前质检结果的申诉，请不要重复申诉！",
		"质检最少" : "质检最少",
		"请求时间" : "请求时间",
		"任一" : "任一",
		"10、人工质检率(%)：人工抽取并且质检完成的数量/任务质检的总数据量" : "10、人工质检率(%)：人工抽取并且质检完成的数量/任务质检的总数据量",
		"评分方式" : "评分方式",
		"分配时间" : "分配时间",
		"坐席事件" : "坐席事件",
		"一票否决坐席数" : "一票否决坐席数",
		"周日" : "周日",
		"规则建议详情" : "规则建议详情",
		"二次申诉审核通过，修改分数" : "二次申诉审核通过，修改分数",
		"规则建议(处理/未处理)" : "规则建议(处理/未处理)",
		"找不到记录" : "找不到记录",
		"质检坐席数" : "质检坐席数",
		"3、待质检数：已人工抽取但没有完成人工质检的数据" : "3、待质检数：已人工抽取但没有完成人工质检的数据",
		"质检总数" : "质检总数",
		"周一" : "周一",
		"请上传文件" : "请上传文件",
		"退回申诉" : "退回申诉",
		"优秀:" : "优秀:",
		"抽取失败" : "抽取失败",
		"语音任务数据最大查找天数" : "语音任务数据最大查找天数",
		"评分项名称" : "评分项名称",
		"待质检数：人工质检已抽取，待质检的数量" : "待质检数：人工质检已抽取，待质检的数量",
		"任务开始日期" : "任务开始日期",
		"周三" : "周三",
		"语音质检分析：使用折线图，显示该质检员统计日期段内每天的语音质检数量" : "语音质检分析：使用折线图，显示该质检员统计日期段内每天的语音质检数量",
		"话务抽检数" : "话务抽检数",
		"未及格数" : "未及格数",
		"未知异常，请联系管理员" : "未知异常，请联系管理员",
		"抽检最少：抽检话务全媒体最少的坐席名称、质检数" : "抽检最少：抽检话务全媒体最少的坐席名称、质检数",
		"确认同步常用语到缓存" : "确认同步常用语到缓存?",
		"获取录音文件成功" : "获取录音文件成功。",
		"用户主动结束服务" : "用户主动结束服务",
		"添加时间" : "添加时间",
		"所属渠道" : "所属渠道",
		"全媒体平均分：所选坐席在抽检日期范围内被质检的全媒体平均得分" : "全媒体平均分：所选坐席在抽检日期范围内被质检的全媒体平均得分",
		"质检得分=基本分数+（质检项得分总分）" : "质检得分=基本分数+（质检项得分总分）",
		"成功删除" : "成功删除",
		"删除失败" : "删除失败",
		"4、及格数：已人工抽取并且进行人工质检的数据,若开启发布就是已发布的数据" : "4、及格数：已人工抽取并且进行人工质检的数据,若开启发布就是已发布的数据",
		"数据统计日志" : "数据统计日志",
		"同时符合以下" : "同时符合以下",
		"全媒体抽检分析" : "全媒体抽检分析",
		"退回" : "退回",
		"全媒体话单" : "全媒体话单",
		"是否单选" : "是否单选",
		"人工、智能质检并行" : "人工、智能质检并行",
		"图表说明" : "图表说明",
		"评分项配置" : "评分项配置",
		"二次申诉审核不通过" : "二次申诉审核不通过",
		"新增常用语" : "新增常用语",
		"服务时间" : "服务时间",
		"请先选择质检任务" : "请先选择质检任务！",
		"话务质检平均分" : "话务质检平均分",
		"登录帐号" : "登录帐号",
		"是否导出质检员评分统计表" : "是否导出质检员评分统计表",
		"智能质检结果表" : "智能质检结果表",
		"智能质检分数" : "智能质检分数",
		"抽取数量" : "抽取数量",
		"一票否决" : "一票否决",
		"质检数量分析：使用柱状图图，显示选择区间内，每天的质检数量" : "质检数量分析：使用柱状图图，显示选择区间内，每天的质检数量",
		"申诉数" : "申诉数",
		"处理规则建议" : "处理规则建议",
		"质检数量趋势：统计每日新增的质检总数、每日进行的质检总数，每日一票否决数" : "质检数量趋势：统计每日新增的质检总数、每日进行的质检总数，每日一票否决数",
		"规则建议列表" : "规则建议列表",
		"分值" : "分值",
		"得分分布" : "得分分布",
		"是否分配给质检组组长" : "是否分配给质检组组长",
		"质检员维护" : "质检员维护",
		"4、已质检数：已人工抽取并且进行人工质检的数据" : "4、已质检数：已人工抽取并且进行人工质检的数据,若开启发布就是已发布的数据",
		"更换质检员" : "更换质检员",
		"通话结束保持提醒" : "通话结束保持提醒",
		"发起申诉" : "发起申诉",
		"坐席结束服务" : "坐席结束服务",
		"质检员数" : "质检员数",
		"保存" : "保存",
		"会话日期" : "会话日期",
		"一键补抽" : "一键补抽",
		"人工质检配置" : "人工质检配置",
		"智能质检数量趋势" : "智能质检数量趋势",
		"全媒体平均分最：包含全媒体所有质检记录平均分最高的坐席、分数" : "全媒体平均分最：包含全媒体所有质检记录平均分最高的坐席、分数",
		"优" : "优",
		"优秀录音查询" : "优秀录音查询",
		"跳转至" : "跳转至",
		"人工质检未完成量" : "人工质检未完成量",
		"质检任务记录" : "质检任务记录",
		"抽取成功" : "抽取成功",
		"该数据已经申诉过两次，无法再次申诉" : "该数据已经申诉过两次，无法再次申诉！",
		"发起二次申诉" : "发起二次申诉",
		"一票否决量" : "一票否决量",
		"每月可申诉区间：" : "每月可申诉区间：",
		"周五" : "周五",
		"一票否决分析：使用柱状图统计一票否决量最多的10个坐席，显示每个坐席的一票否决量" : "一票否决分析：使用柱状图统计一票否决量最多的10个坐席，显示每个坐席的一票否决量",
		"基本信息" : "基本信息",
		"周二" : "周二",
		"新增分类" : "新增分类",
		"结束服务原因" : "结束服务原因",
		"整体全媒体质检统计" : "整体全媒体质检统计",
		"每天" : "每天",
		"该质检规则名称已经存在，请重新命名" : "该质检规则名称已经存在，请重新命名！",
		"有任务在执行中,请稍后再试" : "有任务在执行中,请稍后再试",
		"获取录音文件失败" : "获取录音文件失败！",
		"编辑标签" : "编辑标签",
		"智能质检量" : "智能质检量",
		"数据未完成质检，无法再次申诉" : "数据未完成质检，无法再次申诉！",
		"5、一票否决命中次数排行：统计一票否决评分细项中出现次数最多的前十项" : "5、一票否决命中次数排行：统计一票否决评分细项中出现次数最多的前十项",
		"新浪微博" : "新浪微博",
		"语音抽检量" : "语音抽检量",
		"3、质检总数：统计坐席的质检总数" : "3、质检总数：统计坐席的质检总数",
		"4、智能质检数：任务中发起智能质检的数量" : "4、智能质检数：任务中发起智能质检的数量",
		"跳转URL" : "跳转URL",
		"自动抽取时间" : "自动抽取时间",
		"语音平均分：所选质检员在抽检日期范围内质检的语音平均分" : "语音平均分：所选质检员在抽检日期范围内质检的语音平均分",
		"全媒体" : "全媒体",
		"不及格:" : "不及格:",
		"单选" : "单选",
		"质检最少：质检数最少的质检员名称、质检数" : "质检最少：质检数最少的质检员名称、质检数",
		"条" : "条",
		"质检状态" : "质检状态",
		"分配质检员规则" : "分配质检员规则",
		"常用语配置" : "常用语配置",
		"语音一票否决量：所选坐席在抽检日期范围内被一票否决的语音量" : "语音一票否决量：所选坐席在抽检日期范围内被一票否决的语音量",
		"字段说明" : "字段说明",
		"申诉审核" : "申诉审核",
		"配置字典" : "配置字典",
		"申诉说明" : "申诉说明",
		"关联知识" : "关联知识",
		"上传文件内容为空" : "上传文件内容为空！",
		"请选择条件" : "请选择条件",
		"质检员工号" : "质检员工号",
		"周四" : "周四",
		"全媒体平均分最低" : "全媒体平均分最低",
		"无法释放，参数错误" : "无法释放，参数错误",
		"时间查询范围不能大于31天" : "时间查询范围不能大于31天",
		"机器人" : "机器人",
		"会话时长" : "会话时长",
		"话机号码" : "话机号码",
		"质检员一键质检" : "质检员一键质检",
		"数据统计开始时间" : "数据统计开始时间",
		"质检平均分" : "质检平均分",
		"抽取结果" : "抽取结果",
		"查看工单地址" : "查看工单地址",
		"基本分数" : "基本分数",
		"1、人工一票否决指标量大于0标红整行" : "1、人工一票否决指标量大于0标红整行",
		"点击添加" : "点击添加",
		"质检员主动抽取" : "质检员主动抽取",
		"抽检总量" : "抽检总量",
		"9、人工抽检率(%)：人工抽取的总数/任务质检的总数据量" : "9、人工抽检率(%)：人工抽取的总数/任务质检的总数据量",
		"语音平均分" : "语音平均分",
		"Excel文件" : "Excel文件",
		"每10分钟运行（默认）" : "每10分钟运行（默认）",
		"坐席被抽检数，用于在使用该抽取规则的质检任务里，限制坐席每月(或每周)在对应质检任务里被抽取的条数，仅仅针对质检员在质检执行时，手工进行随机抽取和按条件抽取生效，系统自动从原始记录里抽取到质检库的过程中，不使用该条件。" : "坐席被抽检数，用于在使用该抽取规则的质检任务里，限制坐席每月(或每周)在对应质检任务里被抽取的条数，仅仅针对质检员在质检执行时，手工进行随机抽取和按条件抽取生效，系统自动从原始记录里抽取到质检库的过程中，不使用该条件。",
		"编号" : "编号",
		"查询评分项统计报表失败" : "查询评分项统计报表失败",
		"质检结果配置" : "质检结果配置",
		"服务开始时间" : "服务开始时间",
		"是否导出全媒体质检统计表" : "是否导出全媒体质检统计表?",
		"任务抽取规则" : "任务抽取规则",
		"用户请求" : "用户请求",
		"下载录音文件" : "下载录音文件",
		"评定等级" : "评定等级",
		"人工质检数" : "人工质检数",
		"质检最多：质检数最多的质检员名称、质检数" : "质检最多：质检数最多的质检员名称、质检数",
		"提交人账号/提交人姓名" : "提交人账号/提交人姓名",
		"用户登录超时" : "用户登录超时",
		"质检组编辑" : "质检组编辑",
		"评分细项统计" : "评分细项统计",
		"业务熟练" : "业务熟练",
		"第一次申诉" : "第一次申诉",
		"转移挂机" : "转移挂机",
		"待组长审核" : "待组长审核",
		"近" : "近",
		"工单地址" : "工单地址",
		"等级(一级数量)" : "等级(一级数量)",
		"配置" : "配置",
		"人工抽检量" : "人工抽检量",
		"一票否决坐席数：被一批否决的坐席数量" : "一票否决坐席数：被一批否决的坐席数量",
		"人均质检数" : "人均质检数",
		"请选择要更换的数据！" : "请选择要更换的数据！",
		"是否导出整体全媒体质检统计表" : "是否导出整体全媒体质检统计表",
		"质检项维护" : "质检项维护",
		"分配总数：分配给该坐席的新回执码数量" : "分配总数：分配给该坐席的新回执码数量",
		"已开始执行任务" : "已开始执行任务",
		"智能质检结果" : "智能质检结果",
		"待启动" : "待启动",
		"三级评定" : "三级评定",
		"标准及格分" : "标准及格分",
		"非常优秀" : "非常优秀",
		"2、质检员数：任务的质检组中的质检员数" : "2、质检员数：任务的质检组中的质检员数",
		"通话/会话记录ID" : "通话/会话记录ID",
		"人工质检抽取记录": "人工质检抽取记录",
		"抽取结果描述": "抽取结果描述",
		"条件参数": "条件参数",
		"录音转换配置":"录音转换配置",
		"是否开启历史录音wav转换成mp3功能":"是否开启历史录音wav转换成mp3功能",
		"历史录音wav转mp3后，是否删除旧wav":"历史录音wav转mp3后，是否删除旧wav",
		"历史录音wav转mp3的最大截止转换日期":"历史录音wav转mp3的最大截止转换日期",
		"历史录音wav转mp3的最小转换日期":"历史录音wav转mp3的最小转换日期",
		"为空不转换":"为空不转换",
		"每天允许运行时间":"每天允许运行时间",
		"质检项平均分":"质检项平均分",
		"一票否决命中次数":"一票否决命中次数",
		"质检时间不能为空":"质检时间不能为空",
		"请先选择模块":"请先选择模块",
		"请先配置":"请先配置",
		"的链接":"的链接",
		"客户主动结束服务":"客户主动结束服务",
		"转人工":"转人工",
		"对服务不满意":"对服务不满意",
		"客户评价":"客户评价",
		"客户消息":"客户消息",
		"客户资料":"客户资料",
		"会话小结":"会话小结",
		"每周":"每周",
		"每月":"每月",
		"坐席质检数量统计":"坐席质检数量统计",
		"每个坐席抽取数":"每个坐席抽取数",
		"未配置最少质检数":"未配置最少质检数",
		"标注内容":"标注内容",
		"":"",
		"":"",
		"":"",
		"":"",
		"":"",
	},
	EN : {
		"邮件质检数据":"Mail quality inspection data",
		"每个坐席抽取数":"Each seat draws a number",
		"标注内容":"Tagging content",
		"未配置最少质检数":"The minimum number of checks is not configured",
		"坐席质检数量统计":"Agent inspection quantity statistics",
		"每周":"Once a week",
		"每月":"Once a month",
		"客户评价":"The customer evaluation",
		"请先选择模块":"Please select the module first",
		"对服务不满意":"Not satisfied with the service",
		"客户评价":"The customer evaluation",
		"客户消息":"Customer message",
		"客户资料":"Customer data",
		"会话小结":"Session summary",
		"客户主动结束服务":"The customer terminates the service voluntarily",
		"转人工":"Turn to the artificial",
		"请先配置":"Please configure",
		"的链接":"A link to the",
		"质检时间不能为空":"The quality inspection time cannot be empty",
		"一票否决命中次数":"Number of hits in a no vote",
		"质检项平均分":"Average score of quality inspection items",
		"邮件质检":"Mail quality inspection",
		"处理编号":"Processing number",
		"邮件发送号码":"Mail sending number",
		"邮件接收号码":"Mail receiving number",
		"接收方式":"Receiving mode",
		"邮件满意度":"Mail satisfaction",
		"是否解决":"Resolved",
		"首次处理人姓名":"First processor name",
		"首次处理时长":"First processing time",
		"解决时间":"Solution time",
		"关闭原因":"Closing reason",
		"发送":"Send",
		"抄送":"Cc",
		"密送":"Bcc",
		"正常解决":"Normal resolution",
		"系统关闭":"System shutdown",
		"合并到其他会话":"Merge to another session",
		"坐席主动提交":"Agent actively submits",
		"质检员一键质检":"Inspector one key quality inspection",
		"质检员主动抽取":"Inspector active extraction",
		"系统分配":"System allocation",
		"手工分配":"Manual allocation",
		"随机抽取":"Random extraction",
		"退回修改":"Return modification",
		"申诉修改":"Appeal modification",

		"邮件会话是否自动分配":"Are mail sessions automatically assigned",
		"坐席录音质检统计":"Agent recording quality inspection statistics",
		"质检员录音质检统计":"Inspector recording quality inspection statistics",
		"坐席全媒体质检统计":"Agent all media quality inspection statistics",
		"质检员是否质检自身数据":"Does the quality inspector inspect his own data",
		"质检员全媒体质检统计":"Inspector all media quality inspection statistics",

		"" : "",
		"质检员整体质检分析" : "Quality inspector's overall quality inspection analysis",
		"评分细项名称不能为空" : "Scoring detailed item name cannot be empty",
		"案例申请" : "Case application",
		"呼叫方式" : "Call method",
		"上周" : "Last week",
		"坐席账号" : "Agent account",
		"隐藏坐席和客户信息" : "Hide agent and customer information",
		"操作失败!" : "Operation error",
		"8、申诉数：对应发起申诉的数量" : "8. Number of appeals: corresponds to the number of appeals initiated",
		"请选择需要删除的坐席" : "Please select the agent to be deleted！",
		"挂断类型" : "Hang up type",
		"人工质检规则" : "Manual quality inspection rules",
		"每周最多" : "Weekly maximum",
		"质检员已抽取" : "Quality inspector has drawn",
		"已完成" : "Completed",
		"全媒体抽检分析：使用折线图，显示该坐席在统计时间内，每天的全媒体会话记录总量、抽取量、人工质检量；" : "All media sampling analysis: use a broken line chart to display the total amount of all media session records, extraction amount and manual quality inspection amount of the agent every day within the statistical time:",
		"质检申诉,结果配置" : "Quality inspection appeal, result configuration",
		"是否采纳" : "Yes or No",
		"语音抽检分析：使用折线图，显示该坐席在统计时间内，每天的语音通话记录总量、抽取量、人工质检量；" : "Voice sampling analysis: use the broken line chart to display the total voice call records, extraction quantity and manual quality inspection quantity of the agent every day within the statistical time",
		"等级(三级数量)" : "Grade (three grade quantity)",
		"转移结束" : "End of transfer",
		"该数据已质检完成" : "The data has been inspected",
		"未申诉" : "Not appealed",
		"语音质检数据" : "Voice quality inspection data",
		"1、质检数量趋势：统计每日新增的质检总数、每日进行的质检总数，人工质检统计部分会统计每日一票否决数" : "1. Trend of quality inspection quantity: the total number of newly added quality inspection and the total number of daily quality inspection will be counted. The manual quality inspection statistics section will count the number of rejected votes per day",
		"为空默认31天" : "If blank, the default is 31 days",
		"待处理量均衡" : "Balance of throughput",
		"评定" : "Evaluate",
		"质检员名称" : "Name of quality inspector",
		"是否确定完成当前申诉" : "Are you sure to complete the current appeal?",
		"当前登录坐席不是质检员" : "The current login agent is not a quality inspector",
		"质检组名称已经存在" : "Quality inspection group name already exists",
		"人工质检目标(%)" : "Manual quality inspection target (%)",
		"该记录已经被质检成功" : "The recording has been successfully inspected.",
		"质检总数：定时任务抽取到质检池中的任务总数" : "Total quality inspection: the total number of tasks extracted from the quality inspection pool by scheduled tasks",
		"完成二次质检" : "Suggestions on processing rules",
		"坐席评分项报表" : "Agent scoring item report",
		"我已质检量" : "My quality inspection quantity ",
		"用户姓名" : "username",
		"无数据" : "no data",
		"批量调配" : "Batch deployment",
		"非常好" : "Very good",
		"等级(二级数量)" : "Grade (secondary quantity)",
		"评论" : "Comment",
		"标准工单" : "Standard work order",
		"编辑任务" : "Edit task",
		"11、申诉数：对应发起申诉的数量" : "11. Appeals quantity: corresponding to the quantity of appeals initiated",
		"每个坐席" : "Each agent",
		"人工质检统计" : "Manual quality inspection statistics",
		"评定等级报表" : "Rating Report",
		"话务质检数" : "Number of traffic quality inspections",
		"不插话、不抢话" : "Don't interrupt or rush",
		"12、申诉数：对应发起申诉的数量" : "12. Appeals quantity: corresponding to the quantity of appeals initiated",
		"页" : "Page",
		"已质检数" : "Quality inspection Quantity",
		"新增细项" : "Add detailed item",
		"请输入退回原因：" : "Please enter the return reason:",
		"评分细项" : "Scoring detailed item",
		"质检规则配置" : "Quality inspection rules configuration",
		"坐席发起" : "Agent initiated",
		"全媒体质检平均分：人工已质检的全媒体的平均得分" : "Average score of all media quality inspection: the average score of all media inspected manually",
		"质检" : "Quality inspection",
		"说明" : "Description",
		"智能质检率(%)" : "Intelligent quality inspection rate",
		"最近7天" : "Last 7 days",
		"质检抽取分析" : "Quality inspection extraction analysis",
		"仅抽取有效会话" : "Extract only valid sessions",
		"备注：如果如果账号修改过统计会出现同一个账号有多条数据的情况" : "Remarks: If the account is modified, the statistics will show that there are multiple pieces of data in the same account",
		"质检时间" : "Quality inspection time",
		"一般合格11" : "Generally acceptable 11",
		"语音平均分分析" : "Speech average score analysis",
		"手工分配" : "Manual allocation",
		"申诉成功数" : "Number of successful appeals",
		"待人工质检数据" : "Manual quality inspection pending data",
		"请输入正确的抽取数量" : "Please enter the correct extraction quantity",
		"统计" : "statistics",
		"质检员抽检分析" : "Sampling analysis by quality inspector",
		"一级" : "first-level",
		"耗时" : "Time consuming",
		"被叫" : "Called",
		"质检员数：质检数据的质检员总数" : "Number of quality inspectors: total number of quality inspectors in quality inspection data",
		"全媒体质检数" : "All media quality inspections",
		"填写建议内容，字数上限255字" : "Fill in the suggested content with a maximum of 255 words",
		"人均抽检数据：抽检数据总数/质检坐席数" : "Sampling data per capita: total sampling data / number of quality inspection seats",
		"默认选中一级，此时只按一级统计，二级、三级显示 “--”；选中二级，此时按一级、二级统计，三级显示“--”" : "By default, level 1 is selected. At this time, only level 1 statistics are performed, and “--” is displayed for Level 2 and level 3; If level 2 is selected, statistics will be made by level 1 and level 2, and “--” will be displayed for Level 3",
		"12、人工一票否决量：人工质检结果命中一票否决的记录" : "12. Manual one vote veto quantity: record of manual quality inspection results hitting one vote vet",
		"来电地区" : "Calling area",
		"一般:" : "general:",
		"1、质检数量趋势：统计每日新增的质检总数，和每日进行的智能质检总数" : "1. Quality inspection quantity trend: statistics the daily quantity of new quality inspections, and the daily quantity of intelligent quality inspections",
		"该质检规则已经正在使用中，不能删除" : "This quality inspection rule is already in use and cannot be deleted!",
		"渠道名称" : "Channel name",
		"条/页" : "Item/Page",
		"规则名称" : "Rule name",
		"抽取方式" : "Extraction mode",
		"其它" : "Other",
		"质检员:" : "Quality inspector:",
		"全媒体质检数：人工质检的全媒体总数" : "All media quality inspection quantity: the total number of all media inspected manually",
		"一般般合格一点点" : "Generally acceptable a little bit",
		"合格:" : "qualified:",
		"回访问卷目的" : "Purpose of return visit questionnaire",
		"请先配置工单路径" : "Please configure the ticket path first",
		"客户昵称" : "Customer nickname",
		"规则编号" : "Rule code",
		"坐席" : "Agent",
		"已有质检员" : "Existing quality inspectors",
		"时间查询范围不能为空" : "Time query range cannot be empty",
		"随机抽取5条数据" : "Randomly ezxtract 5 pieces of data",
		"语音申诉量：所选质检员在抽检日期范围内质检的语音被坐席发起申诉的量" : "Voice appeal quantity: the quantity of voice appeal initiated by the agent for quality inspection of the selected quality inspector within the sampling date range",
		"请选择字段" : "Please select a field",
		"人工质检总分" : "Manual quality inspection total score",
		"一票否决分析" : "One vote veto analysis",
		"人工一票否决指标量" : "Manual one vote veto indicator",
		"下一页" : "next page",
		"无申诉信息" : "No appeal information",
		"全媒体质检分析：使用折线图，显示该质检员统计日期段内每天的全媒体质检数量" : "All media quality inspection analysis: use a line chart to display the daily all media quality inspection quantity of the quality inspector within the statistical date period",
		"良好:" : "good:",
		"总量" : "Total",
		"确认" : "Confirm",
		"确认申诉结果" : "Confirm the appeal result",
		"分数等级" : "Score grade",
		"话务平均分最高：话务质检记录平均分最高的坐席、分数" : "Highest traffic average score: the seat and score with the highest average score in traffic quality inspection records",
		"请选择分类" : "Please Select Catagory",
		"质检项" : "Quality inspection items",
		"认真倾听，重复客户问题" : "Listen carefully and repeat customer questions",
		"添加质检对象" : "Add quality inspection object",
		"统计层级" : "Statistical hierarchy",
		"用于区分该评分项的重要等级。一般：黑色，关键：红色，非关键：蓝色" : "It is used to distinguish the importance level of the scoring item. General: black, key: red, non key: Blue",
		"存在已质检数据!无法使用批量质检" : "Quality inspection data exists! Cannot use batch quality inspection",
		"统一的、或时令性的开场白" : "A unified or seasonal opening statement",
		"公共常用语导入" : "Public Phrase Import",
		"质检比例" : "Quality inspection ratio",
		"坐席整体质检分析" : "Overall quality inspection analysis of seats",
		"全媒体一票否决量" : "One vote veto of all media",
		"新增评分项" : "Add scoring item",
		"完成任务" : "Task completed",
		"9、申诉成功数：对应申诉成功的数量" : "9. Number of successful appeals: the number of successful appeals",
		"审核通过，修改分数" : "Review passed, modify the score",
		"天" : "day",
		"服务结束时间" : "Service end time",
		"目标质检率" : "Quality inspection target rate",
		"评分细项名称" : "Scoring item name",
		"上次运行时间" : "Last run time",
		"能力" : "Ability to",
		"抽取规则配置" : "Extract rule configuration",
		"待我质检量" : "Quantity to be inspected by me",
		"10、动态列：除了上述固定列，其余未动态类，即是所选质检任务质检规则中的评分细项信息" : "10. Dynamic column: in addition to the above fixed columns, there are no dynamic classes, that is, the scoring item details information in the quality inspection rules of the selected quality inspection task",
		"删除提示" : "Delete prompt",
		"时间范围不能大于31天，请选择正确的时间范围" : "The time range cannot be greater than 31 days. Please select the correct time range",
		"质检申诉次数配置" : "Configuration of the number of quality inspection appeals",
		"修改抽取规则" : "Edit extraction rules",
		"新增质检总数" : "Total number of new quality inspections",
		"系统" : "System",
		"标准URL" : "Standard URL",
		"质检数据总览" : "Quality inspection data overview",
		"未使用服务禁语" : "Non use of service prohibitions",
		"输入坐席账号、姓名" : "Enter the agent account number and name",
		"人均质检数：每个质检员的平均质检数，已质检的数据/质检员总数" : "Number of quality inspectors: average number of quality inspectors per person, data inspected / total number of quality inspectors",
		"坐席工号" : "Agent ID",
		"质检任务数：开始时间再所选时间范围内的任务数" : "Quality inspection tasks: the number of tasks within the selected time range after the start time",
		"申诉时间" : "Appeal time",
		"质检申诉分析" : "Quality inspection appeal analysis",
		"语音平均分分析：使用柱状图统计抽检语音平均分最高的前10个坐席，显示每个坐席的平均分" : "Speech average score analysis: use histogram statistics to spot check the top 10 seats with the highest speech average score, and display the average score of each seat",
		"统计类型" : "Statistical type",
		"语音质检" : "Voice quality inspection",
		"11、人工质检合格率(%)：人工质检合格的数量/任务质检的总数据量" : "11. Manual quality inspection pass rate (%): manual quality inspections passed quantity/total data volume of quality inspection task",
		"2、质检项排行：统计所有的质检项之中平均得分最高的前十个质检项" : "2. Ranking of quality inspection items: count the top ten quality inspection items with the highest score among all quality inspection items",
		"掌握通话的主动权" : "Take the initiative in the call",
		"服务时长(秒)" : "Service duration (seconds)",
		"坐席数" : "Agent quantity",
		"申诉审核通过量" : "Appeal review pass",
		"任务执行" : "Task execution",
		"抽取规则" : "Extraction rules",
		"质检结果描述" : "Quality inspection results description",
		"质检员质检统计" : "Quality inspector quality inspection statistics",
		"最近一月" : "Last Month",
		"暂停任务" : "Pause task",
		"共" : "Total",
		"同步获取结果时失败" : "Failed to get the sync results ",
		"话单总数" : "Total quantity of call lists",
		"最近三个月" : "Last three months",
		"全媒体平均分分析" : "All media average score analysis",
		"无申诉信息！" : "No complaint information!",
		"不能为空" : "Cannot be empty",
		"修改评分项" : "Edit scoring item",
		"1、仅显示智能质检结果。" : "1. Only display the intelligent quality inspection results.",
		"好" : "good",
		"质检对象数" : "Quality inspection objects",
		"话务平均分：质检的坐席的话务平均分" : "Average traffic score: average traffic score of quality inspection agent",
		"配置优化" : "Configuration optimization",
		"首页" : "Home Page",
		"人工抽检时间" : "Manual sampling time",
		"二级" : "second-level",
		"坐席被质检员抽检数(为空不限制)" : "Number of seats sampled by quality inspectors (if it is empty, there is no limit)",
		"质检申诉时间配置" : "Quality inspection appeal time configuration",
		"是否一票否决" : "One vote veto",
		"坐席挂断" : "Agent hangs up",
		"系统挂断" : "System hangs up",
		"请输入完整的时间范围" : "Please enter the full time range",
		"5、及格数：已人工抽取并且进行人工质检合格的数据" : "5. Pass quantity: Already manually extracted and manual quality inspection passed data",
		"智能质检率" : "Intelligent inspection rate",
		"对结果不满意" : "Dissatisfied with the results",
		"未处理" : "Unprocessed",
		"转移接入" : "Transfer access",
		"2、质检总数：各个任务质检数据的总和" : "2. Total quality inspections quantity: total sum of quality inspection data of each task",
		"人工质检完成数量" : "Manual quality inspections completed quantity",
		"坐席主动提交" : "The agent actively submits",
		"语音质检分析：使用柱状图统计质检语音最多的前10个质检员的质检数量分布" : "Voice quality inspection analysis: use the histogram to count the quality inspection quantity distribution of the top 10 quality inspectors with the most voice quality inspection",
		"请选择时间范围" : "Please select a time range!",
		"质检组新增" : "Add quality inspection group",
		"语音" : "voice",
		"三级" : "three-level",
		"只抽取符合抽取规则并且坐席在质检组里的话单" : "Only extract the call record that meet the extraction rules and the agents are in the quality inspection group",
		"打分项" : "Scoring items",
		"是否开启结果发布功能" : "Enable result publishing function",
		"今日已质检" : "Already inspected today",
		"用户排队结束" : "End of user queue",
		"典型录音" : "Typical recording",
		"请求体为空" : "Request is empty",
		"修改质检规则" : "Edit quality inspection rules",
		"话务平均分最低" : "Lowest average traffic score",
		"上级" : "superior",
		"安抚客户" : "Appease customers",
		"动态时间" : "Dynamic time",
		"正在加载" : "loading",
		"话务平均分最低：话务质检记录平均分最低的坐席、分数" : "Lowest traffic average score: the seat and score with the lowest average score in traffic quality inspection records",
		"二级质检项" : "Secondary quality inspection items",
		"任务编辑" : "Task editor",
		"评定等级分布：使用柱状图，显示话务、全媒体一级评定等级的分布对比" : "Rating distribution: histogram is used to display the distribution comparison of first-class rating of traffic and all media",
		"常用语目录" : "Phrase Dir",
		"15秒内准确理解客户问题" : "Accurately understand customer issues within 15 seconds",
		"操作成功" : "Operation successful",
		"录音质检" : "Recording quality inspection",
		"评分项统计" : "Scoring item statistics",
		"案例跟进" : "Case follow-up",
		"能快速使用知识库解决客服问题" : "Can quickly use the knowledge base to solve customer service problems",
		"是否允许同一坐席属于同一类型下的不同质检分组" : "Allow the same agent to belong to different quality inspection groups under the same type",
		"人工质检比率" : "Manual quality inspection ratio",
		"没有过多的口语" : "Not too much oral English",
		"任务抽检" : "Task sampling",
		"一级质检项" : "Primary quality inspection items",
		"分" : "Score",
		"最大规则细则不能超过15个" : "The maximum rules cannot exceed 15",
		"结果发布" : "Result release",
		"目标质检率(%)" : "Target quality inspection rate (%)",
		"周六" : "Saturday",
		"标准URL与跳转URL不能相同" : "The standard URL and the redirect URL cannot be the same",
		"关键" : "key",
		"添加成功" : "Add successful",
		"沒有反问用户" : "Didn't ask the user",
		"暂停" : "Pause",
		"最多" : "most",
		"评分项内容" : "Scoring item content",
		"请选择要修改的质检对象" : "Please select the quality inspection object to modify",
		"减分项数" : "Deduct score item",
		"文件名" : "file name",
		"条件时才质检" : "Quality inspection only when conditions",
		"工单" : "Ticket",
		"任务调配" : "Task deployment",
		"查找对象账号" : "Find object account",
		"有礼貌请客户重复" : "Politely ask the customer to repeat",
		"多选" : "multi-select",
		"不及格率" : "Unpass rate",
		"话务质检平均分：人工已质检的话务的平均得分" : "Average score of traffic quality inspection: the average score of manual inspected traffic",
		"全媒体抽检量：所选坐席在抽检日期范围内被抽检的全媒体数量" : "All media sampling quantity: the number of all media sampled by the selected seat within the sampling date range",
		"一票否决命中次数排行" : "One-vote veto hits ranking",
		"值包含" : "Value contains",
		"（系统抽取记录到质检库里后，只放到当前任务对应的质检组里，当前组里的所有状态为启用的质检员可以通过随机抽取、指定条件抽取方式，主动领取质检记录）" : "(after the system extracts records from the quality inspection library, they are only placed in the quality inspection group corresponding to the current task. All quality inspectors in the current group whose status is enabled can actively collect quality inspection records by random extraction and specified condition extraction)",
		"导入成功" : "Import successful",
		"坐席抽检分析" : "Seat sampling analysis",
		"质检员评分统计" : "Score statistics of quality inspectors",
		"在质检该数据, 请联系该质检员或者重新调配" : "Please contact the quality inspector or redeploy the data during quality inspection",
		"智能质检规则" : "Intelligent quality inspection rules",
		"运行时间配置" : "Runtime configuration",
		"坐席评分细项报表" : "Agent scoring detail report",
		"一般合格9" : "Generally acceptable 9",
		"人工质检结果表" : "Manual quality inspection result table",
		"一般合格8" : "Generally qualified 8",
		"是否导出整体话务质检表" : "Export overall traffic quality inspection table",
		"一般合格5" : "Generally qualified 5",
		"批量移除" : "Bulk removal",
		"一般合格4" : "Generally qualified 4",
		"一般合格7" : "Generally acceptable 7",
		"一般合格6" : "Generally qualified 6",
		"一般合格1" : "General pass 1",
		"4、已质检数：已人工抽取并且进行人工质检的数据,若开启发布就是已发布的数据" : "4. Quantity inspected: data that has been manually extracted and inspected. If publishing is enabled, it is published data",
		"一般合格2" : "Generally acceptable 2",
		"申诉量" : "Appeal volume",
		"执行机器MAC" : "Execution machine MAC",
		"时间段集合" : "Time period collection",
		"智能质检平均分" : "Intelligent quality inspection average score",
		"结果评定等级" : "Result rating",
		"修改分类" : "Edit Catagory",
		"配置时间段" : "Configure time period",
		"超时结束" : "Timeout ended",
		"质检抽取方式" : "Inspection extraction method",
		"统计JSON" : "Statistics JSON",
		"小结描述" : "Summary description",
		"1、坐席数：任务的质检组中的质检坐席数" : "1. Agents quantity: the quantity of quality inspection agents in the quality inspection group of the task",
		"质检结果修改" : "Edit quality inspection results",
		"结束挂断类型" : "relase type",
		"审核不通过" : "Review not passed",
		"良" : "well",
		"输入坐席账号、坐席姓名" : "Enter seat number, seat name",
		"语音抽检分析：使用柱状图统计抽检语音最多的前10个坐席的数量分布" : "Voice sampling analysis: use the histogram to count the number distribution of the top 10 seats with the most voice sampling",
		"6、人工质检平均分：人工质检平均分每个任务中每个人工质检结果得分的平均分" : "6. Average score of manual quality inspection: the average score of each manual quality inspection result in each task",
		"明细" : "The detail",
		"话务" : "Traffic",
		"规则描述" : "Rule Description",
		"质检数量分析" : "Quality inspection quantity analysis",
		"抽取日期" : "Extraction date",
		"通话日期" : "Call Date",
		"计费开始时间" : "Billing start time",
		"类型" : "type",
		"按条件抽取" : "Conditional extraction",
		"执行时间" : "execution time",
		"随机抽取" : "Random extraction",
		"智能质检结果明细表" : "Intelligent quality inspection result detailed table",
		"常用语" : "Phrase",
		"二级评定" : "Second level assessment",
		"智能质检统计" : "Intelligent quality inspection statistics",
		"天之前的数据" : "days ago' data",
		"质检坐席数：被质检的坐席数量" : "Number of quality inspection seats: number of seats to be inspected",
		"质检统计图表" : "Quality Inspection statistical chart",
		"质检项排行" : "Quality inspection item ranking",
		"坐席质检统计" : "Agent quality inspection statistics",
		"评分细则" : "Scoring rules",
		"语音申诉量" : "Voice appeal volume",
		"启动任务" : "Start task",
		"取值范围：0~10000" : "Ranges：0~10000",
		"确认更换质检员？" : "Confirm replace quality inspector?",
		"审核通过" : "Approved",
		"预拨号呼出" : "Pre-dial call",
		"抽取数" : "Extraction number",
		"重新抽取" : "Re-extract",
		"7、人工一票否决量：人工质检结果命中一票否决的记录" : "7. Manual one vote veto quantity: records of manual quality inspection results hitting one vote veto",
		"评分细项描述" : "Scoring detailed item description",
		"重新统计" : "Re-statistics",
		"查询评分细项统计报表失败" : "Failed to query scoring item details statistics report",
		"工号" : "Employee number",
		"服务类型" : "Service type",
		"申诉是否需要组长审核" : "Does the appeal need to be reviewed by the team leader",
		"IVR转入" : "IVR transfer in",
		"备注:" : "Remarks:",
		"抽取规则信息" : "Extract rule information",
		"质检评论" : "Quality inspection comment",
		"质检坐席" : "Quality inspection agent",
		"设为组长" : "As the group leader",
		"服务时长(s)" : "Service time(s)",
		"语音抽检量：所选坐席在抽检日期范围内被抽检的语音数量" : "Voice sampling quantity: the number of voice sampled by the selected seat within the sampling date range",
		"特别合格" : "Special qualified",
		"关闭" : "Close",
		"人工质检比率(%)" : "Manual quality inspection ratio (%)",
		"人工质检平均分" : "Manual quality inspection average score",
		"一票否决量：一票否决的质检项的命中数量" : "One vote veto quantity: hit quantity of quality inspection items with one vote veto",
		"没有选中数据，请刷新页面重新操作" : "No data is selected, please refresh the page to operate again",
		"及格分数0~10000，不能为空" : "Passing score 0~10000, cannot be empty",
		"已暂停" : "Paused",
		"人工质检结果" : "Manual quality inspection results",
		"任务基本信息" : "Basic task information",
		"选择质检对象" : "Select quality inspection object",
		"操作成功!" : "Operation succeeded",
		"查看详情" : "See details",
		"请最少选择一个评定项" : "Please select at least one scoring item",
		"角色" : "Roles",
		"全媒体一票否决量：所选质检员在抽检日期范围内质检的全媒体一票否决量" : "All media one vote veto quantity: all media one vote veto quantity of the selected quality inspector for quality inspection within the sampling inspection date",
		"及格分数" : "Passing score",
		"数据总览" : "data screening",
		"反馈描述" : "Feedback description",
		"全媒体抽检数" : "Number of all media spot checks",
		"是否允许二次申诉" : "Whether to allow second appeal",
		"系统分配" : "System allocation",
		"很好" : "Very good",
		"通过" : "Pass",
		"已重新抽取，请刷新页面" : "Re-extracted, please refresh the page",
		"推送引擎数" : "Number of push engines",
		"质检组名称" : "Quality inspection group name",
		"服务态度不好" : "Poor service attitude",
		"待质检列表" : "Pending quality inspection list",
		"修改成功" : "Edit successful",
		"通过申诉" : "Approve appeal",
		"一般合格" : "General qualified",
		"是否释放选中数据？" : "Do you want to release the selected data?",
		"任务" : "Tasks",
		"全媒体质检分析：使用柱状图统计质检全媒体最多的前10个质检员的质检数量分布" : "All media quality inspection analysis: use the histogram to count the quality inspection quantity distribution of the top 10 quality inspectors with the most all media quality inspection",
		"时间范围" : "Time limit",
		"挂机原因" : "hang up cause",
		"通话时长" : "Call duration",
		"录音质检统计" : "Voice recording quality assurance statistics",
		"如果是多选数据请选择15个以内的数据,动态时间类型仅在每条录音只质检一次的情况下生效" : "If you select multiple data, please select less than 15 data. The dynamic time type takes effect only when each recording is inspected only once",
		"条件抽取" : "Conditional extraction",
		"抽取时间" : "Extraction time",
		"按日期" : "By date",
		"结果" : "result",
		"任务管理" : "Task management",
		"加分项数" : "Add score item",
		"灵活推荐/采用合适的解决方案" : "Flexibly recommend / adopt appropriate solutions",
		"退回成功" : "Return successful",
		"坐席姓名" : "Agent name",
		"质检评定" : "Quality inspection evaluation",
		"+添加关联" : "add associations",
		"优秀录音" : "Excellent recording",
		"质检组" : "Quality inspection group",
		"本周总量均衡" : "Total balance this week",
		"业务能力突出" : "Outstanding business capability",
		"任务自动分配最大值" : "Maximum automatic assignment of tasks",
		"历时" : "Duration",
		"数据操作重新统计确认" : "Are you Confirm Re-statistics?",
		"全媒体申诉量：所选质检员在抽检日期范围内质检的全媒体被坐席发起申诉的量" : "Amount of all media complaints: the amount of all media complaints initiated by the selected quality inspector within the sampling date",
		"人工一票否决量" : "Manual one vote veto quantity",
		"转派" : "Reassignment",
		"5、智能质检率：任务中发起智能质检的数据占比：智能质检数/总数" : "5. Intelligent quality inspection rate: the proportion of data that initiates intelligent quality inspection in the task: intelligent quality inspections quantity/total",
		"最近一天" : "Last Day",
		"如果是多选请选择15个以内的数据!" : "If it is multiple, please select less than 15 data!",
		"右键菜单操作" : "Right-click menu operation",
		"网页" : "Web page",
		"组长审核申诉" : "Team leader review and appeal",
		"8、人工质检平均分：每个任务中每个人工质检结果得分的平均分" : "8. Manual quality inspection average score: the average scores of each manual quality inspection result in each task",
		"保存成功" : "Save successful",
		"全媒体质检数据" : "All media quality inspection data",
		"抽检最多：抽检话务全媒体最多的坐席名称、质检数" : "Maximum number of spot checks: the name and number of quality inspectors who spot check the most traffic all media",
		"任务结束时间" : "Task end time",
		"质检抽取分析：再下面使用折线图，显示选择区间内，每天的抽检总量" : "Quality inspection sampling analysis: next, use the line chart to display the total sampling quantity of each day in the selected interval",
		"加分" : "Add score",
		"请先选择质检任务！" : "Please select a quality inspection task first!",
		"（计算当前质检组里所有状态为启用的质检员本月在该质检任务里的总量（包含已质检的、待质检的总量），按总量进行均衡分配，优先分配给总量少的质检员）" : "(calculate the total amount (including the total amount of inspected and to be inspected) of all quality inspectors in the active status in the current quality inspection group in the quality inspection task in this month, distribute them evenly according to the total amount, and give priority to the quality inspectors with less total amount)",
		"已质检" : "Inspected",
		"统计查询" : "Statistics inquiry",
		"任务明细数据" : "Task detail data",
		"是否确定退回当前申诉" : "Are you sure you want to return the current appeal?",
		"修改失败" : "Edit failed",
		"用户挂断" : "User hangs up",
		"是否确定删除当前数据？" : "Are you sure to delete the current data?",
		"通话日期" : "Call date",
		"质检运行时段" : "Quality inspection operation period",
		"每天指定时间段" : "Specified time period per day",
		"1、坐席：统计的坐席" : "1. Agent: statistical agent",
		"待抽取" : "To be extracted",
		"语音一票否决量：所选质检员在抽检日期范围内质检的语音一票否决量" : "Voice one vote veto quantity: voice one vote veto quantity of quality inspection of the selected quality inspector within the sampling date range",
		"抽检最少" : "Sampling at least",
		"待审核" : "Review Pending",
		"录音文件" : "Recording files",
		"请进行任务管理配置" : "Please configure task management",
		"添加失败" : "Add failed",
		"合格分数" : "Passed score",
		"个人常用语导入" : "Personal Phrase Import",
		"质检对象" : "Quality inspection object",
		"每周指定时间段" : "Specified time period per week",
		"静音" : "Mute",
		"质检任务:" : "Quali_Task:",
		"全选" : "Select all",
		"会话接入" : "Session access",
		"最近一个月" : "Last month",
		"坐席:" : "Agent:",
		"是否导出质检任务统计表" : "Do you want to export the quality assurance task statistics table?",
		"导入失败" : "Import failed",
		"请不要输入<>这些字符" : "please do not enter < > these characters",
		"我熟人" : "My acquaintances",
		"选择质检坐席" : "Select quality inspection agent",
		"满意度" : "Satisfaction Rate",
		"语音一票否决量" : "Voice one vote veto",
		"聊天记录" : "Chat record",
		"全媒体平均分最高：包含全媒体所有质检记录平均分最高的坐席、分数" : "Highest average score of all media: including the seats and scores with the highest average score of all quality inspection records of all media",
		"待质检数" : "Quality inspection pending quantity",
		"条件" : "Conditions",
		"非关键" : "Non critical",
		"我的规划建议" : "My planning suggestions",
		"质检项排行：统计所有的质检项之中平均得分最高的前十个质检项" : "Quality inspection item ranking: statistics the top ten quality inspection items with the highest average score among all quality inspection items",
		"一票否决数" : "One vote veto quantity",
		"来源" : "Source",
		"1、话单总数：坐席对应坐席或日期的话单总数" : "1. Total tickets: the total tickets corresponding to the agent or date",
		"评定等级分布" : "Rating distribution",
		"质检对象维护" : "Quality inspection object maintenance",
		"是否有一票否决" : "One vote item",
		"质检员工作分析" : "Job analysis of quality inspector",
		"7、人工质检未完成数：质检员已抽取但未质检的数量" : "7. Manual quality inspections uncompleted quantity: the un inspected quantity that the quality inspector has extracted",
		"结案" : "Case closed",
		"立即执行" : "Apply now",
		"人工质检完成量" : "Manual quality inspection completed quantity",
		"保存失败" : "Save failed",
		"渠道类型" : "Channel type",
		"质检结果隐藏质检员" : "Hidden quality inspector of quality inspection results",
		"统计口径" : "Statistical caliber",
		"对此质检项作出建议" : "Suggestions on this quality inspection",
		"智能质检数" : "Intelligent quality inspection quantity",
		"人工质检分数" : "Manual quality inspection score",
		"话务质检数：人工质检的话务总数" : "Traffic quality inspection quantity: the total number of traffic inspected manually",
		"智能质检结果数" : "Intelligent quality inspection results quantity",
		"优秀率" : "Excellent rate",
		"通话保持开始提醒" : "Call hold start reminder",
		"全部规则建议" : "All rule recommendations",
		"13、人工一票否决量：人工质检结果命中一票否决的记录" : "13. Manual one vote veto quantity: record of manual quality inspection results hitting one vote vet",
		"请选择质检员！" : "Please select a quality inspector!",
		"处理时间" : "processing time",
		"第二次申诉" : "Second appeal",
		"尾页" : "Last page",
		"人工质检未完成数" : "Manual quality inspections uncompleted quantity",
		"二次申诉修改" : "Second appeal modification",
		"二次待确认" : "Second appeal to be confirmed",
		"人工质检合格率(%)" : "Pass rate of manual quality inspection (%)",
		"质检结果" : "Quality inspection results",
		"状态" : "Status",
		"外部模块质检规则地址" : "External module quality inspection rule address",
		"通话时长(s)" : "Call duration (s)",
		"部门:" : "Dept:",
		"全媒体抽检分析：使用柱状图统计抽检全媒体最多的前10个坐席的数量分布" : "All media sampling analysis: use the histogram to count the number distribution of the top 10 seats with the most all media sampling",
		"礼貌性结束用语" : "Polite ending",
		"是否导出录音质检统计表" : "Do you want to export the recording quality inspection statistics table?",
		"扣分" : "Deduct score",
		"近n天" : "last n days",
		"处理结果" : "Process results",
		"全媒体质检量：所选质检员在抽检日期范围内质检的全媒体数量" : "All media quality inspection quantity: the quantity of all media inspected by the selected quality inspector within the sampling date range",
		"4、分数等级：质检数据的得分等级,根据'系统管理'>>'参数配置'>>'质检参数配置'  里的'质检分数配置' 决定优秀,良好,一般,不及格的范围" : "4. Score level: the score level of quality inspection data. According to the 'quality inspection score configuration' in 'system management' >'parameter configuration '>'Quality inspection parameter configuration', the range of excellent, good, average and unpass is determined",
		"质检组管理" : "Quality inspection group management",
		"优秀评定等级编号" : "Excellent rating grade No",
		"分数" : "Score",
		"操作" : "operate",
		"质检任务" : "Quality inspection tasks",
		"没有通话录音文件" : "No call recording files!",
		"一般" : "commonly",
		"否" : "No",
		"请选择" : "Please Select",
		"不满意" : "dissatisfied",
		"下载导入模板" : "Download the imported template",
		"任务名称" : "Task name",
		"按质检员" : "By inspector",
		"本日" : "today",
		"查找对象" : "Search objects",
		"话务平均分最高" : "The average traffic score is the highest",
		"结束原因" : "Reason for ending",
		"暂无数据" : "No data",
		"坐席名称" : "Agent name",
		"当天" : "today",
		"质检员账号" : "Quality inspector account",
		"同时满足以下" : "Also meet the following",
		"质检申诉分析：使用两条折线图，统计每天的发起申诉量、申诉审核通过量" : "Quality inspection appeal analysis: use two broken line charts to count the number of appeals initiated and approved every day",
		"抽取规则使用中" : "The extraction rule is already in use",
		"日期范围" : "date range",
		"已有任务在质检该数据，您还不是任务" : "There is a task checking the data. You are not a task yet",
		"评论内容" : "Comment content",
		"非常满意" : "Very satisfied",
		"发布" : "relase",
		"语气不耐烦" : "Tone impatient",
		"抽取人" : "Extractor",
		"我已质检" : "inspected",
		"呼入" : "Incoming Call",
		"统计名称" : "Statistics Name",
		"规则建议" : "Rule suggestion",
		"等于" : "equal",
		"质检规则评分项" : "Quality Inspection Rules Scoring Items",
		"人工质检整体分析" : "Overall analysis of manual quality inspection",
		"语音抽检分析" : "Voice sampling analysis",
		"10、动态列：除了上述固定列，其余未动态类，即是所选质检任务质检规则中的评分项信息" : "10. Dynamic column: in addition to the above fixed columns, there are no dynamic classes, that is, the scoring item information in the quality inspection rules of the selected quality inspection task",
		"1、话单总数：对应坐席、质检员或者按日期统计的话单总数" : "1. Total tickets: the total tickets counted by corresponding agent, quality inspectors or by date",
		"数据权限" : "data permission",
		"质检统计分析" : "Statistical analysis of quality inspection",
		"取消" : "cancel",
		"整体全媒体质检" : "Overall all media quality inspection",
		"1、仅显示人工质检结果。" : "1. Only display the manual quality inspection results.",
		"主动发起" : "Session Initiative",
		"质检分数配置" : "Quality inspection score configuration",
		"抽检最多" : "Most spot checks",
		"人很好看" : "Who is very good-looking",
		"全部" : "All",
		"建议内容" : "Suggestion content",
		"3、得分分布：所有质检项的的得分分布" : "3. Score distribution: the score distribution of all quality inspection items",
		"（计算当前质检组里所有状态为启用的质检员本周在该质检任务里的总量（包含已质检的、待质检的总量），按总量进行均衡分配，优先分配给总量少的质检员）" : "(calculate the total amount (including the total amount of inspected and to be inspected) of all quality inspectors in the active status in the current quality inspection group in the quality inspection task this week, distribute it evenly according to the total amount, and give priority to the quality inspectors with less total amount)",
		"姓名" : "Name",
		"质检比例：人工质检量所占质检总数的比例" : "Quality inspection proportion: the proportion of manual quality inspection quantity in the total quality inspection quantity",
		"输入质检员账号,姓名" : "Enter the account number and name of the quality inspector",
		"总数" : "Total",
		"针对该质检规则提出建议" : "Put forward suggestions for the quality inspection rules",
		"正在抽取，稍后请刷新页面查看" : "Extracting, please refresh the page to view later",
		"二次申诉" : "Second appeal",
		"自定义区间" : "Custom interval",
		"释放" : "Release",
		"请选择要抽取的坐席！" : "Please select the seat to be extracted！",
		"任务分配" : "Task Assignment",
		"二次申请" : "Second application",
		"全媒体质检修改" : "Modification of all media quality inspection ",
		"抽取状态" : "Extraction status",
		"只能查看:" : "View only:",
		"选择文件" : "Select a document",
		"全媒体申诉量" : "All media complaints",
		"及格数" : "Pass quantity",
		"先智能质检再人工质检" : "Intelligent quality inspection first and then manual quality inspection",
		"是否确定" : "Are you sure",
		"结果修改" : "Result modification",
		"所有" : "All",
		"数据统计结束时间" : "Data Statistics End Time",
		"整体话务质检" : "Overall traffic quality inspection",
		"质检及格=质检得分>及格分数" : "Pass the quality inspection=Quality inspection score>Passing score",
		"全媒体平均分最高" : "The average score of all media is the highest",
		"上一页" : "previous page",
		"质检员选择" : "Select quality inspector",
		"未质检" : "Un-inspected",
		"按坐席" : "By agent",
		"汇总" : "Summary",
		"修改常用语" : "Edit Phrase",
		"质检员" : "Quality inspector",
		"质检平均分：已质检数据的平均得分" : "Quality inspection average score: the average score of quality inspection data",
		"7、人工质检总分：每个任务中每个人工质检结果得分的总和" : "7. Manual quality inspection total score: the sum of the scores of each manual quality inspection result in each task",
		"申诉状态" : "Appeal status",
		"人工抽检率(%)" : "Manual sampling rate (%)",
		"一键质检时手动选择质检任务" : "Manually select quality inspection tasks during one-click quality inspection",
		"全媒体平均分分析：使用柱状图统计抽检全每天平均分最高的前10个坐席，显示每个坐席的平均分" : "All media average score analysis: use histogram statistics to spot check the top 10 seats with the highest daily average score, and display the average score of each seat",
		"智能质检比率" : "Intelligent quality inspection ratio",
		"语调亲切、委婉" : "The tone is kind and euphemistic",
		"请选择上传的文件" : "Please select an uploaded file!",
		"退回原因不能为空" : "Return reason cannot be empty",
		"表现良好" : "To perform well",
		"呼出" : "Outbound call",
		"会话编号" : "Session number",
		"规则名称不能为空" : "Rule name cannot be empty",
		"外线呼入" : "Incoming call",
		"申诉" : "Appeal",
		"的质检员，无法操作该数据" : "The quality inspector cannot operate the data",
		"提交人姓名" : "Submitter",
		"系统自动结案" : "The system automatically closes the case",
		"良好" : "good",
		"质检进度" : "Quality inspection progress",
		"语音质检量：所选质检员在抽检日期范围内质检的语音数量" : "Voice quality inspection quantity: the voice quality inspection quantity of the selected quality inspector within the sampling date range",
		"新增质检规则" : "Add quality inspection rules",
		"客户姓名" : "Customer's name",
		"一票否决命中次数排行：统计一票否决评分细项中出现次数最多的前十项" : "Ranking of hit times of one vote veto: count the top ten items with the highest number of times in the scoring items of one vote veto",
		"评定结果" : "Evaluation results",
		"来源说明" : "Source description",
		"系统提示" : "system reminder",
		"申诉内容" : "Appeal content",
		"清空" : "Clear",
		"主叫" : "Caller",
		"暂存" : "Save",
		"添加质检员" : "Add quality inspector",
		"最少" : "least",
		"质检结果明细" : "Quality inspection result details",
		"情绪异常" : "Abnormal emotion",
		"时间段" : "time slot",
		"知识标题" : "Knowledge title",
		"待质检录音记录" : "Pending quality inspection recording",
		"不合格" : "unqualified",
		"人工质检数量趋势" : "Manual inspection",
		"6、未及格数：人工质检不合格的数据，已质检数 - 及格数" : "6. Unpass quantity: Manual quality inspection unpassed data, quality inspections completed quantity-Pass quantity",
		"质检数量" : "Quality inspection quantity",
		"合格" : "qualified",
		"一般合格101" : "General pass 101",
		"展开" : "Unfold",
		"查看抽取记录" : "View extraction records",
		"本月总量均衡" : "Total balance of this month",
		"业务名称" : "Task name",
		"质检组:" : "Quality inspection team:",
		"组长审批申诉" : "Group leader approves representations",
		"本月" : "this month",
		"不及格" : "fail",
		"全媒体质检" : "All media quality inspection",
		"质检员姓名" : "Quality inspector names",
		"查看工单" : "View ticket",
		"处理人账号" : "Processor account number",
		"该质检分组存在质检员或坐席，不允许删除!" : "The inspection group cannot be deleted because there are inspectors or agents in the group.",
		"日期范围:" : "Date range:",
		"申诉成功" : "Appeal successful",
		"快进" : "Fast forward",
		"分配质检员" : "Assign quality inspectors",
		"客户姓名/主叫" : "Customer name / Caller",
		"任务执行日志" : "Task execution log",
		"创建原因" : "Creation reason",
		"人工质检合格率" : "Manual quality inspection passing rate",
		"请选择要释放的数据！" : "Please select the data to be released!",
		"不文明用语" : "Uncivilized language",
		"通话记录是否自动分配" : "Are call records automatically assigned",
		"质检最多" : "Most quality inspection",
		"全媒体平均分：所选质检员在抽检日期范围内质检的全媒体平均分" : "All media average score: the average score of the selected quality inspector for quality inspection within the sampling date range",
		"班组长审批申诉" : "The team leader approves the appeal",
		"全媒体平均分" : "All media average",
		"话务平均分" : "Traffic average score",
		"不出现冷场" : "There is no cold field",
		"全媒体质检平均分" : "All media quality inspection average score",
		"录音质检修改" : "Modification of recording quality inspection",
		"任务开始时间" : "Task start time",
		"6、人工质检完成数：已经完成人工质检的数量" : "6. Manual quality inspections completed quantity: manual quality inspections completed quantity",
		"设置坐席质检数(为空不限制)" : "Set agent quality inspections quantity (no limit when empty)",
		"合格率" : "Pass rate",
		"结束时间" : "End Time",
		"评分细项内容" : "Scoring detailed item content",
		"已处理" : "Processed",
		"日期参数不能为空" : "Date parameter cannot be empty",
		"优秀" : "excellent",
		"呼入时长(秒)" : "Incoming call duration (seconds)",
		"最小1，最大31" : "min 1, max 31",
		"（查询出当前质检组里的状态为启用的质检员，将本次的抽取量，平均分配给各个坐席）" : "(query the quality inspectors in the current quality inspection group whose status is enabled, and evenly allocate the current extraction quantity to each agent)",
		"请选择要修改的质检员" : "Select the inspector to be modified",
		"良好率" : "Good rate",
		"分数等级：质检数据的得分等级,根据 系统管理 >> 参数配置 >> 质检参数配置里的质检分数配置 决定优秀,良好,一般,不及格的范围" : "Score grade: the score grade of quality inspection data. The range of excellent, good, average and unqualified is determined according to the quality inspection score configuration in system management > > parameter configuration > > quality inspection parameter configuration",
		"启动中" : "Starting",
		"运行时间规则" : "Run time rule",
		"评论列表" : "Comment list",
		"字数不超过200" : "Words cannot exceed 200",
		"及格" : "Pass",
		"后退" : "Back",
		"得分分布：所有质检项的的得分分布" : "Score distribution: the score distribution of all quality inspection items",
		"全媒体平均分：质检的坐席的全媒体平均分" : "All media average score: the all media average score of the quality inspection agent",
		"找不到对应的质检结果" : "The corresponding quality inspection result cannot be found",
		"质检数量趋势：统计每日新增的质检总数、每日进行的质检总数" : "Quality inspection quantity trend: statistics the total number of newly added quality inspections and the total number of quality inspections performed each day",
		"二次待审核" : "Second approve pending",
		"计费结束时间" : "Billing end time",
		"小结内容" : "Summary content",
		"表现一般" : "Average performance",
		"标注" : "Remark",
		"全媒体申诉量：所选坐席在抽检日期范围内发起的全媒体申诉量" : "All media appeals: the number of all media appeals initiated by the selected seat within the sampling date range",
		"语音申诉量：所选坐席在抽检日期范围内发起的语音申诉量" : "Voice appeal volume: the voice appeal volume initiated by the selected agent within the sampling date range",
		"日期" : "date",
		"值存在于" : "Value exists in",
		"是否允许二次申诉：" : "Whether secondary appeal is allowed:",
		"删除任务" : "Delete task",
		"申诉修改" : "Appeal modification",
		"语音平均分：所选坐席在抽检日期范围内被质检的语音平均得分" : "Average voice score: the average voice score of the selected agent inspected within the sampling date range",
		"8、人工质检平均分每个任务中每个人工质检结果得分的平均分" : "8. Average score of manual quality inspection: the average score of each manual quality inspection result in each task",
		"5、未及格数：人工质检不合格的数据，已质检数 - 及格数" : "5. Failed Qty: the data that fails manual quality inspection. Inspected quantity - passed quantity",
		"删除分类" : "Delete Catagory",
		"智能质检总分" : "Intelligent quality inspection total score",
		"呼叫结果" : "Call result",
		"全媒体任务数据最大查找天数" : "Maximum number of days to find all media task data",
		"人均抽检数据" : "Sampling data per capita",
		"差" : "poor",
		"质检评论列表" : "Quality inspection comment list",
		"质检详情" : "Quality inspection details",
		"是否确定审核通过该数据申诉，并修改上次质检结果" : "Are you sure to approve this data appeal and modify the last quality inspection result?",
		"提交人账号" : "Submitter account",
		"执行时长秒" : "Execution Time(Second)",
		"录音信息" : "Recording information",
		"微信" : "WeChat",
		"8、目标质检率(%)：新建任务时的人工质检目标" : "8. Target quality inspection rate (%): manual quality inspection target when creating a new task",
		"新增抽取规则" : "Add extraction rules",
		"该质检组存在质检员，不能删除" : "The quality inspection group has quality inspector and cannot be deleted!",
		"结果评定" : "Result evaluation",
		"抽取已存在" : "The name of the extraction rule already exists",
		"全媒体质检统计" : "Media quality assurance statistics",
		"人工质检时间" : "Manual quality inspection time",
		"坐席被抽检数(为空不限制)" : "Number of seats sampled (no limit if empty)",
		"选择质检员" : "Select quality inspector",
		"批量分配" : "Batch allocation",
		"描述" : "description",
		"最近一周" : "Last Week",
		"该质检组存在质检对象，不能删除" : "The quality inspection group has quality inspection objects and cannot be deleted!",
		"人工质检目标" : "Manual quality inspection target",
		"服务日期" : "Service date",
		"单次均衡" : "Single equilibrium",
		"地址" : "address",
		"质检任务统计": "Quality assurance task statistics",
		"坐席原始数据抽取率(为空不限制)" : "Agent raw data extraction rate (if it is empty, there is no limit)",
		"质检项类型" : "Quality inspection item type",
		"（计算当前质检组里所有状态为启用的质检员在该质检任务里待处理的总量（只包含待质检的总量），按待处理总量进行均衡分配，优先分配给待处理总量少的质检员）" : "(calculate the total quantity to be processed (including only the total quantity to be inspected) of all quality inspectors in the active status in the current quality inspection group in the quality inspection task, distribute it evenly according to the total quantity to be processed, and give priority to the quality inspectors with less total quantity to be processed)",
		"点击新增该评分项的评分细项" : "Click to add the detailed item of scoring item",
		"第二次申诉说明" : "Explanation of the second appeal",
		"质检顺序" : "Quality inspection order",
		"二次申诉审核" : "The second appeal review",
		"全媒体一票否决量：所选坐席在抽检日期范围内被一票否决的全媒体量" : "One vote veto of all media: the number of all media rejected by one vote within the sampling date range of the selected seat",
		"呼叫类型" : "Call type",
		"是" : "Yes",
		"按模块抽取" : "Extraction by module",
		"质检评定 总分:" : "Quality inspection total score:",
		"请选择正确的时间范围，时间不能大于30天" : "Please select the correct time range, the time cannot be greater than 30 days",
		"合理使用欢迎语" : "Use greetings appropriately",
		"会话记录是否自动分配" : "Whether session records are automatically assigned",
		"专业能力一般" : "General professional ability",
		"统计日期" : "Statistical date",
		"人工质检完成数" : "Manual quality inspections completed quantity",
		"播放" : "Play",
		"不自动分配" : "Do not assign automatically",
		"是否导出坐席质检统计表" : "Export agent quality inspection statistics",
		"渠道" : "channel",
		"每月指定日期和时间段" : "Specify the date and time period of each month",
		"2、类型：渠道类型" : "2. Type: channel type",
		"开始时间" : "Starting Time",
		"语速快慢适中" : "Speak at a moderate speed",
		"删除成功" : "Delete successful",
		"9、智能质检总分：每个任务中每个智能质检结果得分的总和" : "9. Intelligent quality inspection total score: the sum of the scores of each intelligent quality inspection result in each task",
		"在区间内" : "Within the interval",
		"质检申诉" : "Quality inspection appeal",
		"隐藏显示" : "Hide",
		"10、智能质检平均分：每个任务中每个智能质检结果得分的平均分" : "10. Intelligent quality inspection average score: the average score of each intelligent quality inspection result score in each task",
		"请输入备注信息" : "Please enter the remark information",
		"添加质检坐席" : "Add quality inspection agent",
		"3、总数：任务质检的总数据量" : "3. Total: the total data volume of quality inspection task",
		"人工质检率(%)" : "Manual quality inspection rate (%)",
		"一级评定" : "First level assessment",
		"全媒体抽检量" : "All media sampling volume",
		"次数" : "Count",
		"每月最多" : "Monthly maximum",
		"播放录音" : "Play recording",
		"质检任务数" : "Number of quality inspection tasks",
		"技能组" : "Tech Group",
		"服务时长" : "Service time",
		"质检规则预览" : "Quality inspection rules preview",
		"已存在当前质检结果的申诉，请不要重复申诉" : "Already have an appeal for the current quality inspection results, please do not repeat the appeal!",
		"质检最少" : "Minimum quality inspection",
		"请求时间" : "Request time",
		"任一" : "Either",
		"10、人工质检率(%)：人工抽取并且质检完成的数量/任务质检的总数据量" : "10. Manual quality inspection rate (%): manual extracted and quality inspection completed quantity/total data volume of quality inspection task",
		"评分方式" : "Score Type",
		"分配时间" : "Allocate time",
		"坐席事件" : "Agent event",
		"一票否决坐席数" : "Number of seats with one vote veto",
		"周日" : "Sunday",
		"规则建议详情" : "Details of proposed rules",
		"二次申诉审核通过，修改分数" : "The second appeal review passed, modify the score",
		"规则建议(处理/未处理)" : "Rule recommendations (processed/unprocessed)",
		"找不到记录" : "No record found",
		"质检坐席数" : "Number of quality inspection seats",
		"3、待质检数：已人工抽取但没有完成人工质检的数据" : "3. Number to be inspected: data that has been manually extracted but not completed",
		"质检总数" : "Quality inspection total quantity",
		"周一" : "Monday",
		"请上传文件" : "Please upload the file",
		"退回申诉" : "Return appeal",
		"优秀:" : "excellent:",
		"抽取失败" : "Extraction failed",
		"语音任务数据最大查找天数" : "Maximum number of days to find voice task data",
		"评分项名称" : "Scoring item name",
		"待质检数：人工质检已抽取，待质检的数量" : "Quantity to be inspected: the quantity to be inspected after manual inspection",
		"任务开始日期" : "Task start date",
		"周三" : "Wednesday",
		"语音质检分析：使用折线图，显示该质检员统计日期段内每天的语音质检数量" : "Voice quality inspection analysis: use a line chart to display the voice quality inspection quantity per day within the statistical date period of the quality inspector",
		"话务抽检数" : "Traffic sampling number",
		"未及格数" : "Unpassed Quantity",
		"未知异常，请联系管理员" : "Unknown exception, please contact the administrator",
		"抽检最少：抽检话务全媒体最少的坐席名称、质检数" : "Spot check minimum: spot check the minimum number of seats and quality inspection numbers in all media",
		"确认同步常用语到缓存" : "Are you Confirm Sync Phrase to Cache?",
		"获取录音文件成功" : "Successfully get the recording file.",
		"用户主动结束服务" : "User actively ends the service",
		"添加时间" : "Add time",
		"所属渠道" : "Affiliated Channels",
		"全媒体平均分：所选坐席在抽检日期范围内被质检的全媒体平均得分" : "Average score of all media: the average score of all media inspected by the selected agent within the sampling date range",
		"质检得分=基本分数+（质检项得分总分）" : "Quality inspection score=Basic score+（Total score of quality inspection items）",
		"成功删除" : "Delete successful",
		"删除失败" : "Delete failed",
		"4、及格数：已人工抽取并且进行人工质检的数据,若开启发布就是已发布的数据" : "4. Pass count: data that has been manually extracted and subjected to manual quality inspection. If publishing is enabled, it is published data",
		"数据统计日志" : "Data Statistics Log",
		"同时符合以下" : "Also meet the following",
		"全媒体抽检分析" : "All media sampling analysis",
		"退回" : "Return",
		"全媒体话单" : "All media ticket",
		"是否单选" : "Single choice",
		"人工、智能质检并行" : "Manual and intelligent quality inspection in parallel",
		"图表说明" : "Chart description",
		"评分项配置" : "Scoring items configuration",
		"二次申诉审核不通过" : "The second appeal review not passed",
		"新增常用语" : "Add Phrase",
		"服务时间" : "service time",
		"请先选择质检任务" : "Please select the quality inspection task first！",
		"话务质检平均分" : "Average traffic quality inspection score",
		"登录帐号" : "login account",
		"智能质检结果表" : "Intelligent quality inspection result table",
		"是否导出质检员评分统计表" : "Export quality inspector score statistics",
		"智能质检分数" : "Intelligent quality inspection score",
		"抽取数量" : "Extraction quantity",
		"一票否决" : "One vote veto",
		"质检数量分析：使用柱状图图，显示选择区间内，每天的质检数量" : "Quality inspection quantity analysis: use the histogram to display the daily quality inspection quantity in the selected interval",
		"申诉数" : "Appeal quantity",
		"处理规则建议" : "Suggestions on processing rules",
		"质检数量趋势：统计每日新增的质检总数、每日进行的质检总数，每日一票否决数" : "Quality inspection quantity trend: statistics the total number of newly added quality inspections, the total number of quality inspections performed every day, and the number of one vote veto per day",
		"规则建议列表" : "Rule suggestion list",
		"分值" : "Score",
		"得分分布" : "Score distribution",
		"是否分配给质检组组长" : "Assign to quality inspection team leader",
		"质检员维护" : "Quality inspector maintenance",
		"4、已质检数：已人工抽取并且进行人工质检的数据" : "4. Quality inspections completed quantity: Already manually extracted and manual quality inspection processing data,If you open the release, it is the published data",
		"更换质检员" : "Replace quality inspector",
		"通话结束保持提醒" : "End of call hold reminder",
		"发起申诉" : "Initiate an appeal",
		"坐席结束服务" : "Agent end the service",
		"质检员数" : "Quality inspector quantity",
		"保存" : "Save",
		"会话日期" : "Session date",
		"一键补抽" : "One key supplementary pumping",
		"人工质检配置" : "Manual quality inspection configuration",
		"智能质检数量趋势" : "Intelligent quality inspection quantity trend",
		"全媒体平均分最：包含全媒体所有质检记录平均分最高的坐席、分数" : "Average score of all media: including the seats and scores with the highest average score of all quality inspection records of all media",
		"优" : "Excellent",
		"优秀录音查询" : "Excellent recording query",
		"跳转至" : "Redirect to",
		"人工质检未完成量" : "Manual quality inspection unfinished quantity",
		"质检任务记录" : "Quality inspection task record",
		"抽取成功" : "Extraction Successful",
		"该数据已经申诉过两次，无法再次申诉" : "This data has been appealed twice and cannot be appealed again",
		"发起二次申诉" : "Initiate the second appeal",
		"一票否决量" : "One vote veto",
		"每月可申诉区间：" : "Monthly appeal interval:",
		"周五" : "Friday",
		"一票否决分析：使用柱状图统计一票否决量最多的10个坐席，显示每个坐席的一票否决量" : "One vote veto analysis: use the histogram to count the 10 seats with the most one vote veto, and display the one vote veto of each seat",
		"基本信息" : "Basic Information",
		"周二" : "Tuesday",
		"新增分类" : "Add Catagory",
		"结束服务原因" : "Reason for ending service",
		"整体全媒体质检统计" : "Overall all media quality inspection statistics",
		"每天" : "Every day",
		"该质检规则名称已经存在，请重新命名" : "The quality inspection rule name already exists, please rename!",
		"有任务在执行中,请稍后再试" : "There is a task in progress. Please try again later",
		"获取录音文件失败" : "Fail to get the recording file!",
		"编辑标签" : "Edit label",
		"智能质检量" : "Intelligent quality inspection",
		"数据未完成质检，无法再次申诉" : "The data has not completed the quality inspection and cannot be appealed again",
		"5、一票否决命中次数排行：统计一票否决评分细项中出现次数最多的前十项" : "5, One-vote veto hits ranking: the statistics of a veto score in the detailed items appear the most times of the top 10",
		"新浪微博" : "Sina Weibo",
		"语音抽检量" : "Voice sampling quantity",
		"3、质检总数：统计坐席的质检总数" : "3. Total quantity of quality inspection: counts the total quantity of quality inspection of the agent",
		"4、智能质检数：任务中发起智能质检的数量" : "4. Intelligent inspections quantity: the quantity of intelligent inspections initiated in the task",
		"跳转URL" : "Redirect URL",
		"自动抽取时间" : "Automatic extraction time",
		"语音平均分：所选质检员在抽检日期范围内质检的语音平均分" : "Voice average score: the voice average score of the selected quality inspector within the sampling date range",
		"全媒体" : "All media",
		"不及格:" : "fail:",
		"单选" : "The radio",
		"质检最少：质检数最少的质检员名称、质检数" : "Minimum quality inspection quantity: the name and quantity of the quality inspector with the least quality inspection quantity",
		"条" : "Item",
		"质检状态" : "Quality inspection status",
		"分配质检员规则" : "Rules for assigning quality inspectors",
		"常用语配置" : "Phrase Config",
		"语音一票否决量：所选坐席在抽检日期范围内被一票否决的语音量" : "Voice one vote veto quantity: the voice volume of the selected seat rejected by one vote within the sampling date range",
		"字段说明" : "Field description",
		"申诉审核" : "Appeal review",
		"配置字典" : "Configuration dictionary",
		"申诉说明" : "Appeal description",
		"关联知识" : "Related knowledge",
		"上传文件内容为空" : "The uploaded file is empty！",
		"请选择条件" : "Please select condition",
		"质检员工号" : "Quality inspector ID",
		"周四" : "Thursday",
		"全媒体平均分最低" : "The average score of all media is the lowest",
		"无法释放，参数错误" : "Unable to release, wrong parameter",
		"时间查询范围不能大于31天" : "The time query range cannot be greater than 31 days",
		"机器人" : "robot",
		"会话时长" : "Session duration",
		"话机号码" : "Phone number",
		"质检员一键质检" : "Quality inspector one key quality inspection",
		"数据统计开始时间" : "Data Statistics Begin Time",
		"质检平均分" : "Quality inspection average score",
		"抽取结果" : "Extraction result",
		"查看工单地址" : "View work order address",
		"基本分数" : "Basic score",
		"1、人工一票否决指标量大于0标红整行" : "1. The number of manual one-vote veto indicators is greater than 0 for the entire line marked red",
		"点击添加" : "Click to add",
		"质检员主动抽取" : "The quality inspector takes the initiative to draw",
		"抽检总量" : "Total sampling quantity",
		"9、人工抽检率(%)：人工抽取的总数/任务质检的总数据量" : "9. Manual sampling rate (%): total manual sampling quantity/total data volume of quality inspection task",
		"语音平均分" : "Average speech score",
		"Excel文件" : "Excel file",
		"每10分钟运行（默认）" : "Run every 10 minutes (default)",
		"坐席被抽检数，用于在使用该抽取规则的质检任务里，限制坐席每月(或每周)在对应质检任务里被抽取的条数，仅仅针对质检员在质检执行时，手工进行随机抽取和按条件抽取生效，系统自动从原始记录里抽取到质检库的过程中，不使用该条件。" : "The number of agents selected for inspection is used to limit the number of agents to be selected in the corresponding quality inspection tasks per month (or week) in the quality inspection tasks using the extraction rules. This is only for the quality inspectors when the quality inspection is performed. Manual random extraction and extraction according to conditions take effect. The system automatically extracts from the original records to the quality inspection library without using this condition.",
		"编号" : "number",
		"查询评分项统计报表失败" : "Failed to query scoring item statistics report",
		"质检结果配置" : "Quality inspection result configuration",
		"服务开始时间" : "Service start time",
		"是否导出全媒体质检统计表" : "Do you want to export all media quality inspection statistics table?",
		"任务抽取规则" : "Task extraction rule",
		"用户请求" : "user request",
		"下载录音文件" : "Download recording file",
		"评定等级" : "Rating",
		"人工质检数" : "Manual quality inspections quantity",
		"质检最多：质检数最多的质检员名称、质检数" : "Maximum quality inspection quantity: the name and quantity of the quality inspector with the largest quantity of quality inspection",
		"提交人账号/提交人姓名" : "Submitter account/Submitter name",
		"用户登录超时" : "User login timeout",
		"质检组编辑" : "Edit quality inspection group",
		"评分细项统计" : "Scoring item details statistics",
		"业务熟练" : "Proficient in business",
		"转移挂机" : "Transfer hang up",
		"第一次申诉" : "First appeal",
		"待组长审核" : "To be reviewed by leader",
		"近" : "last",
		"工单地址" : "Work order address",
		"等级(一级数量)" : "Grade (first grade quantity)",
		"配置" : "Config",
		"人工抽检量" : "Manual sampling inspection quantity",
		"一票否决坐席数：被一批否决的坐席数量" : "Number of seats rejected by one vote: number of seats rejected by one group",
		"人均质检数" : "Number of people",
		"请选择要更换的数据！" : "Please select the data to be replaced!",
		"是否导出整体全媒体质检统计表" : "Export overall all media quality inspection statistics",
		"质检项维护" : "Quality inspection item maintenance",
		"分配总数：分配给该坐席的新回执码数量" : "Total allocation: the number of new receipt codes allocated to this agent",
		"已开始执行任务" : "Task started",
		"智能质检结果" : "Intelligent quality inspection results",
		"待启动" : "To be started",
		"三级评定" : "Three level assessment",
		"标准及格分" : "Standard Pass score",
		"非常优秀" : "Very excellent",
		"2、质检员数：任务的质检组中的质检员数" : "2. Quality inspectors quantity: the quantity of quality inspectors in the quality inspection group of the task",
		"通话/会话记录ID" : "Call/session ID",
		"人工质检抽取记录": "Manual quality inspection sampling record",
		"抽取结果描述": "Extraction result description",
		"条件参数": "Condition parameters",
		"录音转换配置":"Recording conversion configuration",
		"是否开启历史录音wav转换成mp3功能":"Whether to turn on the function of converting historical recording wav to MP3",
		"历史录音wav转mp3后，是否删除旧wav":"Delete old wav after converting historical recording wav to MP3",
		"历史录音wav转mp3的最大截止转换日期":"Maximum deadline for converting historical recording wav to MP3",
		"历史录音wav转mp3的最小转换日期":"Minimum conversion date of historical recording wav to MP3",
		"为空不转换":"Null, no conversion",
		"每天允许运行时间":"Allowable running time per day"
	}
}