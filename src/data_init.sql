--初始化组织类型
INSERT INTO ycbusi.cc_group_type(G<PERSON>UP_TYPE,GROUP_TYPE_NAME,CALL_FLAG,MAX_NODE_COUNT,UNIQUE_FLAG,NODE_FLAG,ENT_ID) VALUES ('01', '总监', 1, 10, 0, 0, '@ENT_ID');
INSERT INTO ycbusi.cc_group_type(GROUP_TYPE,GROUP_TYPE_NAME,CALL_FLAG,MAX_NODE_COUNT,UNIQUE_FLAG,NODE_FLAG,ENT_ID) VALUES ('0101', '团队', 1, 50, 0, 0, '@ENT_ID');
INSERT INTO ycbusi.cc_group_type(G<PERSON><PERSON>_TYPE,GROUP_TYPE_NAME,CALL_FLAG,MAX_NODE_COUNT,UNIQUE_FLAG,NODE_FLAG,ENT_ID) VALUES ('010101', '技能组', 1, 100, 0, 0, '@ENT_ID');
INSERT INTO ycbusi.cc_group_type(GRO<PERSON>_TYPE,GROUP_TYPE_NAME,CALL_FLAG,MAX_NODE_COUNT,UNIQUE_FLAG,NODE_FLAG,ENT_ID) VALUES ('99', '部门', 0, 500, 1, 0, '@ENT_ID');
INSERT INTO ycbusi.cc_group_type(GROUP_TYPE,GROUP_TYPE_NAME,CALL_FLAG,MAX_NODE_COUNT,UNIQUE_FLAG,NODE_FLAG,ENT_ID) VALUES ('020202', '全媒体技能组', 0, 100, 0, 0, '@ENT_ID');
INSERT INTO ycbusi.cc_group_type(GROUP_TYPE,GROUP_TYPE_NAME,CALL_FLAG,MAX_NODE_COUNT,UNIQUE_FLAG,NODE_FLAG,ENT_ID) VALUES ('030303', '语音技能组', 1, 100, 0, 0, '@ENT_ID');

--初始化全媒体侧边栏菜单
INSERT INTO ycbusi.c_media_op_menu(ID,MENU_ID,MENU_NAME,MENU_URL,STATUS,BAKUP,SORT_NUM,CREATE_ACC,CREATE_NAME,CREATE_TIME,ENT_ID,BUSI_ORDER_ID,BULIT_IN) VALUES ('@ENT_ID_@BUSI_ORDER_ID_khzl', 'khzl', '客户资料', '/cc-custmgr/servlet/page?action=customInfo', '01', '1', 1, 'system', 'system', '@CREATE_TIME', '@ENT_ID', '@BUSI_ORDER_ID', 'Y');
INSERT INTO ycbusi.c_media_op_menu(ID,MENU_ID,MENU_NAME,MENU_URL,STATUS,BAKUP,SORT_NUM,CREATE_ACC,CREATE_NAME,CREATE_TIME,ENT_ID,BUSI_ORDER_ID,BULIT_IN) VALUES ('@ENT_ID_@BUSI_ORDER_ID_zsk', 'zsk', '知识库', '/cc-workbench/pages/online/knowledge.jsp', '01', '2', 2, 'system', 'system', '@CREATE_TIME', '@ENT_ID', '@BUSI_ORDER_ID',  'Y');
INSERT INTO ycbusi.c_media_op_menu(ID,MENU_ID,MENU_NAME,MENU_URL,STATUS,BAKUP,SORT_NUM,CREATE_ACC,CREATE_NAME,CREATE_TIME,ENT_ID,BUSI_ORDER_ID,BULIT_IN) VALUES ('@ENT_ID_@BUSI_ORDER_ID_gd', 'gd', '标准工单', '/cc-sorder/servlet/order?action=orderInfo&source=media', '01', '3', 3, 'system', 'system', '@CREATE_TIME', '@ENT_ID', '@BUSI_ORDER_ID',  'Y');
INSERT INTO ycbusi.c_media_op_menu(ID,MENU_ID,MENU_NAME,MENU_URL,STATUS,BAKUP,SORT_NUM,CREATE_ACC,CREATE_NAME,CREATE_TIME,ENT_ID,BUSI_ORDER_ID,BULIT_IN) VALUES ('@ENT_ID_@BUSI_ORDER_ID_jqr', 'jqr', '机器人', '/cc-workbench/pages/online/robot.jsp', '01', '4', 4, 'system', 'system', '@CREATE_TIME', '@ENT_ID', '@BUSI_ORDER_ID',  'Y');
INSERT INTO ycbusi.c_media_op_menu(ID,MENU_ID,MENU_NAME,MENU_URL,STATUS,BAKUP,SORT_NUM,CREATE_ACC,CREATE_NAME,CREATE_TIME,ENT_ID,BUSI_ORDER_ID,BULIT_IN) VALUES ('@ENT_ID_@BUSI_ORDER_ID_gtls', 'gtls', '沟通历史', '/cc-workbench/pages/online/contact-history.jsp', '01', '5', 5, 'system', 'system', '@CREATE_TIME', '@ENT_ID', '@BUSI_ORDER_ID',  'Y');
INSERT INTO ycbusi.c_media_op_menu(ID,MENU_ID,MENU_NAME,MENU_URL,STATUS,BAKUP,SORT_NUM,CREATE_ACC,CREATE_NAME,CREATE_TIME,ENT_ID,BUSI_ORDER_ID,BULIT_IN) VALUES ('@ENT_ID_@BUSI_ORDER_ID_thjl', 'thjl', '通话记录', '/cc-workbench/pages/media/call-record-history.jsp', '01', '8', 6, 'system', 'system',  '@CREATE_TIME', '@ENT_ID', '@BUSI_ORDER_ID', 'Y');
INSERT INTO ycbusi.c_media_op_menu(ID,MENU_ID,MENU_NAME,MENU_URL,STATUS,BAKUP,SORT_NUM,CREATE_ACC,CREATE_NAME,CREATE_TIME,ENT_ID,BUSI_ORDER_ID,BULIT_IN) VALUES ('@ENT_ID_@BUSI_ORDER_ID_cyy', 'cyy', '常用语', '/cc-workbench/servlet/page?action=allwaysPhras', '01', '6', 7, 'system', 'system', '@CREATE_TIME', '@ENT_ID', '@BUSI_ORDER_ID',  'Y');
INSERT INTO ycbusi.c_media_op_menu(ID,MENU_ID,MENU_NAME,MENU_URL,STATUS,BAKUP,SORT_NUM,CREATE_ACC,CREATE_NAME,CREATE_TIME,ENT_ID,BUSI_ORDER_ID,BULIT_IN) VALUES ('@ENT_ID_@BUSI_ORDER_ID_hscx', 'hscx', '话术查询', '/cc-workbench/pages/task-word-list.jsp', '01', '7', 8, 'system', 'system', '@CREATE_TIME', '@ENT_ID', '@BUSI_ORDER_ID',  'Y');
INSERT INTO ycbusi.c_media_op_menu(ID,MENU_ID,MENU_NAME,MENU_URL,STATUS,BAKUP,SORT_NUM,CREATE_ACC,CREATE_NAME,CREATE_TIME,ENT_ID,BUSI_ORDER_ID,BULIT_IN) VALUES ('@ENT_ID_@BUSI_ORDER_ID_qs', 'qs', '问卷调查', '/cc-qs/servlet/qsTemp?action=QsResult', '01', '9', 9, 'system', 'system',  '@CREATE_TIME', '@ENT_ID', '@BUSI_ORDER_ID', 'Y');
INSERT INTO ycbusi.c_media_op_menu(ID,MENU_ID,MENU_NAME,MENU_URL,STATUS,BAKUP,SORT_NUM,CREATE_ACC,CREATE_NAME,CREATE_TIME,ENT_ID,BUSI_ORDER_ID,BULIT_IN) VALUES ('@ENT_ID_@BUSI_ORDER_ID_xggd', 'xggd', '相关工单', '/cc-sorder/pages/sorder/all/customer-order-list.jsp', '01', '3', 10, 'system', 'system', '@CREATE_TIME', '@ENT_ID', '@BUSI_ORDER_ID',  'Y');
INSERT INTO ycbusi.c_media_op_menu(ID,MENU_ID,MENU_NAME,MENU_URL,STATUS,BAKUP,SORT_NUM,CREATE_ACC,CREATE_NAME,CREATE_TIME,ENT_ID,BUSI_ORDER_ID,BULIT_IN) VALUES ('@ENT_ID_@BUSI_ORDER_ID_lsgd', 'lsgd', '历史工单', '/cc-eorder/pages/eorder/search/sidebar/his-order-list.jsp', '01', '3', 11, 'system', 'system', '@CREATE_TIME', '@ENT_ID', '@BUSI_ORDER_ID',  'Y');
INSERT INTO ycbusi.c_media_op_menu(ID,MENU_ID,MENU_NAME,MENU_URL,STATUS,BAKUP,SORT_NUM,CREATE_ACC,CREATE_NAME,CREATE_TIME,ENT_ID,BUSI_ORDER_ID,BULIT_IN) VALUES ('@ENT_ID_@BUSI_ORDER_ID_qrCode', 'qrCode', '二维码', '/cc-workbench/pages/online/agentQrCode.jsp', '02', '10', 12, 'system', 'system',  '@CREATE_TIME', '@ENT_ID', '@BUSI_ORDER_ID', 'Y');
INSERT INTO ycbusi.c_media_op_menu(ID,MENU_ID,MENU_NAME,MENU_URL,STATUS,BAKUP,SORT_NUM,CREATE_ACC,CREATE_NAME,CREATE_TIME,ENT_ID,BUSI_ORDER_ID,BULIT_IN) VALUES ('@ENT_ID_@BUSI_ORDER_ID_zxivr', 'zxivr', '咨询IVR', '/cc-workbench/pages/ivr-tree.jsp', '02', '10', 13, 'system', 'system',  '@CREATE_TIME', '@ENT_ID', '@BUSI_ORDER_ID', 'Y');
INSERT INTO ycbusi.c_media_op_menu(ID,MENU_ID,MENU_NAME,MENU_URL,STATUS,BAKUP,SORT_NUM,CREATE_ACC,CREATE_NAME,CREATE_TIME,ENT_ID,BUSI_ORDER_ID,BULIT_IN) VALUES ('@ENT_ID_@BUSI_ORDER_ID_llls', 'llls', '联络历史', '/cc-workbench/pages/media/contact-history.jsp', '01', '10', 14, 'system', 'system',  '@CREATE_TIME', '@ENT_ID', '@BUSI_ORDER_ID', 'Y');
INSERT INTO ycbusi.c_media_op_menu(ID,MENU_ID,MENU_NAME,MENU_URL,STATUS,BAKUP,SORT_NUM,CREATE_ACC,CREATE_NAME,CREATE_TIME,ENT_ID,BUSI_ORDER_ID,BULIT_IN) VALUES ('@ENT_ID_@BUSI_ORDER_ID_khlsgd', 'khlsgd', '客户历史工单', '/cc-sorder/pages/sorder/all/his-order-list.jsp', '01', '3', 15, 'system', 'system', '@CREATE_TIME', '@ENT_ID', '@BUSI_ORDER_ID',  'Y');
INSERT INTO ycbusi.c_media_op_menu(ID,MENU_ID,MENU_NAME,MENU_URL,STATUS,BAKUP,SORT_NUM,CREATE_ACC,CREATE_NAME,CREATE_TIME,ENT_ID,BUSI_ORDER_ID,BULIT_IN) VALUES ('@ENT_ID_@BUSI_ORDER_ID_hhxj', 'hhxj', '会话小结', '/cc-summary/pages/summary/summaryAddSidebar.jsp?type=3', '01', '3', 16, 'system', 'system', '@CREATE_TIME', '@ENT_ID', '@BUSI_ORDER_ID',  'Y');
INSERT INTO ycbusi.c_media_op_menu(ID,MENU_ID,MENU_NAME,MENU_URL,STATUS,BAKUP,SORT_NUM,CREATE_ACC,CREATE_NAME,CREATE_TIME,ENT_ID,BUSI_ORDER_ID,BULIT_IN) VALUES ('@ENT_ID_@BUSI_ORDER_ID_hhxjnew', 'hhxjnew', '会话信息', '/cc-workbench/pages/media/session-history.jsp', '01', '3', 17, 'system', 'system', '@CREATE_TIME', '@ENT_ID', '@BUSI_ORDER_ID',  'Y');
INSERT INTO ycbusi.c_media_op_menu(ID,MENU_ID,MENU_NAME,MENU_URL,STATUS,BAKUP,SORT_NUM,CREATE_ACC,CREATE_NAME,CREATE_TIME,ENT_ID,BUSI_ORDER_ID,BULIT_IN) VALUES ('@ENT_ID_@BUSI_ORDER_ID_khxxmedia', 'khxxmedia', '客户消息', '/cc-workbench/pages/media/customer-message.jsp', '01', '3', 1, 'system', 'system', '@CREATE_TIME', '@ENT_ID', '@BUSI_ORDER_ID',  'Y');
INSERT INTO ycbusi.c_media_op_menu(ID,MENU_ID,MENU_NAME,MENU_URL,STATUS,BAKUP,SORT_NUM,CREATE_ACC,CREATE_NAME,CREATE_TIME,ENT_ID,BUSI_ORDER_ID,BULIT_IN) VALUES ('@ENT_ID_@BUSI_ORDER_ID_khzlmedia', 'khzlmedia', '客户资料', '/cc-custmgr/servlet/page?action=customNotModifyInfo', '01', '3', 1, 'system', 'system', '@CREATE_TIME', '@ENT_ID', '@BUSI_ORDER_ID',  'Y');



INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_khzl_1', '@ENT_ID_@BUSI_ORDER_ID_khzl','1');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_khzl_2', '@ENT_ID_@BUSI_ORDER_ID_khzl','2');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_khzl_3', '@ENT_ID_@BUSI_ORDER_ID_khzl','3');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_zsk_1', '@ENT_ID_@BUSI_ORDER_ID_zsk','1');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_zsk_2', '@ENT_ID_@BUSI_ORDER_ID_zsk','2');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_zsk_3', '@ENT_ID_@BUSI_ORDER_ID_zsk','3');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_gd_1', '@ENT_ID_@BUSI_ORDER_ID_gd','1');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_gd_2', '@ENT_ID_@BUSI_ORDER_ID_gd','2');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_gd_3', '@ENT_ID_@BUSI_ORDER_ID_gd','3');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_jqr_1', '@ENT_ID_@BUSI_ORDER_ID_jqr','1');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_jqr_2', '@ENT_ID_@BUSI_ORDER_ID_jqr','2');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_jqr_3', '@ENT_ID_@BUSI_ORDER_ID_jqr','3');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_gtls_1', '@ENT_ID_@BUSI_ORDER_ID_gtls','1');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_gtls_2', '@ENT_ID_@BUSI_ORDER_ID_gtls','2');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_thjl_1', '@ENT_ID_@BUSI_ORDER_ID_thjl','1');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_thjl_2', '@ENT_ID_@BUSI_ORDER_ID_thjl','2');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_cyy_1', '@ENT_ID_@BUSI_ORDER_ID_cyy','1');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_hscx_2', '@ENT_ID_@BUSI_ORDER_ID_hscx','2');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_qs_1', '@ENT_ID_@BUSI_ORDER_ID_qs','1');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_xggd_1', '@ENT_ID_@BUSI_ORDER_ID_xggd','1');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_xggd_2', '@ENT_ID_@BUSI_ORDER_ID_xggd','2');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_xggd_3', '@ENT_ID_@BUSI_ORDER_ID_xggd','3');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_lsgd_1', '@ENT_ID_@BUSI_ORDER_ID_lsgd','1');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_lsgd_2', '@ENT_ID_@BUSI_ORDER_ID_lsgd','2');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_qrCode_1', '@ENT_ID_@BUSI_ORDER_ID_qrCode','1');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_zxivr_2', '@ENT_ID_@BUSI_ORDER_ID_zxivr','2');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_llls_1', '@ENT_ID_@BUSI_ORDER_ID_llls','1');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_khlsgd_1', '@ENT_ID_@BUSI_ORDER_ID_khlsgd','1');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_khlsgd_2', '@ENT_ID_@BUSI_ORDER_ID_khlsgd','2');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_khlsgd_3', '@ENT_ID_@BUSI_ORDER_ID_khlsgd','3');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_hhxj_1', '@ENT_ID_@BUSI_ORDER_ID_hhxj','1');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_hhxjnew_1', '@ENT_ID_@BUSI_ORDER_ID_hhxjnew','1');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_khxxmedia_1', '@ENT_ID_@BUSI_ORDER_ID_khxxmedia','99');
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_khzlmedia_1', '@ENT_ID_@BUSI_ORDER_ID_khzlmedia','99');

--初始化31个省
INSERT INTO cc_province(PROVINCE_CODE,PROVINCE_NAME) VALUES ('110000','北京');
INSERT INTO cc_province(PROVINCE_CODE,PROVINCE_NAME) VALUES ('120000','天津');
INSERT INTO cc_province(PROVINCE_CODE,PROVINCE_NAME) VALUES ('130000','河北');
INSERT INTO cc_province(PROVINCE_CODE,PROVINCE_NAME) VALUES ('140000','山西');
INSERT INTO cc_province(PROVINCE_CODE,PROVINCE_NAME) VALUES ('150000','内蒙古');
INSERT INTO cc_province(PROVINCE_CODE,PROVINCE_NAME) VALUES ('210000','辽宁');
INSERT INTO cc_province(PROVINCE_CODE,PROVINCE_NAME) VALUES ('220000','吉林');
INSERT INTO cc_province(PROVINCE_CODE,PROVINCE_NAME) VALUES ('230000','黑龙江');
INSERT INTO cc_province(PROVINCE_CODE,PROVINCE_NAME) VALUES ('310000','上海');
INSERT INTO cc_province(PROVINCE_CODE,PROVINCE_NAME) VALUES ('320000','江苏');
INSERT INTO cc_province(PROVINCE_CODE,PROVINCE_NAME) VALUES ('330000','浙江');
INSERT INTO cc_province(PROVINCE_CODE,PROVINCE_NAME) VALUES ('340000','安徽');
INSERT INTO cc_province(PROVINCE_CODE,PROVINCE_NAME) VALUES ('350000','福建');
INSERT INTO cc_province(PROVINCE_CODE,PROVINCE_NAME) VALUES ('360000','江西');
INSERT INTO cc_province(PROVINCE_CODE,PROVINCE_NAME) VALUES ('370000','山东');
INSERT INTO cc_province(PROVINCE_CODE,PROVINCE_NAME) VALUES ('410000','河南');
INSERT INTO cc_province(PROVINCE_CODE,PROVINCE_NAME) VALUES ('420000','湖北');
INSERT INTO cc_province(PROVINCE_CODE,PROVINCE_NAME) VALUES ('430000','湖南');
INSERT INTO cc_province(PROVINCE_CODE,PROVINCE_NAME) VALUES ('440000','广东');
INSERT INTO cc_province(PROVINCE_CODE,PROVINCE_NAME) VALUES ('450000','广西');
INSERT INTO cc_province(PROVINCE_CODE,PROVINCE_NAME) VALUES ('460000','海南');
INSERT INTO cc_province(PROVINCE_CODE,PROVINCE_NAME) VALUES ('500000','重庆');
INSERT INTO cc_province(PROVINCE_CODE,PROVINCE_NAME) VALUES ('510000','四川');
INSERT INTO cc_province(PROVINCE_CODE,PROVINCE_NAME) VALUES ('520000','贵州');
INSERT INTO cc_province(PROVINCE_CODE,PROVINCE_NAME) VALUES ('530000','云南');
INSERT INTO cc_province(PROVINCE_CODE,PROVINCE_NAME) VALUES ('540000','西藏');
INSERT INTO cc_province(PROVINCE_CODE,PROVINCE_NAME) VALUES ('610000','陕西');
INSERT INTO cc_province(PROVINCE_CODE,PROVINCE_NAME) VALUES ('620000','甘肃');
INSERT INTO cc_province(PROVINCE_CODE,PROVINCE_NAME) VALUES ('630000','青海');
INSERT INTO cc_province(PROVINCE_CODE,PROVINCE_NAME) VALUES ('640000','宁夏');
INSERT INTO cc_province(PROVINCE_CODE,PROVINCE_NAME) VALUES ('650000','新疆');

--初始化公告常用语
INSERT INTO ycbusi.C_CF_PHRASE_DIR(ID,CODE,NAME,BAKUP,PARENT_ID,SORT_NO,CREATE_ACC,CREATE_TIME,CREATE_DEPT,EP_CODE,BUSI_TYPE,MODULE,TYPE,SORT_NUM,ENABLE_STATUS,BUSI_ORDER_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_tyl','101','通用类','','-1','1','@CREATE_USER_ACC','@CREATE_TIME','500501500','@ENT_ID','01','99','01','1','01','@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_PHRASE_DIR(ID,CODE,NAME,BAKUP,PARENT_ID,SORT_NO,CREATE_ACC,CREATE_TIME,CREATE_DEPT,EP_CODE,BUSI_TYPE,MODULE,TYPE,SORT_NUM,ENABLE_STATUS,BUSI_ORDER_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_kty','101101','开头语','','@ENT_ID_@BUSI_ORDER_ID_tyl','1','@CREATE_USER_ACC','@CREATE_TIME','500501500','@ENT_ID','01','99','01','1','01','@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_PHRASE_DIR(ID,CODE,NAME,BAKUP,PARENT_ID,SORT_NO,CREATE_ACC,CREATE_TIME,CREATE_DEPT,EP_CODE,BUSI_TYPE,MODULE,TYPE,SORT_NUM,ENABLE_STATUS,BUSI_ORDER_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_jsy','101102','结束语','','@ENT_ID_@BUSI_ORDER_ID_tyl','1','@CREATE_USER_ACC','@CREATE_TIME','500501500','@ENT_ID','01','99','01','2','01','@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_PHRASE_DIR(ID,CODE,NAME,BAKUP,PARENT_ID,SORT_NO,CREATE_ACC,CREATE_TIME,CREATE_DEPT,EP_CODE,BUSI_TYPE,MODULE,TYPE,SORT_NUM,ENABLE_STATUS,BUSI_ORDER_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_xyyhzxdds','101103','需要用户在线等待时','','@ENT_ID_@BUSI_ORDER_ID_tyl','1','@CREATE_USER_ACC','@CREATE_TIME','500501500','@ENT_ID','01','99','01','3','01','@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_PHRASE_DIR(ID,CODE,NAME,BAKUP,PARENT_ID,SORT_NO,CREATE_ACC,CREATE_TIME,CREATE_DEPT,EP_CODE,BUSI_TYPE,MODULE,TYPE,SORT_NUM,ENABLE_STATUS,BUSI_ORDER_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_ydyhtcjys','101104','遇到客户提出建议时','','@ENT_ID_@BUSI_ORDER_ID_tyl','1','@CREATE_USER_ACC','@CREATE_TIME','500501500','@ENT_ID','01','99','01','4','01','@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_PHRASE_DIR(ID,CODE,NAME,BAKUP,PARENT_ID,SORT_NO,CREATE_ACC,CREATE_TIME,CREATE_DEPT,EP_CODE,BUSI_TYPE,MODULE,TYPE,SORT_NUM,ENABLE_STATUS,BUSI_ORDER_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_ydqqyhljs','101105','遇到请求用户谅解时','','@ENT_ID_@BUSI_ORDER_ID_tyl','1','@CREATE_USER_ACC','@CREATE_TIME','500501500','@ENT_ID','01','99','01','5','01','@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_PHRASE_DIR(ID,CODE,NAME,BAKUP,PARENT_ID,SORT_NO,CREATE_ACC,CREATE_TIME,CREATE_DEPT,EP_CODE,BUSI_TYPE,MODULE,TYPE,SORT_NUM,ENABLE_STATUS,BUSI_ORDER_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_ydwfdchfs','101106','遇到无法当场答复时','','@ENT_ID_@BUSI_ORDER_ID_tyl','1','@CREATE_USER_ACC','@CREATE_TIME','500501500','@ENT_ID','01','99','01','6','01','@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_PHRASE_DIR(ID,CODE,NAME,BAKUP,PARENT_ID,SORT_NO,CREATE_ACC,CREATE_TIME,CREATE_DEPT,EP_CODE,BUSI_TYPE,MODULE,TYPE,SORT_NUM,ENABLE_STATUS,BUSI_ORDER_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_ydyhlsrdsh','101107','遇到用户来骚扰的时候','','@ENT_ID_@BUSI_ORDER_ID_tyl','1','@CREATE_USER_ACC','@CREATE_TIME','500501500','@ENT_ID','01','99','01','7','01','@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_PHRASE_DIR(ID,CODE,NAME,BAKUP,PARENT_ID,SORT_NO,CREATE_ACC,CREATE_TIME,CREATE_DEPT,EP_CODE,BUSI_TYPE,MODULE,TYPE,SORT_NUM,ENABLE_STATUS,BUSI_ORDER_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_ydyhqxjds','101108','遇到用户情绪激动时','','@ENT_ID_@BUSI_ORDER_ID_tyl','1','@CREATE_USER_ACC','@CREATE_TIME','500501500','@ENT_ID','01','99','01','8','01','@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_PHRASE_DIR(ID,CODE,NAME,BAKUP,PARENT_ID,SORT_NO,CREATE_ACC,CREATE_TIME,CREATE_DEPT,EP_CODE,BUSI_TYPE,MODULE,TYPE,SORT_NUM,ENABLE_STATUS,BUSI_ORDER_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_ydyhzqs','101109','遇到用户致歉时','','@ENT_ID_@BUSI_ORDER_ID_tyl','1','@CREATE_USER_ACC','@CREATE_TIME','500501500','@ENT_ID','01','99','01','9','01','@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_PHRASE_DIR(ID,CODE,NAME,BAKUP,PARENT_ID,SORT_NO,CREATE_ACC,CREATE_TIME,CREATE_DEPT,EP_CODE,BUSI_TYPE,MODULE,TYPE,SORT_NUM,ENABLE_STATUS,BUSI_ORDER_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_ydyhzxs','101110','遇到用户致谢时','','@ENT_ID_@BUSI_ORDER_ID_tyl','1','@CREATE_USER_ACC','@CREATE_TIME','500501500','@ENT_ID','01','99','01','10','01','@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_PHRASE_DIR(ID,CODE,NAME,BAKUP,PARENT_ID,SORT_NO,CREATE_ACC,CREATE_TIME,CREATE_DEPT,EP_CODE,BUSI_TYPE,MODULE,TYPE,SORT_NUM,ENABLE_STATUS,BUSI_ORDER_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_ydyhwtccfwfws','101111','遇到客户问题超出服务范围时','','@ENT_ID_@BUSI_ORDER_ID_tyl','1','@CREATE_USER_ACC','@CREATE_TIME','500501500','@ENT_ID','01','99','01','11','01','@BUSI_ORDER_ID');

INSERT INTO ycbusi.C_CF_PHRASE(ID,DIR_ID,TITLE,content,IS_PUBLIC,CREATE_ACC,CREATE_NO,CREATE_NAME,CREATE_DEPT,CREATE_TIME,SORT_NUM,ENABLE_STATUS,USE_NUM,BUSI_ORDER_ID,ENT_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_hyy1','@ENT_ID_@BUSI_ORDER_ID_kty','欢迎语','您好,很高兴为您服务','Y','@CREATE_USER_ACC',null,'@CREATE_USER_NAME','500501','@CREATE_TIME','1','01','0','@BUSI_ORDER_ID','@ENT_ID')
INSERT INTO ycbusi.C_CF_PHRASE(ID,DIR_ID,TITLE,content,IS_PUBLIC,CREATE_ACC,CREATE_NO,CREATE_NAME,CREATE_DEPT,CREATE_TIME,SORT_NUM,ENABLE_STATUS,USE_NUM,BUSI_ORDER_ID,ENT_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_hyy2','@ENT_ID_@BUSI_ORDER_ID_kty','欢迎语2','您好，请问有什么可以帮助您的么O(∩_∩)O~','Y','@CREATE_USER_ACC',null,'@CREATE_USER_NAME','500501','@CREATE_TIME','2','01','0','@BUSI_ORDER_ID','@ENT_ID')
INSERT INTO ycbusi.C_CF_PHRASE(ID,DIR_ID,TITLE,content,IS_PUBLIC,CREATE_ACC,CREATE_NO,CREATE_NAME,CREATE_DEPT,CREATE_TIME,SORT_NUM,ENABLE_STATUS,USE_NUM,BUSI_ORDER_ID,ENT_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_jsy1','@ENT_ID_@BUSI_ORDER_ID_jsy','结束语','感谢您对我们业务的支持，祝您生活愉快，再见！','Y','@CREATE_USER_ACC',null,'@CREATE_USER_NAME','500501','@CREATE_TIME','1','01','0','@BUSI_ORDER_ID','@ENT_ID')
INSERT INTO ycbusi.C_CF_PHRASE(ID,DIR_ID,TITLE,content,IS_PUBLIC,CREATE_ACC,CREATE_NO,CREATE_NAME,CREATE_DEPT,CREATE_TIME,SORT_NUM,ENABLE_STATUS,USE_NUM,BUSI_ORDER_ID,ENT_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_jsy2','@ENT_ID_@BUSI_ORDER_ID_jsy','结束语2','请问还有其他疑问可以帮助您的吗？','Y','@CREATE_USER_ACC',null,'@CREATE_USER_NAME','500501','@CREATE_TIME','1','01','0','@BUSI_ORDER_ID','@ENT_ID')
INSERT INTO ycbusi.C_CF_PHRASE(ID,DIR_ID,TITLE,content,IS_PUBLIC,CREATE_ACC,CREATE_NO,CREATE_NAME,CREATE_DEPT,CREATE_TIME,SORT_NUM,ENABLE_STATUS,USE_NUM,BUSI_ORDER_ID,ENT_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_ddy','@ENT_ID_@BUSI_ORDER_ID_xyyhzxdds','等待语','正在为您查询,请稍候...','Y','@CREATE_USER_ACC',null,'@CREATE_USER_NAME','500501','@CREATE_TIME','1','01','0','@BUSI_ORDER_ID','@ENT_ID')
INSERT INTO ycbusi.C_CF_PHRASE(ID,DIR_ID,TITLE,content,IS_PUBLIC,CREATE_ACC,CREATE_NO,CREATE_NAME,CREATE_DEPT,CREATE_TIME,SORT_NUM,ENABLE_STATUS,USE_NUM,BUSI_ORDER_ID,ENT_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_tcjy1','@ENT_ID_@BUSI_ORDER_ID_ydyhtcjys','提出建议1','亲,您反馈的情况,我们会及时改进的,很抱歉给您带来不便','Y','@CREATE_USER_ACC',null,'@CREATE_USER_NAME','500501','@CREATE_TIME','1','01','0','@BUSI_ORDER_ID','@ENT_ID')
INSERT INTO ycbusi.C_CF_PHRASE(ID,DIR_ID,TITLE,content,IS_PUBLIC,CREATE_ACC,CREATE_NO,CREATE_NAME,CREATE_DEPT,CREATE_TIME,SORT_NUM,ENABLE_STATUS,USE_NUM,BUSI_ORDER_ID,ENT_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_tcjy2','@ENT_ID_@BUSI_ORDER_ID_ydyhtcjys','提出建议2','亲,感谢您珍贵的建议','Y','@CREATE_USER_ACC',null,'@CREATE_USER_NAME','500501','@CREATE_TIME','1','01','0','@BUSI_ORDER_ID','@ENT_ID')
INSERT INTO ycbusi.C_CF_PHRASE(ID,DIR_ID,TITLE,content,IS_PUBLIC,CREATE_ACC,CREATE_NO,CREATE_NAME,CREATE_DEPT,CREATE_TIME,SORT_NUM,ENABLE_STATUS,USE_NUM,BUSI_ORDER_ID,ENT_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_tcjy3','@ENT_ID_@BUSI_ORDER_ID_ydyhtcjys','提出建议3','非常感谢您提出的宝贵建议,我们会及时反馈给相关人员，再次感谢您对我们的关心和支持','Y','@CREATE_USER_ACC',null,'@CREATE_USER_NAME','500501','@CREATE_TIME','1','01','0','@BUSI_ORDER_ID','@ENT_ID')
INSERT INTO ycbusi.C_CF_PHRASE(ID,DIR_ID,TITLE,content,IS_PUBLIC,CREATE_ACC,CREATE_NO,CREATE_NAME,CREATE_DEPT,CREATE_TIME,SORT_NUM,ENABLE_STATUS,USE_NUM,BUSI_ORDER_ID,ENT_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_wfhf1','@ENT_ID_@BUSI_ORDER_ID_ydwfdchfs','无法回复1','您的情况已经记录下来，由于需帮您与相关部门做进一步确认，稍后我会给您回复！请问您一会是否在线或者请您留下您的联系方式，如有结果我们客服人员会在第一时间与您联系。','Y','@CREATE_USER_ACC',null,'@CREATE_USER_NAME','500501','@CREATE_TIME','1','01','0','@BUSI_ORDER_ID','@ENT_ID')
INSERT INTO ycbusi.C_CF_PHRASE(ID,DIR_ID,TITLE,content,IS_PUBLIC,CREATE_ACC,CREATE_NO,CREATE_NAME,CREATE_DEPT,CREATE_TIME,SORT_NUM,ENABLE_STATUS,USE_NUM,BUSI_ORDER_ID,ENT_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_sr1','@ENT_ID_@BUSI_ORDER_ID_ydyhlsrdsh','骚扰1','非常抱歉，您的咨询不在我们服务范围内，感谢您对我们的支持。谢谢！','Y','@CREATE_USER_ACC',null,'@CREATE_USER_NAME','500501','@CREATE_TIME','1','01','0','@BUSI_ORDER_ID','@ENT_ID')
INSERT INTO ycbusi.C_CF_PHRASE(ID,DIR_ID,TITLE,content,IS_PUBLIC,CREATE_ACC,CREATE_NO,CREATE_NAME,CREATE_DEPT,CREATE_TIME,SORT_NUM,ENABLE_STATUS,USE_NUM,BUSI_ORDER_ID,ENT_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_sbqy','@ENT_ID_@BUSI_ORDER_ID_ydyhqxjds','深表歉意','由于我们的工作给您带来不愉快，我深表歉意。请将事情原由告诉我，我们这里会为您尽力妥善处理。','Y','@CREATE_USER_ACC',null,'@CREATE_USER_NAME','500501','@CREATE_TIME','1','01','0','@BUSI_ORDER_ID','@ENT_ID')
INSERT INTO ycbusi.C_CF_PHRASE(ID,DIR_ID,TITLE,content,IS_PUBLIC,CREATE_ACC,CREATE_NO,CREATE_NAME,CREATE_DEPT,CREATE_TIME,SORT_NUM,ENABLE_STATUS,USE_NUM,BUSI_ORDER_ID,ENT_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_zxrsgd','@ENT_ID_@BUSI_ORDER_ID_ydyhqxjds','咨询人数过多','由于咨询人数较多，给您带来不便，非常抱歉。','Y','@CREATE_USER_ACC',null,'@CREATE_USER_NAME','500501','@CREATE_TIME','1','01','0','@BUSI_ORDER_ID','@ENT_ID')
INSERT INTO ycbusi.C_CF_PHRASE(ID,DIR_ID,TITLE,content,IS_PUBLIC,CREATE_ACC,CREATE_NO,CREATE_NAME,CREATE_DEPT,CREATE_TIME,SORT_NUM,ENABLE_STATUS,USE_NUM,BUSI_ORDER_ID,ENT_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_qbh','@ENT_ID_@BUSI_ORDER_ID_ydyhqxjds','请包涵','亲！理解您的心情，我们出现这种情况确实工作不到位，给您带来不便，请您多多谅解，我们会及时帮您查询的哦，请亲多多包涵！谢谢/:)-(','Y','@CREATE_USER_ACC',null,'@CREATE_USER_NAME','500501','@CREATE_TIME','1','01','0','@BUSI_ORDER_ID','@ENT_ID')
INSERT INTO ycbusi.C_CF_PHRASE(ID,DIR_ID,TITLE,content,IS_PUBLIC,CREATE_ACC,CREATE_NO,CREATE_NAME,CREATE_DEPT,CREATE_TIME,SORT_NUM,ENABLE_STATUS,USE_NUM,BUSI_ORDER_ID,ENT_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_jqfw','@ENT_ID_@BUSI_ORDER_ID_ydyhzqs','加强服务','确实这方面我们需要加强，为您们提供更快捷的服务！','Y','@CREATE_USER_ACC',null,'@CREATE_USER_NAME','500501','@CREATE_TIME','1','01','0','@BUSI_ORDER_ID','@ENT_ID')
INSERT INTO ycbusi.C_CF_PHRASE(ID,DIR_ID,TITLE,content,IS_PUBLIC,CREATE_ACC,CREATE_NO,CREATE_NAME,CREATE_DEPT,CREATE_TIME,SORT_NUM,ENABLE_STATUS,USE_NUM,BUSI_ORDER_ID,ENT_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_zxhf','@ENT_ID_@BUSI_ORDER_ID_ydyhzxs','致谢回复','亲，感谢您的支持和肯定，我们会再接再厉','Y','@CREATE_USER_ACC',null,'@CREATE_USER_NAME','500501','@CREATE_TIME','1','01','0','@BUSI_ORDER_ID','@ENT_ID')
INSERT INTO ycbusi.C_CF_PHRASE(ID,DIR_ID,TITLE,content,IS_PUBLIC,CREATE_ACC,CREATE_NO,CREATE_NAME,CREATE_DEPT,CREATE_TIME,SORT_NUM,ENABLE_STATUS,USE_NUM,BUSI_ORDER_ID,ENT_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_ccfw','@ENT_ID_@BUSI_ORDER_ID_ydyhwtccfwfws','超出范围1','亲，很抱歉，您咨询的业务不在我们的负责的范围内','Y','@CREATE_USER_ACC',null,'@CREATE_USER_NAME','500501','@CREATE_TIME','1','01','0','@BUSI_ORDER_ID','@ENT_ID')

--来电弹屏配置初始化
INSERT INTO ycbusi.c_cf_callin_op_menu (ID, MENU_ID, MENU_NAME, MENU_URL, STATUS, BAKUP, SORT_NUM, CREATE_ACC, CREATE_NAME, CREATE_DEPT, CREATE_DEPT_NAME, CREATE_TIME, ENT_ID, BUSI_ORDER_ID, BULIT_IN, TYPE) VALUES ('@ENT_ID_@BUSI_ORDER_ID_01', 'standardOrder', '标准工单', '/cc-sorder/pages/sorder/order-main.jsp', '01', '标准工单', '1', 'system', 'system', NULL, NULL, '@CREATE_TIME', '@ENT_ID', '@BUSI_ORDER_ID', 'Y', '01');
INSERT INTO ycbusi.c_cf_callin_op_menu (ID, MENU_ID, MENU_NAME, MENU_URL, STATUS, BAKUP, SORT_NUM, CREATE_ACC, CREATE_NAME, CREATE_DEPT, CREATE_DEPT_NAME, CREATE_TIME, ENT_ID, BUSI_ORDER_ID, BULIT_IN, TYPE) VALUES ('@ENT_ID_@BUSI_ORDER_ID_02', 'customerInfo', '客户资料', '/cc-custmgr/servlet/page/order?action=CustomInfo', '01', '客户资料', '1', 'system', 'system', NULL, NULL, '@CREATE_TIME', '@ENT_ID', '@BUSI_ORDER_ID', 'Y', '02');
INSERT INTO ycbusi.c_cf_callin_op_menu (ID, MENU_ID, MENU_NAME, MENU_URL, STATUS, BAKUP, SORT_NUM, CREATE_ACC, CREATE_NAME, CREATE_DEPT, CREATE_DEPT_NAME, CREATE_TIME, ENT_ID, BUSI_ORDER_ID, BULIT_IN, TYPE) VALUES ('@ENT_ID_@BUSI_ORDER_ID_03', 'customerOrder', '相关工单', '/cc-sorder/pages/sorder/all/customer-order-list.jsp', '01', '相关工单', '2', 'system', 'system', NULL, NULL, '@CREATE_TIME', '@ENT_ID', '@BUSI_ORDER_ID', 'Y', '02');
INSERT INTO ycbusi.c_cf_callin_op_menu (ID, MENU_ID, MENU_NAME, MENU_URL, STATUS, BAKUP, SORT_NUM, CREATE_ACC, CREATE_NAME, CREATE_DEPT, CREATE_DEPT_NAME, CREATE_TIME, ENT_ID, BUSI_ORDER_ID, BULIT_IN, TYPE) VALUES ('@ENT_ID_@BUSI_ORDER_ID_04', 'knowledgeInfo', '知识库', '/cc-workbench/pages/online/knowledge.jsp', '01', '知识库', '4', 'system', 'system', NULL, NULL, '@CREATE_TIME', '@ENT_ID', '@BUSI_ORDER_ID', 'Y', '02');
INSERT INTO ycbusi.c_cf_callin_op_menu (ID, MENU_ID, MENU_NAME, MENU_URL, STATUS, BAKUP, SORT_NUM, CREATE_ACC, CREATE_NAME, CREATE_DEPT, CREATE_DEPT_NAME, CREATE_TIME, ENT_ID, BUSI_ORDER_ID, BULIT_IN, TYPE) VALUES ('@ENT_ID_@BUSI_ORDER_ID_05', 'liaison-history', '客户联络历史', '/yc-agent/pages/portal/include/record-history.jsp', '01', '客户联络历史', '3', 'system', 'system', NULL, NULL, '@CREATE_TIME', '@ENT_ID', '@BUSI_ORDER_ID', 'Y', '02');
INSERT INTO ycbusi.c_cf_callin_op_menu (ID, MENU_ID, MENU_NAME, MENU_URL, STATUS, BAKUP, SORT_NUM, CREATE_ACC, CREATE_NAME, CREATE_DEPT, CREATE_DEPT_NAME, CREATE_TIME, ENT_ID, BUSI_ORDER_ID, BULIT_IN, TYPE) VALUES ('@ENT_ID_@BUSI_ORDER_ID_07', 'IVR-change-over', 'IVR转接', '/cc-workbench/pages/ivr-tree.jsp', '01', 'IVR转接', '06', 'system', 'system', NULL, NULL, '@CREATE_TIME', '@ENT_ID', '@BUSI_ORDER_ID', 'Y', '02');

--关键字
INSERT INTO ycbusi.C_CF_KEYWORD_DIR(ID,CODE,NAME,BAKUP,PARENT_ID,CREATE_ACC,CREATE_TIME,CREATE_DEPT,EP_CODE,IS_DEFALUT,BUSI_TYPE,MODULE,DIR_TYPE,SORT_NUM,ENABLE_STATUS,USER_TYPE,BUSI_ORDER_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_tyl','101','通用类','','-1','@CREATE_USER_ACC','@CREATE_TIME','500501500','@ENT_ID',null,'01','99',NULL,'1','01',NULL,'@BUSI_ORDER_ID')

INSERT INTO ycbusi.C_CF_KEYWORD(ID,DIR_ID,TITLE,CONTENT,IS_PUBLIC,CREATE_ACC,CREATE_NO,CREATE_NAME,CREATE_DEPT,SORT_NUM,ENABLE_STATUS,CREATE_TIME,USER_TYPE,BUSI_ORDER_ID,ENT_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_ts','@ENT_ID_@BUSI_ORDER_ID_tyl',NULL,'投诉','Y','@CREATE_USER_ACC',NULL,'@CREATE_USER_NAME','500501','1','01','@CREATE_TIME',NULL,'@BUSI_ORDER_ID','@ENT_ID')
INSERT INTO ycbusi.C_CF_KEYWORD(ID,DIR_ID,TITLE,CONTENT,IS_PUBLIC,CREATE_ACC,CREATE_NO,CREATE_NAME,CREATE_DEPT,SORT_NUM,ENABLE_STATUS,CREATE_TIME,USER_TYPE,BUSI_ORDER_ID,ENT_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_jb','@ENT_ID_@BUSI_ORDER_ID_tyl',NULL,'举报','Y','@CREATE_USER_ACC',NULL,'@CREATE_USER_NAME','500501','1','01','@CREATE_TIME',NULL,'@BUSI_ORDER_ID','@ENT_ID')

--敏感词
INSERT INTO ycbusi.C_CF_SENSITIVE_DIR(ID,CODE,NAME,BAKUP,PARENT_ID,SORT_NUM,CREATE_ACC,CREATE_TIME,CREATE_DEPT,EP_CODE,ENABLE_STATUS,BUSI_TYPE,MODULE,BUSI_ORDER_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_zxtyl','101','坐席通用类','','-1','1','@CREATE_USER_ACC','@CREATE_TIME','500501500','@ENT_ID','01','01','99','@BUSI_ORDER_ID')
INSERT INTO ycbusi.C_CF_SENSITIVE_DIR(ID,CODE,NAME,BAKUP,PARENT_ID,SORT_NUM,CREATE_ACC,CREATE_TIME,CREATE_DEPT,EP_CODE,ENABLE_STATUS,BUSI_TYPE,MODULE,BUSI_ORDER_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_dxtyl','101','短信通用类','','-1','1','@CREATE_USER_ACC','@CREATE_TIME','500501500','@ENT_ID','01','02','99','@BUSI_ORDER_ID')
INSERT INTO ycbusi.C_CF_SENSITIVE_DIR(ID,CODE,NAME,BAKUP,PARENT_ID,SORT_NUM,CREATE_ACC,CREATE_TIME,CREATE_DEPT,EP_CODE,ENABLE_STATUS,BUSI_TYPE,MODULE,BUSI_ORDER_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_khtyl','101','客户通用类','','-1','1','@CREATE_USER_ACC','@CREATE_TIME','500501500','@ENT_ID','01','03','99','@BUSI_ORDER_ID')

INSERT INTO ycbusi.C_CF_SENSITIVE(ID,CONTENT,CREATE_ACC,CREATE_NO,CREATE_NAME,CREATE_DEPT,SORT_NUM,ENABLE_STATUS,CREATE_TIME,DIR_ID,BUSI_ORDER_ID,ENT_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_ch','蠢货','@CREATE_USER_ACC',null,'@CREATE_USER_NAME','500501','1','01','@CREATE_TIME','@ENT_ID_@BUSI_ORDER_ID_zxtyl','@BUSI_ORDER_ID','@ENT_ID')
INSERT INTO ycbusi.C_CF_SENSITIVE(ID,CONTENT,CREATE_ACC,CREATE_NO,CREATE_NAME,CREATE_DEPT,SORT_NUM,ENABLE_STATUS,CREATE_TIME,DIR_ID,BUSI_ORDER_ID,ENT_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_qs','去死','@CREATE_USER_ACC',null,'@CREATE_USER_NAME','500501','2','01','@CREATE_TIME','@ENT_ID_@BUSI_ORDER_ID_zxtyl','@BUSI_ORDER_ID','@ENT_ID')
INSERT INTO ycbusi.C_CF_SENSITIVE(ID,CONTENT,CREATE_ACC,CREATE_NO,CREATE_NAME,CREATE_DEPT,SORT_NUM,ENABLE_STATUS,CREATE_TIME,DIR_ID,BUSI_ORDER_ID,ENT_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_zs','找死','@CREATE_USER_ACC',null,'@CREATE_USER_NAME','500501','3','01','@CREATE_TIME','@ENT_ID_@BUSI_ORDER_ID_zxtyl','@BUSI_ORDER_ID','@ENT_ID')
INSERT INTO ycbusi.C_CF_SENSITIVE(ID,CONTENT,CREATE_ACC,CREATE_NO,CREATE_NAME,CREATE_DEPT,SORT_NUM,ENABLE_STATUS,CREATE_TIME,DIR_ID,BUSI_ORDER_ID,ENT_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_ds','打死','@CREATE_USER_ACC',null,'@CREATE_USER_NAME','500501','4','01','@CREATE_TIME','@ENT_ID_@BUSI_ORDER_ID_zxtyl','@BUSI_ORDER_ID','@ENT_ID')
INSERT INTO ycbusi.C_CF_SENSITIVE(ID,CONTENT,CREATE_ACC,CREATE_NO,CREATE_NAME,CREATE_DEPT,SORT_NUM,ENABLE_STATUS,CREATE_TIME,DIR_ID,BUSI_ORDER_ID,ENT_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_zc','真蠢','@CREATE_USER_ACC',null,'@CREATE_USER_NAME','500501','5','01','@CREATE_TIME','@ENT_ID_@BUSI_ORDER_ID_zxtyl','@BUSI_ORDER_ID','@ENT_ID')
INSERT INTO ycbusi.C_CF_SENSITIVE(ID,CONTENT,CREATE_ACC,CREATE_NO,CREATE_NAME,CREATE_DEPT,SORT_NUM,ENABLE_STATUS,CREATE_TIME,DIR_ID,BUSI_ORDER_ID,ENT_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_sjb','神经病','@CREATE_USER_ACC',null,'@CREATE_USER_NAME','500501','6','01','@CREATE_TIME','@ENT_ID_@BUSI_ORDER_ID_zxtyl','@BUSI_ORDER_ID','@ENT_ID')
INSERT INTO ycbusi.C_CF_SENSITIVE(ID,CONTENT,CREATE_ACC,CREATE_NO,CREATE_NAME,CREATE_DEPT,SORT_NUM,ENABLE_STATUS,CREATE_TIME,DIR_ID,BUSI_ORDER_ID,ENT_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_yb','有病','@CREATE_USER_ACC',null,'@CREATE_USER_NAME','500501','7','01','@CREATE_TIME','@ENT_ID_@BUSI_ORDER_ID_zxtyl','@BUSI_ORDER_ID','@ENT_ID')
INSERT INTO ycbusi.C_CF_SENSITIVE(ID,CONTENT,CREATE_ACC,CREATE_NO,CREATE_NAME,CREATE_DEPT,SORT_NUM,ENABLE_STATUS,CREATE_TIME,DIR_ID,BUSI_ORDER_ID,ENT_ID) VALUES('@ENT_ID_@BUSI_ORDER_ID_sg','傻瓜','@CREATE_USER_ACC',null,'@CREATE_USER_NAME','500501','8','01','@CREATE_TIME','@ENT_ID_@BUSI_ORDER_ID_zxtyl','@BUSI_ORDER_ID','@ENT_ID')

--首页指标分组
INSERT INTO ycbusi.C_CF_INDICATORS_GROUP(ID, CODE, NAME, TYPE, SHOW_TYPE, BAKUP, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, DATA_SCOPE,BUSI_ORDER_ID) values ('@ENT_ID_@BUSI_ORDER_ID_yygzqk1', '001', '语音坐席工作情况', '01', '01', '语音坐席工作情况', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 1, '01', '01', '@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS_GROUP(ID, CODE, NAME, TYPE, SHOW_TYPE, BAKUP, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, DATA_SCOPE,BUSI_ORDER_ID) values ('@ENT_ID_@BUSI_ORDER_ID_yygzqk2', '002', '语音班组工作情况', '02', '01', '语音班组工作情况', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 2, '01', '01', '@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS_GROUP(ID, CODE, NAME, TYPE, SHOW_TYPE, BAKUP, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, DATA_SCOPE,BUSI_ORDER_ID) values ('@ENT_ID_@BUSI_ORDER_ID_yyfwzl1', '003', '语音坐席服务质量', '01', '01', '语音坐席服务质量', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 3, '01', '01', '@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS_GROUP(ID, CODE, NAME, TYPE, SHOW_TYPE, BAKUP, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, DATA_SCOPE,BUSI_ORDER_ID) values ('@ENT_ID_@BUSI_ORDER_ID_yyfwzl2', '004', '语音班组服务质量', '02', '01', '语音班组服务质量', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 4, '01', '01', '@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS_GROUP(ID, CODE, NAME, TYPE, SHOW_TYPE, BAKUP, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, DATA_SCOPE,BUSI_ORDER_ID) values ('@ENT_ID_@BUSI_ORDER_ID_zxgzqk1', '005', '在线坐席工作情况', '01', '01', '在线坐席工作情况', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 5, '01', '01', '@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS_GROUP(ID, CODE, NAME, TYPE, SHOW_TYPE, BAKUP, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, DATA_SCOPE,BUSI_ORDER_ID) values ('@ENT_ID_@BUSI_ORDER_ID_zxgzqk2', '006', '在线班组工作情况', '02', '01', '在线班组工作情况', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 6, '01', '01', '@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS_GROUP(ID, CODE, NAME, TYPE, SHOW_TYPE, BAKUP, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, DATA_SCOPE,BUSI_ORDER_ID) values ('@ENT_ID_@BUSI_ORDER_ID_zxfwzl1', '007', '在线坐席服务质量', '01', '01', '在线坐席服务质量', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 7, '01', '01', '@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS_GROUP(ID, CODE, NAME, TYPE, SHOW_TYPE, BAKUP, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, DATA_SCOPE,BUSI_ORDER_ID) values ('@ENT_ID_@BUSI_ORDER_ID_zxfwzl2', '008', '在线班组服务质量', '02', '01', '在线坐席服务质量', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 8, '01', '01', '@BUSI_ORDER_ID');

--首页指标
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_yyzxsc1', '@ENT_ID_@BUSI_ORDER_ID_yygzqk1', 'CALL_LOGIN_LEN', '语音在线时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 1, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_yyzxsc2', '@ENT_ID_@BUSI_ORDER_ID_yygzqk2', 'CALL_LOGIN_LEN', '语音在线时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 2, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_hrs1', '@ENT_ID_@BUSI_ORDER_ID_yygzqk1', 'CALL_IN_NUM', '呼入数', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 3, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_hrs2', '@ENT_ID_@BUSI_ORDER_ID_yygzqk2', 'CALL_IN_NUM', '呼入数', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 4, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_hcs1', '@ENT_ID_@BUSI_ORDER_ID_yygzqk1', 'CALL_OUT_NUM', '呼出数', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 5, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_hcs2', '@ENT_ID_@BUSI_ORDER_ID_yygzqk2', 'CALL_OUT_NUM', '呼出数', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 6, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_hrzthsc1', '@ENT_ID_@BUSI_ORDER_ID_yygzqk1', 'CALL_TOTAL_IN_LENS', '呼入总通话时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 7, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_hrzthsc2', '@ENT_ID_@BUSI_ORDER_ID_yygzqk2', 'CALL_TOTAL_IN_LENS', '呼入总通话时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 8, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_pjhrthsc1', '@ENT_ID_@BUSI_ORDER_ID_yygzqk1', 'CALL_AVG_IN_LENS', '平均呼入通话时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 9, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_pjhrthsc2', '@ENT_ID_@BUSI_ORDER_ID_yygzqk2', 'CALL_AVG_IN_LENS', '平均呼入通话时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 10, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_hczthsc1', '@ENT_ID_@BUSI_ORDER_ID_yygzqk1', 'CALL_TOTAL_OUT_LENS', '呼出总通话时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 11, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_hczthsc2', '@ENT_ID_@BUSI_ORDER_ID_yygzqk2', 'CALL_TOTAL_OUT_LENS', '呼出总通话时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 12, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_pjhcthsc1', '@ENT_ID_@BUSI_ORDER_ID_yygzqk1', 'CALL_AVG_OUT_LENS', '平均呼出通话时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 13, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_pjhcthsc2', '@ENT_ID_@BUSI_ORDER_ID_yygzqk2', 'CALL_AVG_OUT_LENS', '平均呼出通话时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 14, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_byzjcql1', '@ENT_ID_@BUSI_ORDER_ID_yyfwzl1', 'CALL_QC_NUM', '本月质检抽取量', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 15, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_byzjcql2', '@ENT_ID_@BUSI_ORDER_ID_yyfwzl2', 'CALL_QC_NUM', '本月质检抽取量', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 16, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_byzjhgl1', '@ENT_ID_@BUSI_ORDER_ID_yyfwzl1', 'CALL_QC_PASS_NUM', '本月质检合格量', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 17, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_byzjhgl2', '@ENT_ID_@BUSI_ORDER_ID_yyfwzl2', 'CALL_QC_PASS_NUM', '本月质检合格量', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 18, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_dtyylyclzl1', '@ENT_ID_@BUSI_ORDER_ID_yygzqk1', 'CALL_VMSG_TOTAL', '当天语音留言处理总量', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 19, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_dtyylyclzl2', '@ENT_ID_@BUSI_ORDER_ID_yygzqk2', 'CALL_VMSG_TOTAL', '当天语音留言处理总量', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 20, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_dtyylhclzl1', '@ENT_ID_@BUSI_ORDER_ID_yygzqk1', 'CALL_LOUHUA_TOTAL', '当天语音漏话处理总量', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 21, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_dtyylhclzl2', '@ENT_ID_@BUSI_ORDER_ID_yygzqk2', 'CALL_LOUHUA_TOTAL', '当天语音漏话处理总量', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 22, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_dtwjltclzl1', '@ENT_ID_@BUSI_ORDER_ID_yygzqk1', 'CALL_CALLLOSS_TOTAL', '当天未接来电处理总量', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 23, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_dtwjltclzl2', '@ENT_ID_@BUSI_ORDER_ID_yygzqk2', 'CALL_CALLLOSS_TOTAL', '当天未接来电处理总量', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 24, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_sccs1', '@ENT_ID_@BUSI_ORDER_ID_yygzqk1', 'CALL_NOTREADY_TIMES', '示忙次数', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 25, '01', '次', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_sccs2', '@ENT_ID_@BUSI_ORDER_ID_yygzqk2', 'CALL_NOTREADY_AVG_TIMES', '人均示忙次数', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 26, '01', '次', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_scsc1', '@ENT_ID_@BUSI_ORDER_ID_yygzqk1', 'CALL_NOTREADY_TIME', '示忙时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 27, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_scsc2', '@ENT_ID_@BUSI_ORDER_ID_yygzqk2', 'CALL_NOTREADY_AVG_LEN', '人均示忙时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 28, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_sxscx1', '@ENT_ID_@BUSI_ORDER_ID_yygzqk1', 'CALL_READY_TIME', '示闲时长(新)', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 29, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_smyshrs1', '@ENT_ID_@BUSI_ORDER_ID_yygzqk1', 'CALL_IN_10_NUM', '10s以上呼入数', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 31, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_smyshrs2', '@ENT_ID_@BUSI_ORDER_ID_yygzqk2', 'CALL_IN_10_NUM2', '10s以上呼入数', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 32, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_smyshcs1', '@ENT_ID_@BUSI_ORDER_ID_yygzqk1', 'CALL_OUT_10_NUM', '10s以上呼出数', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 33, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_smyshcs2', '@ENT_ID_@BUSI_ORDER_ID_yygzqk2', 'CALL_OUT_10_NUM2', '10s以上呼出数', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 34, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_hhzlsc1', '@ENT_ID_@BUSI_ORDER_ID_yygzqk1', 'CALL_ACW_TIME', '话后整理时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 35, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_hhzlsc2', '@ENT_ID_@BUSI_ORDER_ID_yygzqk2', 'CALL_ACW_TIME', '话后整理时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 36, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_pjhhzlsc1', '@ENT_ID_@BUSI_ORDER_ID_yygzqk1', 'CALL_AVG_ACW_TIME', '平均话后整理时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 37, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_pjhhzlsc2', '@ENT_ID_@BUSI_ORDER_ID_yygzqk2', 'CALL_AVG_ACW_TIME', '平均话后整理时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 38, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_cpl1', '@ENT_ID_@BUSI_ORDER_ID_yyfwzl1', 'CALL_EVAL_NUM', '参评量', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 39, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_cpl2', '@ENT_ID_@BUSI_ORDER_ID_yyfwzl2', 'CALL_EVAL_NUM', '参评量', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 40, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_myl1', '@ENT_ID_@BUSI_ORDER_ID_yyfwzl1', 'CALL_SATISYF_NUM', '满意量', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 41, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_myl2', '@ENT_ID_@BUSI_ORDER_ID_yyfwzl2', 'CALL_SATISYF_NUM', '满意量', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 42, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_yymyl1', '@ENT_ID_@BUSI_ORDER_ID_yyfwzl1', 'CALL_SATISYF_RATE', '语音满意率(新)', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 43, '01', '%', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_yymyl2', '@ENT_ID_@BUSI_ORDER_ID_yyfwzl2', 'CALL_SATISYF_RATE', '语音满意率(新)', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 44, '01', '%', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_qmtzxsc1', '@ENT_ID_@BUSI_ORDER_ID_zxgzqk1', 'M_LOGIN_LEN', '全媒体在线时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 45, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_qmtzxsc2', '@ENT_ID_@BUSI_ORDER_ID_zxgzqk2', 'M_AGENT_NUM', '全媒体在线坐席数', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 46, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_qmtzxsc3', '@ENT_ID_@BUSI_ORDER_ID_zxgzqk2', 'M_LOGIN_AVG_LEN', '全媒体人均在线时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 46, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_qmtfws1', '@ENT_ID_@BUSI_ORDER_ID_zxgzqk1', 'M_IN_NUM', '全媒体服务数', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 47, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_qmtfws2', '@ENT_ID_@BUSI_ORDER_ID_zxgzqk2', 'M_IN_NUM', '全媒体服务数', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 48, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_qmtfws3', '@ENT_ID_@BUSI_ORDER_ID_zxgzqk2', 'M_IN_AVG_NUM', '全媒体人均服务数', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 48, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_fwzsc1', '@ENT_ID_@BUSI_ORDER_ID_zxgzqk1', 'M_TOTLA_IN_LENS', '服务总时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 49, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_fwzsc2', '@ENT_ID_@BUSI_ORDER_ID_zxgzqk2', 'M_TOTLA_IN_LENS', '服务总时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 50, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_pjfwsc1', '@ENT_ID_@BUSI_ORDER_ID_zxgzqk1', 'M_AVG_IN_LENS', '平均服务时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 51, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_pjfwsc2', '@ENT_ID_@BUSI_ORDER_ID_zxgzqk2', 'M_AVG_IN_LENS', '平均服务时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 52, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_scpjxysc1', '@ENT_ID_@BUSI_ORDER_ID_zxgzqk1', 'M_FIRST_REPLY_AVG_LENS', '首次平均响应时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 53, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_scpjxysc2', '@ENT_ID_@BUSI_ORDER_ID_zxgzqk2', 'M_FIRST_REPLY_AVG_LENS', '首次平均响应时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 54, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_pjxysc1', '@ENT_ID_@BUSI_ORDER_ID_zxgzqk1', 'M_REPLY_AVG_LENS', '平均响应时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 55, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_pjxysc2', '@ENT_ID_@BUSI_ORDER_ID_zxgzqk2', 'M_REPLY_AVG_LENS', '平均响应时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 56, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_dtlxxxzl1', '@ENT_ID_@BUSI_ORDER_ID_zxgzqk1', 'M_HANDLE_WORD_NUM', '当天离线消息总量', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 57, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_dtlxxxzl2', '@ENT_ID_@BUSI_ORDER_ID_zxgzqk2', 'M_HANDLE_WORD_NUM', '当天离线消息总量', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 58, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_dtwcllxxxzl1', '@ENT_ID_@BUSI_ORDER_ID_zxgzqk1', 'M_WAIT_HANDLE_WORD_NUM', '当天未处理离线消息总量', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 59, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_dtwcllxxxzl2', '@ENT_ID_@BUSI_ORDER_ID_zxgzqk2', 'M_WAIT_HANDLE_WORD_NUM', '当天未处理离线消息总量', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 60, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_qmtsmcs1', '@ENT_ID_@BUSI_ORDER_ID_zxgzqk1', 'M_NOTREADY_TIMES', '全媒体示忙次数', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 61, '01', '次', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_qmtsmcs2', '@ENT_ID_@BUSI_ORDER_ID_zxgzqk2', 'M_NOTREADY_AVG_TIMES', '全媒体人均示忙次数', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 62, '01', '次', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_qmtsmsc1', '@ENT_ID_@BUSI_ORDER_ID_zxgzqk1', 'M_NOTREADY_TIME', '全媒体示忙时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 63, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_qmtsmsc2', '@ENT_ID_@BUSI_ORDER_ID_zxgzqk2', 'M_NOTREADY_AVG_TIME', '全媒体人均示忙时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 64, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_qmtbyzjcql1', '@ENT_ID_@BUSI_ORDER_ID_zxfwzl1', 'M_QC_NUM', '全媒体本月质检抽取量', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 65, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_qmtbyzjcql2', '@ENT_ID_@BUSI_ORDER_ID_zxfwzl2', 'M_QC_NUM', '全媒体本月质检抽取量', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 66, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_qmtbyzjhgl1', '@ENT_ID_@BUSI_ORDER_ID_zxfwzl1', 'M_QC_PASS_NUM', '全媒体本月质检合格量', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 67, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_qmtbyzjhgl2', '@ENT_ID_@BUSI_ORDER_ID_zxfwzl2', 'M_QC_PASS_NUM', '全媒体本月质检合格量', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 68, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_qmtcpl1', '@ENT_ID_@BUSI_ORDER_ID_zxfwzl1', 'M_EVAL_NUM', '全媒体参评量', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 69, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_qmtcpl2', '@ENT_ID_@BUSI_ORDER_ID_zxfwzl2', 'M_EVAL_NUM', '全媒体参评量', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 70, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_qmtmyl1', '@ENT_ID_@BUSI_ORDER_ID_zxfwzl1', 'M_SATISYF_NUM', '全媒体满意量', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 71, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_qmtmyl2', '@ENT_ID_@BUSI_ORDER_ID_zxfwzl2', 'M_SATISYF_NUM', '全媒体满意量', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 72, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_qmtmylv1', '@ENT_ID_@BUSI_ORDER_ID_zxfwzl1', 'M_SATISYF_RATE', '全媒体满意率', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 73, '01', '%', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_qmtmylv2', '@ENT_ID_@BUSI_ORDER_ID_zxfwzl2', 'M_SATISYF_RATE', '全媒体满意率', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 74, '01', '%', null,'@BUSI_ORDER_ID');

INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_mHangTimes1', '@ENT_ID_@BUSI_ORDER_ID_zxgzqk1', 'M_HANG_TIMES', '当天挂起次数', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 75, '01', '次', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_mHangTimes2', '@ENT_ID_@BUSI_ORDER_ID_zxgzqk2', 'M_HANG_TIMES', '当天挂起次数', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 76, '01', '次', null,'@BUSI_ORDER_ID');

INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_mHangSecond1', '@ENT_ID_@BUSI_ORDER_ID_zxgzqk1', 'M_HANG_SECOND', '当天挂起时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 77, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_mHangSecond2', '@ENT_ID_@BUSI_ORDER_ID_zxgzqk2', 'M_HANG_SECOND', '当天挂起时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 78, '01', '秒', null,'@BUSI_ORDER_ID');

INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_gzsc1', '@ENT_ID_@BUSI_ORDER_ID_yygzqk1', 'CALL_IN_OUT_TOTAL_ALERT_LENS', '工作时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 79, '01', '秒', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_gzsc2', '@ENT_ID_@BUSI_ORDER_ID_yygzqk2', 'CALL_IN_OUT_TOTAL_ALERT_LENS', '工作时长', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 80, '01', '秒', null,'@BUSI_ORDER_ID');

INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_byhhmyl1', '@ENT_ID_@BUSI_ORDER_ID_yyfwzl1', 'CALL_SATISYF_MONTH_NUM', '本月话后满意量', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 81, '01', '个', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_byhhmyl2', '@ENT_ID_@BUSI_ORDER_ID_yyfwzl2', 'CALL_SATISYF_MONTH_NUM', '本月话后满意量', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 82, '01', '个', null,'@BUSI_ORDER_ID');

INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_byzjpjf1', '@ENT_ID_@BUSI_ORDER_ID_yyfwzl1', 'CALL_QC_AVG_SCORE', '本月质检平均分', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 83, '01', '分', null,'@BUSI_ORDER_ID');
INSERT INTO ycbusi.C_CF_INDICATORS(ID, GROUP_ID, CODE, NAME, SOURCE, BAKUP, SQL1, SHOW_TYPE, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, SORT_NUM, ENABLE_STATUS, UNIT, URLNAME,BUSI_ORDER_ID)values ('@ENT_ID_@BUSI_ORDER_ID_byzjpjf2', '@ENT_ID_@BUSI_ORDER_ID_yyfwzl2', 'CALL_QC_AVG_SCORE', '本月质检平均分', '01', null, null, '01', '@CREATE_USER_ACC', '@CREATE_TIME', null, '@ENT_ID', 84, '01', '分', null,'@BUSI_ORDER_ID'); 


--初始化国际化语言管理
INSERT INTO CC_SYS_LANG(ID,LANG_CODE,LANG_NAME,LANG_OUT_CODE,LANG_DESC,ENABLE_STATUS,CREATE_ACC,CREATE_TIME,UPDATE_ACC,UPDATE_TIME,SORT_NUM)values('LANG_CN','CN','中文','zh-CN',null,'01','@CREATE_USER_ACC', '@CREATE_TIME',null,null,'1');
INSERT INTO CC_SYS_LANG(ID,LANG_CODE,LANG_NAME,LANG_OUT_CODE,LANG_DESC,ENABLE_STATUS,CREATE_ACC,CREATE_TIME,UPDATE_ACC,UPDATE_TIME,SORT_NUM)values('LANG_EN','EN','English','en',null,'01','@CREATE_USER_ACC', '@CREATE_TIME',null,null,'2');
INSERT INTO CC_SYS_LANG(ID,LANG_CODE,LANG_NAME,LANG_OUT_CODE,LANG_DESC,ENABLE_STATUS,CREATE_ACC,CREATE_TIME,UPDATE_ACC,UPDATE_TIME,SORT_NUM)values('LANG_ja','ja','日本語','ja',null,'02','@CREATE_USER_ACC', '@CREATE_TIME',null,null,'3');