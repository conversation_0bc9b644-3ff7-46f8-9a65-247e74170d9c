
package com.yunqu.cc.km.servlet;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.Part;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.CacheTime.CacheName;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.cache.impl.EhcacheKit;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.DBTypes;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.excel.ExcelUtils;
import org.easitline.common.utils.excel.handler.ExcelHeaderStyle;
import org.easitline.common.utils.excel.utils.Utils;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.annontation.InfAuthCheck;
import com.yq.busi.common.base.CEConstants;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.base.ServiceID;
import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.model.Yqlogger;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.FileUtil;
import com.yq.busi.common.util.LogUtil;
import com.yq.busi.common.util.ShortUrlGenerator;
import com.yq.busi.common.util.UserUtil;
import com.yq.busi.common.util.mq.MQTopicUtil;
import com.yunqu.cc.km.base.AppBaseServlet;
import com.yunqu.cc.km.base.CommonLogger;
import com.yunqu.cc.km.base.Constants;
import com.yunqu.cc.km.utils.ContentDealUtils;
import com.yunqu.cc.km.utils.InfoUtil;
import com.yunqu.cc.km.utils.ListUtils;
import com.yunqu.cc.km.utils.PortraitUtil;
import com.yunqu.cc.km.utils.RobotUtils;
import com.yunqu.cc.km.utils.UnicodeGBK2Alpha;

@WebServlet("/servlet/kmInfo")
@MultipartConfig
public class KmInfoServlet extends AppBaseServlet {
	
	private static final long serialVersionUID = 5054944876079785280L;
	private Logger logger = CommonLogger.logger;

	private Logger impLogger = CommonLogger.getLogger("imp");

	private EasyCache cache = CacheManager.getMemcache();

	/**
	 * 知识导入新增目录
	 */
	private Set<String> ADD_DIR_ID = new HashSet<String>(); 
	
	public void actionForSmsSend() {
		String infoId = this.getRequest().getParameter("infoId");
		String channelKey = this.getRequest().getParameter("channelKey");
		if(StringUtils.isBlank(channelKey)) {
			channelKey = "";
		}
		String infoTitle = this.getRequest().getParameter("infoTitle");
		String url =  "/cc-km/wechat/km-info-detail-h5.html?infoId=" + infoId + "&kmChannelKey=" + channelKey + "&schema=" + this.getDbName();
		
		try {
			url = ShortUrlGenerator.createShortUrl(this.getEntId(), this.getBusiOrderId(), UserUtil.getRequestUserAcc(getRequest()), null, url );
			logger.info(CommonUtil.getClassNameAndMethod(this) + " 生成短链接：" + url);
			String content = this.getQuery().queryForString("SELECT CONTENT FROM " + this.getTableName("C_SMS_TEMPLATE") + " WHERE EP_CODE = ? AND BUSI_ORDER_ID = ? AND MSG_CODE = ?", this.getEntId(), this.getBusiOrderId(), "SYSTEM_KM_DETAIL");
			if(StringUtils.isBlank(content)) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + " 获取短信内容失败");
				return;
			}
			if(StringUtils.isBlank(url)) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + " 生成短链接失败");
				return;
			}
			content = content.replace("INFO_TITLE", infoTitle).replace("INFO_URL", url);
			content = URLEncoder.encode(content, "utf-8");
			this.getResponse().sendRedirect("/cc-sms/pages/send/smsSendPopup.jsp?isNotDiv=1&content=" + content);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
		}
	}
	
	/**
	 * 知识点新增|编辑
	 * @return
	 */
	@InfAuthCheck(resId = "cc-km-manage-edit",msg = "没有权限")
	public EasyResult actionForKmFormEdit() {
		JSONObject infoJson = this.getJSONObject("KM_INFO");
		JSONObject json  = this.getJSONObject();
		EasyQuery query = this.getQuery();
		try {
			query.begin();
			
			// 获取知识点基本数据
			String infoId = infoJson.getString("INFO_ID");
			String infoTopic = infoJson.getString("INFO_TOPIC");
			String searchCode = infoJson.getString("SEARCH_CODE");
			String classifyCode = this.getJSONObject().getString("classifyCode");
			String validityDate = infoJson.getString("VALIDITY_DATE");
			String isNew = infoJson.getString("IS_NEW");
			String newExpireDate = infoJson.getString("NEW_EXPIRE_DATE");
			String templateId = infoJson.getString("TEMPLATE_ID");
			String linkFile1 = infoJson.getString("LINK_FILE1");
			String dirId = infoJson.getString("DIR_ID");
			String secretLevel = infoJson.getString("SECRET_LEVEL");
			String kmProvider = infoJson.getString("KM_PROVIDER");
			String updateMemoNew = infoJson.getString("UPDATE_MEMO");
			
			UserModel user = UserUtil.getUser(getRequest());
			String date = DateUtil.getCurrentDateStr();
			String entId = this.getEntId();
			String busiOrderId = this.getBusiOrderId();
			
			String infoInitStatus = query.queryForString("SELECT INFO_STATUS FROM " + this.getTableName("KM_INFO") + " WHERE INFO_ID = ?", infoId);
			
			EasySQL sql = new EasySQL("SELECT INFO_TOPIC,SEARCH_CODE FROM " + this.getTableName("KM_INFO"));
			sql.append("WHERE 1 = 1");
			sql.append(entId, "AND ENT_ID = ?");
			sql.append(busiOrderId, "AND BUSI_ORDER_ID = ?");
			sql.append("03", "AND KM_INFO_TYPE = ?");
			sql.append(infoId, "AND INFO_ID <> ?");
			List<JSONObject> entityList = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			StringBuilder entityStr = new StringBuilder();
			for (JSONObject entity : entityList) {
				entityStr.append(";" + entity.getString("INFO_TOPIC") + ";" + entity.getString("SEARCH_CODE"));
			}
			
			Integer itemCount = new Integer(query.queryForInt("SELECT COUNT(1) FROM " + this.getTableName("KM_INFO_ITEM") + " WHERE INFO_ID = ?", new Object[] { infoId }));
			
			// 校验标题是否重复
			if(entityStr.indexOf(";" + infoTopic + ";") > -1) {
				query.roolback();
				return EasyResult.error(500, getI18nValue("知识标题重复"));
			}
			
			entityStr.append(";" + infoTopic + ";");
			
			// 校验别名是否重复
			if(StringUtils.isNotBlank(searchCode)) {
				for(String keyCode : searchCode.split(",")) {
					if(entityStr.indexOf(";" + keyCode + ";") > -1 || infoTopic.equals(keyCode)) {
						query.roolback();
						return EasyResult.error(500, getI18nValue("知识别名[") + keyCode + getI18nValue("]重复"));
					}
				}
			}
			
			// 数据处理
			
			String [] dateArray = validityDate.split("~"); // 有效期处理
			String validDate = dateArray[0].trim(); // 有效开始时间
			String expireDate = dateArray[1].trim(); // 有效结束时间
			
			String infoStatus = "";
			
			String newDate = DateUtil.getCurrentDateStr("yyyy-MM-dd");
			if(DateUtil.getDate("yyyy-MM-dd", expireDate).before(DateUtil.getDate("yyyy-MM-dd", newDate)) ) {
				infoStatus = "4";
			} else if(DateUtil.getDate("yyyy-MM-dd", newDate).before(DateUtil.getDate("yyyy-MM-dd", validDate))) {
				infoStatus = "3";
			}
			
			if("[\"on\"]".equals(isNew)) {
				isNew = "1";
			} else {
				isNew = "0";
				newExpireDate = "";
			}
			
			EasyRecord infoRecord = new EasyRecord(this.getTableName("KM_INFO"), "INFO_ID");
			infoRecord.put("INFO_TOPIC", infoTopic); // 知识标题
			searchCode = StringUtils.isNotBlank(searchCode) ? searchCode.replaceAll(",", ";") : searchCode;
			infoRecord.put("SEARCH_CODE", searchCode); // 关键字
			infoRecord.put("SECRET_LEVEL", secretLevel); // 关键字
			infoRecord.put("KM_PROVIDER", kmProvider); // 关键字
			infoRecord.put("IS_NEW", isNew); // 是否新知识
			infoRecord.put("NEW_EXPIRE_DATE", newExpireDate); // 新知识有效期
			infoRecord.put("TEMPLATE_ID", templateId); // 模板ID
			infoRecord.put("LINK_FILE1", linkFile1); // 附件
			infoRecord.put("VALID_DATE", validDate); // 有效开始时间
			infoRecord.put("EXPIRE_DATE", expireDate); // 有效结束时间
			infoRecord.set("ITEM_COUNT", itemCount);
			
			if(StringUtils.isBlank(infoId)) {
				// 新增知识
				infoId = RandomKit.randomStr();
				
				infoRecord.put("INFO_ID", infoId);
				infoRecord.put("INFO_STATUS", "7"); // 知识点状态为待完善
				infoRecord.put("DIR_ID", dirId);
				infoRecord.put("KM_INFO_TYPE", "03");
				infoRecord.put("ENT_ID", entId);
				infoRecord.put("BUSI_ORDER_ID", busiOrderId);
				infoRecord.put("CREATE_USER", user.getUserAcc());
				infoRecord.put("CREATE_DATE", date);
				infoRecord.put("CREATE_USERNAME", user.getUserName());
				
				StringBuilder updateMemo = new StringBuilder();
				itemCount = addTemItem(templateId, infoId, user, date, updateMemo, query);
				
				infoRecord.put("UPDATE_MEMO", updateMemo.toString());
				infoRecord.put("ITEM_COUNT", itemCount);
				
				query.save(infoRecord);
				
			} else {
				// 检测知识点是否完善
				if (InfoUtil.getInstance().isPerfect(infoId, getDbName(), query) && !json.getBoolean("ITEM_UPDATE") && StringUtils.isBlank(infoStatus)) {
					if("FALSE".equals(Constants.IS_CHECK)) {
						infoStatus = "8";
					} else {
						infoStatus = "1";
					}
				} else if(StringUtils.isBlank(infoStatus)){
					infoStatus = "7";
				}
				
				String updateMemo = query.queryForString("SELECT UPDATE_MEMO FROM " + this.getTableName("KM_INFO") + " WHERE INFO_ID = ?", infoId);
				if(StringUtils.isNotBlank(updateMemoNew)) {
					updateMemo += user.getUserAcc() + "(" + user.getUserName() + ")" + "[" + date + "]：" + updateMemoNew + "&#10;";
				}
				
				infoRecord.put("INFO_STATUS", infoStatus);
				infoRecord.put("INFO_ID", infoId);
				infoRecord.put("UPDATE_USER", user.getUserAcc());
				infoRecord.put("UPDATE_MEMO", updateMemo);
				infoRecord.put("UPDATE_DATE", date);
				infoRecord.put("UPDATE_USERNAME", user.getUserName());
				
				if(json.getBoolean("ITEM_UPDATE")) {

					delItemByInfoId(infoId,query);
					StringBuilder updateMemoStr = new StringBuilder(updateMemo);
					itemCount = addTemItem(templateId, infoId, user, date, updateMemoStr, query);
					updateMemo = updateMemoStr.toString();
					
					infoRecord.put("INFO_STATUS", "7"); // 知识点状态为待完善
				}
				
				infoRecord.put("UPDATE_MEMO", updateMemo);
				infoRecord.put("ITEM_COUNT", itemCount);
				
				query.update(infoRecord);
				
				InfoUtil.getInstance().operLog(infoId, null, "03", updateMemo, "02", user, this.getDbName(), entId, busiOrderId);
				
				query.execute("DELETE FROM " + this.getTableName("KM_INFO_Q") + " WHERE KM_INFO_ID = ?", infoId);
				
				query.execute("DELETE FROM " + this.getTableName("KM_CLASSIFY_REF") + " WHERE REF_ID = ?", infoId);
			}
			
			logger.info(CommonUtil.getClassNameAndMethod(this) + " classifyCode:" + classifyCode);
			if(StringUtils.isNotBlank(classifyCode)) {
				String [] classifyCodeArr = classifyCode.split(",");
				for (int i = 0; i < classifyCodeArr.length; i++ ) {
					EasyRecord questionRecord = new EasyRecord(this.getTableName("KM_CLASSIFY_REF"),"ID");
					questionRecord.put("ID", RandomKit.randomStr());
					questionRecord.put("CLASSIFY_ID", classifyCodeArr[i]);
					questionRecord.put("TYPE", "1");
					questionRecord.put("REF_ID", infoId);
					query.save(questionRecord);
				}
			}
			query.commit();
			
			if("0".equals(infoInitStatus)) {
				RobotUtils.getInstance().delRobot(infoId, null, "03", entId, busiOrderId, getDbName());
			}
			if("FALSE".equals(Constants.IS_CHECK)) {
				RobotUtils.getInstance().synRobotByInfoId(getDbName(), infoId, user, entId, busiOrderId);
			}
			
			InfoUtil.getInstance().operLog(infoId, null, "03", updateMemoNew, "01", user, this.getDbName(), entId, busiOrderId);
			return EasyResult.ok("",getI18nValue("操作成功!")).put("infoId", infoId);
		} catch (Exception e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
			}
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
			return EasyResult.error(999, getI18nValue("操作失败"));
		}
	}
	
	/**
	 * 知识项新增|编辑
	 * @return
	 */
	@InfAuthCheck(resId = "cc-km-manage-edit",msg = "没有权限")
	public EasyResult actionForKmItemEdit() {
		JSONObject json = this.getJSONObject();
		JSONObject itemJson = this.getJSONObject("KM_INFO_ITEM");
		EasyQuery query = this.getQuery();
		DBTypes types = query.getTypes();
		try {
			query.begin();
			
			// 获取知识点基本数据
			String itemId = itemJson.getString("ID");
			String title = itemJson.getString("TITLE");
			String question = itemJson.getString("QUESTION");
			String keyCode = itemJson.getString("KEY_CODE");
			String infoId = itemJson.getString("INFO_ID");
			String idxOrder = itemJson.getString("IDX_ORDER");
			String fileBusiId = itemJson.getString("FILE_BUSI_ID");
			itemJson.remove("FILE_BUSI_ID");
			
			String bakup = itemJson.getString("UPDATE_MEMO");
			
			UserModel user = UserUtil.getUser(getRequest());
			String date = DateUtil.getCurrentDateStr();
			String entId = this.getEntId();
			String busiOrderId = this.getBusiOrderId();
			
			JSONObject infoJson = query.queryForRow("select INFO_STATUS,UPDATE_MEMO,VALID_DATE,EXPIRE_DATE from " + this.getTableName("KM_INFO") + " WHERE INFO_ID = ?", new Object[]{infoId}, new JSONMapperImpl());
			if(infoJson == null) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + " 找不到知识点信息infoId:" + infoId);
				return EasyResult.fail(getI18nValue("找不到知识点信息"));
			}
			
			String infoInitStatus = infoJson.getString("INFO_STATUS");
			String updateMemo = infoJson.getString("UPDATE_MEMO");
			int itemCount = query.queryForInt("SELECT COUNT(ID) FROM " + this.getTableName("KM_INFO_ITEM") + " WHERE INFO_ID = ?", infoId);
			
			EasySQL sql = new EasySQL();
			sql.append("SELECT COUNT(1) FROM " + this.getTableName("KM_INFO_ITEM"));
			sql.append("WHERE 1 = 1");
			sql.append(infoId, "AND INFO_ID = ?");
			sql.append(title, "AND TITLE = ?");
			sql.append(itemId, "AND ID <> ?");
			if(query.queryForInt(sql.getSQL(), sql.getParams()) > 0) {
				try { query.roolback(); } catch(SQLException e) {}
				return EasyResult.error(500, getI18nValue("知识项标题重复"));
			}
			
			String contentIdAdd = json.getString("contentIdAdd");
			String contentIdUpd = json.getString("contentIdUpd");
			String contentIdDel = json.getString("contentIdDel");

			JSONArray fileArray = json.getJSONArray("fileArray");
			
			//生成历史版本
			ArrayList<Object> historyVer = getHistoryVer(this.getQuery(),getDbName(),getEntId(),getBusiOrderId(),itemId,infoId, contentIdAdd, contentIdUpd, contentIdDel,types,json,bakup,user);
			boolean isUpdateItem = (boolean) historyVer.get(0);
			String oldItemVersion = (String) historyVer.get(1);
			
			
			EasyRecord itemRecord = new EasyRecord(this.getTableName("KM_INFO_ITEM"), "ID");
			itemRecord.put("TITLE", title); // 知识标题
			itemRecord.put("INFO_ID", infoId); // 知识标题
			itemRecord.put("NEED_UPDATE", "N"); // 知识标题
			itemRecord.put("IDX_ORDER", idxOrder);
			
			String operType = "21";
			
			// 附件处理
			String fileIds = ",";
			for(int i = 0;i < fileArray.size(); i++ ) {
				fileIds += fileArray.getJSONObject(i).getString("") + ",";
			}
			
			try {
				List<JSONObject> fileList = query.queryForList("SELECT ID,FILE_PATH FROM " + this.getTableName("C_CF_ATTACHMENT") + " WHERE BUSI_ID = ? AND IS_DEL = ?", new Object[]{itemId, "N"}, new JSONMapperImpl());
				if(CommonUtil.listIsNotNull(fileList)) {
					for (JSONObject fileJson : fileList) {
						String fileId = fileJson.getString("ID");
						if(fileIds.indexOf("," + fileId + ",") < 0) {
							File file = new File(fileJson.getString("FILE_PATH"));
							if(file.exists()) {
								file.delete();
							}
							query.execute("UPDATE " + this.getTableName("C_CF_ATTACHMENT") + " SET IS_DEL = ? WHERE ID = ?", "Y", fileId);
						}
					}
				}
			} catch (Exception e) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + " 清理附件失败, error:" + e.getMessage(), e);
			}
			
			if(StringUtils.isBlank(itemId)) {
				// 新增知识
				itemId = RandomKit.randomStr();
				
				
				itemRecord.put("ID", itemId);
				itemRecord.put("ENT_ID", entId);
				itemRecord.put("BUSI_ORDER_ID", busiOrderId);
				query.save(itemRecord);
				
				itemCount++;
				bakup = "新增知识项[" + itemRecord.getString("TITLE") + "]";
				
				if(StringUtils.isNotBlank(fileBusiId)) {
					query.execute("update " + this.getTableName("C_CF_ATTACHMENT") + " set BUSI_ID = ? where BUSI_ID = ?", itemId, fileBusiId);
				}
			} else {
				operType = "22";
				itemRecord.put("EX_FILE", fileArray.toJSONString()); // 知识标题
				
				if(StringUtils.isBlank(bakup)) {
					bakup = "补充知识项[" + title + "]内容";
				}
				
				// 修改知识
				itemRecord.put("ID", itemId);
				if(isUpdateItem){
					int itemVersionNum = StringUtils.isBlank(oldItemVersion) ? 0 : Integer.parseInt(oldItemVersion);
					int newItemVersion = itemVersionNum+1;
					itemRecord.put("ITEM_VERSION", newItemVersion);
				}
				// 检测知识点是否完善
				query.update(itemRecord);
				
				// 修改关联到此知识项的知识项状态
				query.execute("UPDATE " + this.getTableName("KM_INFO_ITEM") + " SET NEED_UPDATE = 'Y' WHERE ID IN (SELECT INFO_ITEM_ID FROM " + this.getTableName("KM_LINK_INFO_ITEM") + " WHERE LINK_INFO_ITEM_ID = ?)", itemId);
				
			}
			
			String str = "," + title + "," + question + "," + keyCode + ",";
			
			List<JSONObject> questionList = query.queryForList("SELECT * FROM " + this.getTableName("KM_INFO_ITEM_Q") + " WHERE KM_ITEM_ID = ?", new Object[]{itemId}, new JSONMapperImpl());
			Map<String, JSONObject> questionMap = new HashMap<String, JSONObject>();
			for (JSONObject questionJson : questionList) {
				questionMap.put(questionJson.getString("QUESTIONS"), questionJson);
			}
			
			query.execute("DELETE FROM " + this.getTableName("KM_INFO_ITEM_Q") + " WHERE KM_ITEM_ID = ?", itemId);
			
			// 维护相似问
			if(StringUtils.isNotBlank(question)) {
				String [] questionArray = question.split(",");
				for (String questionStr : questionArray) {
					if(str.indexOf("," + questionStr + ",") != str.lastIndexOf("," + questionStr + ",")) {
						try { query.roolback(); } catch(SQLException e) {}
						return EasyResult.error(500, getI18nValue("相似问[") + questionStr + getI18nValue("]与标题|相似问|关键字重复"));
					}
					
					if(questionMap.containsKey(questionStr)) {
						EasyRecord questionRecord = new EasyRecord(this.getTableName("KM_INFO_ITEM_Q"),"ID");
						questionRecord.putAll(questionMap.get(questionStr));
						questionRecord.remove("IDX_ORDER");
						query.save(questionRecord);
					} else {
						EasyRecord questionRecord = new EasyRecord(this.getTableName("KM_INFO_ITEM_Q"),"ID");
						questionRecord.put("ID", RandomKit.randomStr());
						questionRecord.put("KM_ITEM_ID", itemId);
						questionRecord.put("QUESTIONS", questionStr);
						questionRecord.put("CREATE_ACC", user.getUserAcc());
						questionRecord.put("CREATE_NAME", user.getUserName());
						questionRecord.put("CREATE_DEPT", user.getDeptCode());
						questionRecord.put("CREATE_DEPT_NAME", user.getDeptName());
						questionRecord.put("CREATE_TIME", date);
						questionRecord.put("Q_TYPE", "2");
						query.save(questionRecord);
					}
					
				}
			}
			
			// 维护相似问
			if(StringUtils.isNotBlank(keyCode)) {
				String [] keyCodeArray = keyCode.split(",");
				for (String keyCodeStr : keyCodeArray) {
					if(str.indexOf("," + keyCodeStr + ",") != str.lastIndexOf("," + keyCodeStr + ",")) {
						try { query.roolback(); } catch(SQLException e) {}
						return EasyResult.error(500, getI18nValue("关键字[") + keyCodeStr + getI18nValue("]与标题|相似问|关键字重复"));
					}
					if(questionMap.containsKey(keyCodeStr)) {
						EasyRecord questionRecord = new EasyRecord(this.getTableName("KM_INFO_ITEM_Q"),"ID");
						questionRecord.putAll(questionMap.get(keyCodeStr));
						questionRecord.remove("IDX_ORDER");
						query.save(questionRecord);
					} else {
						EasyRecord questionRecord = new EasyRecord(this.getTableName("KM_INFO_ITEM_Q"),"ID");
						questionRecord.put("ID", RandomKit.randomStr());
						questionRecord.put("KM_ITEM_ID", itemId);
						questionRecord.put("QUESTIONS", keyCodeStr);
						questionRecord.put("CREATE_ACC", user.getUserAcc());
						questionRecord.put("CREATE_NAME", user.getUserName());
						questionRecord.put("CREATE_DEPT", user.getDeptCode());
						questionRecord.put("CREATE_DEPT_NAME", user.getDeptName());
						questionRecord.put("CREATE_TIME", date);
						questionRecord.put("Q_TYPE", "1");
						query.save(questionRecord);
					}
				}
			}
			
			// 删除知识项下所有内容关联信息
			query.execute("DELETE FROM " + this.getTableName("KM_INFO_ITEM_CONTENT_REF") + " WHERE INFO_ITEM_ID = ?", itemId);
			
			JSONArray contentList = json.getJSONArray("contentList");
			for(int i = 0; i < contentList.size(); i++ ) {
				JSONObject contentInfo = contentList.getJSONObject(i);
				String contentId = contentInfo.getString("CONTENT_ID");
				
				EasyRecord contentRecord = new EasyRecord(this.getTableName("KM_INFO_ITEM_CONTENT"),"ID");
				contentRecord.put("ID", contentId);
				contentRecord.put("INFO_ITEM_ID", itemId);
				contentRecord.put("CONTENT_TYPE", contentInfo.getString("CONTENT_TYPE"));
				if("1".equals(contentInfo.getString("CONTENT_TYPE"))) {
					String content = URLDecoder.decode(URLEncoder.encode(contentInfo.getString("CONTENT"), "utf-8"), "utf-8");
					
					contentRecord.put("CONTENT", ContentDealUtils.parseUtf8(URLDecoder.decode(content, "utf-8")));
				} else {
					contentRecord.put("CONTENT", contentInfo.getString("CONTENT"));
				}
				contentRecord.put("IS_MAIN", contentInfo.getString("IS_MAIN"));
				if(contentIdAdd.indexOf(contentId) != -1) {
					contentRecord.put("CREATE_ACC", user.getUserAcc());
					contentRecord.put("CREATE_NAME", user.getUserName());
					contentRecord.put("CREATE_DEPT", user.getDeptCode());
					contentRecord.put("CREATE_DEPT_NAME", user.getDeptName());
					contentRecord.put("CREATE_TIME", date);
					query.save(contentRecord);
				} else if(contentIdUpd.indexOf(contentId) != -1) {
					contentRecord.put("UPDATE_ACC", user.getUserAcc());
					contentRecord.put("UPDATE_TIME", date);
					query.update(contentRecord);
				}
				
				// 重新关联知识内容渠道
				String [] channelArray = contentInfo.getString("channelList").split(",");
				for (String channelKey : channelArray) {
					EasyRecord contentRefInfo = new EasyRecord(this.getTableName("KM_INFO_ITEM_CONTENT_REF"), "ID");
					contentRefInfo.put("ID", RandomKit.randomStr());
					contentRefInfo.put("INFO_ITEM_ID", itemId);
					contentRefInfo.put("INFO_ITEM_CONTENT_ID", contentId);
					contentRefInfo.put("REF_TYPE", "1");
					contentRefInfo.put("REF_ID", channelKey);
					query.save(contentRefInfo);
				}
				
			}
			
			// 删除知识项
			String [] delArray = contentIdDel.split(",");
			for (int i = 0; i < delArray.length; i++) {
				EasyRecord contentRecord = new EasyRecord(this.getTableName("KM_INFO_ITEM_CONTENT"),"ID");
				contentRecord.put("ID", delArray[i]);
				query.deleteById(contentRecord);
			}
			
			EasyRecord infoRecord = new EasyRecord(this.getTableName("KM_INFO"),"INFO_ID");
			infoRecord.put("INFO_ID", infoId);
			if(!"4".equals(infoInitStatus) && !"3".equals(infoInitStatus)) {
				if (InfoUtil.getInstance().isPerfect(infoId, getDbName(), query)) {
					if("FALSE".equals(Constants.IS_CHECK)) {
						infoRecord.put("INFO_STATUS", "8");
					} else {
						infoRecord.put("INFO_STATUS", "1");
					}
				} else {
					infoRecord.put("INFO_STATUS", "7");
				}
			}
			
			updateMemo += user.getUserAcc() + "(" + user.getUserName() + ")" + "[" + date + "]：" + bakup + "&#10;";
			
			infoRecord.put("UPDATE_MEMO", updateMemo);
			infoRecord.put("UPDATE_DATE", date);
			infoRecord.put("ITEM_COUNT", itemCount);
			query.update(infoRecord);
			
			query.commit();
			
			InfoUtil.getInstance().operLog(infoId, itemId, "03", "修改知识项", operType, user, this.getDbName(), entId, busiOrderId);
			if("0".equals(infoInitStatus)) {
				RobotUtils.getInstance().delRobot(infoId, null, "03", entId, busiOrderId, getDbName());
			}
			if("FALSE".equals(Constants.IS_CHECK)) {
				RobotUtils.getInstance().synRobotByInfoId(getDbName(), infoId, user, entId, busiOrderId);
			}
			return EasyResult.ok("",getI18nValue("操作成功!")).put("itemId", itemId);
		} catch (Exception e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
			}
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
			return EasyResult.error(999, getI18nValue("操作失败"));
		}
	}
	
	/**
	 * 发送更新知识的mq消息
	 * @return
	 */
	public EasyResult actionForUpdateInfo() {
		JSONObject msgContent = new JSONObject();
		msgContent.put("infoId", this.getJSONObject().getString("infoId"));
		msgContent.put("entId", this.getEntId());
		msgContent.put("schema", this.getDbName());
		msgContent.put("command", "updateInfo");
		MQTopicUtil.sendTopicMsg(ServiceCommand.KM_INFO_EXTERNAL_MSG, msgContent.toJSONString());
		return EasyResult.ok();
	}
	
	/**
	 * 知识项删除
	 * @return
	 */
	public EasyResult actionForKmItemDel() {
		JSONObject jsonObject = this.getJSONObject();
		
		String itemId = jsonObject.getString("itemId");
		String infoId = jsonObject.getString("infoId");
		
		UserModel user = UserUtil.getUser(getRequest());
		EasyQuery query = this.getQuery();
		try {
			InfoUtil.getInstance().operLog(infoId, itemId, "03", "", "23", user, this.getDbName(), this.getEntId(), this.getBusiOrderId());
			
			query.begin();
			if (StringUtils.isBlank(itemId)) {
				return EasyResult.error(999, "知识项ID不能为空");
			}
			
			JSONObject itemInfo = query.queryForRow("select t1.TITLE,t2.UPDATE_MEMO,t2.INFO_STATUS,t2.ITEM_COUNT from " + this.getTableName("KM_INFO_ITEM") + " T1 LEFT JOIN " + this.getTableName("KM_INFO") + "  T2 ON T1.INFO_ID = T2.INFO_ID WHERE T1.ID = ?", new Object[]{itemId}, new JSONMapperImpl());
			
			String infoInitStatus = itemInfo.getString("INFO_STATUS");
			String infoStatus = infoInitStatus;
			String updateMemo = itemInfo.getString("UPDATE_MEMO");
			int itemCount = itemInfo.getIntValue("ITEM_COUNT");
			itemCount--;
			
			
			delItemById(itemId,query);
			
			updateMemo = user.getUserAcc() + "[" + DateUtil.getCurrentDateStr() + "]：" + "删除知识项[" + itemInfo.getString("TITLE") + "]&#10;";
			
			EasyRecord record = new EasyRecord(this.getTableName("KM_INFO"), "INFO_ID").setPrimaryValues(infoId);
			if(!"4".equals(infoInitStatus)) {
				if("0".equals(infoStatus) || "8".equals(infoStatus)) {
					infoStatus = "1";
				} else if(!InfoUtil.getInstance().isPerfect(infoId, getDbName(), query)) {
					infoStatus = "7";
				}
			}
			record.put("INFO_STATUS", infoStatus);
			record.put("UPDATE_MEMO", updateMemo);
			record.put("ITEM_COUNT", itemCount);
			query.update(record);
			
			
			query.commit();
			
			if("0".equals(infoInitStatus)) {
				RobotUtils.getInstance().delRobot(infoId,itemId , "03", this.getEntId(), this.getBusiOrderId(), this.getDbName());
			}
			
			try {
				List<JSONObject> fileList = query.queryForList("SELECT ID FILE_ID,FILE_PATH FROM " + this.getTableName("C_CF_ATTACHMENT") + " WHERE BUSI_ID = ? AND IS_DEL = ?", new Object[]{itemId, "N"}, new JSONMapperImpl());
				for (JSONObject fileJson : fileList) {
					String fileId = fileJson.getString("FILE_ID");
					File file = new File(fileJson.getString("FILE_PATH"));
					if(file.exists()) {
						file.delete();
					}
					query.execute("UPDATE " + this.getTableName("C_CF_ATTACHMENT") + " SET IS_DEL = ? WHERE ID = ?", "Y", fileId);
				}
			} catch (Exception e) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + " 清理附件失败, error:" + e.getMessage(), e);
			}
			
			return EasyResult.ok("",getI18nValue("操作成功!"));
		} catch (Exception e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
			}
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
			return EasyResult.error(999, "系统异常");
		}
	}
	
	/**
	 * 添加模板项
	 * @param templateId 模板ID
	 * @param infoId 知识ID
	 * @throws SQLException
	 */
	private int addTemItem(String templateId,String infoId, UserModel user, String date, StringBuilder updateMemo,EasyQuery query) throws SQLException {
		EasySQL temSql = new EasySQL("SELECT T2.NAME,T2.ID FROM " + this.getTableName("KM_TEMPLATE") + " T1");
		temSql.append("LEFT JOIN " + this.getTableName("KM_TEMPLATE_ITEM") + " T2 ON T1.ID = T2.TEMPLATE_ID");
		temSql.append("WHERE 1 = 1");
		temSql.append(templateId, "AND T1.ID = ?");
		temSql.append("ORDER BY T2.CREATE_TIME,NAME DESC");
		List<JSONObject> temList = query.queryForList(temSql.getSQL(), temSql.getParams(), new JSONMapperImpl());
		int i = 1;
		for (JSONObject temJson : temList) {
			String itemId = RandomKit.randomStr();
			EasyRecord itemRecord = new EasyRecord(this.getTableName("KM_INFO_ITEM"), "ID");
			itemRecord.put("ID", itemId);
			itemRecord.put("INFO_ID", infoId);
			itemRecord.put("TITLE", temJson.getString("NAME"));
			itemRecord.put("TEMPLATE_ITEM_ID", temJson.getString("ID"));
			itemRecord.put("ENT_ID", getEntId());
			itemRecord.put("BUSI_ORDER_ID", getBusiOrderId());
			itemRecord.put("IDX_ORDER", i);
			query.save(itemRecord);
			i++;
			
			List<JSONObject> itemTemplateQ = query.queryForList("SELECT ID,QUESTIONS,Q_TYPE FROM " + this.getTableName("KM_TEMPLATE_ITEM_Q") + " WHERE TEMPLATE_ITEM_ID = ? AND STATUS = ?", new Object[] {temJson.getString("ID"), "01"}, new JSONMapperImpl());
			if(CommonUtil.listIsNotNull(itemTemplateQ)) {
				for (JSONObject templateQ : itemTemplateQ) {
					EasyRecord questionRecord = new EasyRecord(this.getTableName("KM_INFO_ITEM_Q"),"ID");
					questionRecord.put("ID", RandomKit.randomStr());
					questionRecord.put("KM_ITEM_ID", itemId);
					questionRecord.put("TEMPLATE_ITEM_Q_ID", templateQ.getString("ID"));
					questionRecord.put("QUESTIONS", templateQ.getString("QUESTIONS"));
					questionRecord.put("CREATE_ACC", user.getUserAcc());
					questionRecord.put("CREATE_NAME", user.getUserName());
					questionRecord.put("CREATE_DEPT", user.getDeptCode());
					questionRecord.put("CREATE_DEPT_NAME", user.getDeptName());
					questionRecord.put("CREATE_TIME", date);
					questionRecord.put("Q_TYPE", templateQ.getString("Q_TYPE"));
					query.save(questionRecord);
				}
			}
			
			updateMemo.append(user.getUserAcc() + "[" + date + "]：新增模板知识项[" + temJson.getString("NAME") + "]&#10;");
		}
		return temList.size();
	}
	
	/**
	 * 删除知识点下所有知识项
	 * @param infoId
	 * @throws SQLException 
	 */
	private void delItemByInfoId(String infoId,EasyQuery query) throws SQLException {
		// 删除知识内容
		query.execute("DELETE FROM " + this.getTableName("KM_INFO_ITEM_CONTENT") + " WHERE INFO_ITEM_ID IN (SELECT ID FROM " + this.getTableName("KM_INFO_ITEM") + " WHERE INFO_ID = ?)", new Object[] { infoId });
		// 删除知识项和知识内容关联表
		query.execute("DELETE FROM " + this.getTableName("KM_INFO_ITEM_CONTENT_REF") + " WHERE INFO_ITEM_ID IN (SELECT ID FROM " + this.getTableName("KM_INFO_ITEM") + " WHERE INFO_ID = ?)", new Object[] { infoId });
		// 删除知识项相似问&关键字
		query.execute("DELETE FROM " + this.getTableName("KM_INFO_ITEM_Q") + " WHERE KM_ITEM_ID IN (SELECT ID FROM " + this.getTableName("KM_INFO_ITEM") + " WHERE INFO_ID = ?)", new Object[] { infoId });
		// 删除知识项
		query.execute("DELETE FROM " + this.getTableName("KM_INFO_ITEM") + " WHERE INFO_ID = ?", infoId);

		// 删除知识点下所有知识项关联数据
		query.execute("DELETE FROM " + this.getTableName("KM_LINK_INFO_ITEM") + " WHERE INFO_ID = ? OR LINK_INFO_ID = ?", infoId, infoId);
	}
	
	/**
	 * 删除知识项
	 * @param infoId
	 * @throws SQLException 
	 */
	private void delItemById(String itemId,EasyQuery query) throws SQLException {
		// 删除知识内容
		query.execute("DELETE FROM " + this.getTableName("KM_INFO_ITEM_CONTENT") + " WHERE INFO_ITEM_ID = ?", itemId);
		// 删除知识项和知识内容关联表
		query.execute("DELETE FROM " + this.getTableName("KM_INFO_ITEM_CONTENT_REF") + " WHERE INFO_ITEM_ID = ?", new Object[] { itemId });
		// 删除知识项相似问&关键字
		query.execute("DELETE FROM " + this.getTableName("KM_INFO_ITEM_Q") + " WHERE KM_ITEM_ID = ?", new Object[] { itemId });
		// 删除知识项
		query.execute("DELETE FROM " + this.getTableName("KM_INFO_ITEM") + " WHERE ID = ?", itemId);
		
		// 删除知识点下所有知识项关联数据
		query.execute("DELETE FROM " + this.getTableName("KM_LINK_INFO_ITEM") + " WHERE INFO_ITEM_ID = ? OR LINK_INFO_ITEM_ID = ?", itemId, itemId);
		
		// 删除操作日志
		// query.execute("DELETE FROM " + this.getTableName("KM_OPER_LOG") + " WHERE INFO_ITEM_ID = ?", itemId);
	}
	
	
	
	/**
	 * 新增知识点
	 * 
	 * @return
	 */
	public EasyResult actionForAdd() {
		JSONObject jsonObject = getJSONObject("kmInfo");
		EasyRecord record = new EasyRecord(this.getTableName("KM_INFO"), "INFO_ID");
		EasyQuery query = this.getQuery();
		try {
			query.begin();
			String infoId = jsonObject.getString("INFO_ID");
			String dirId = jsonObject.getString("DIR_ID");
			String infoTopic = jsonObject.getString("INFO_TOPIC");
			int itemCount = query.queryForInt("SELECT COUNT(1) FROM " + this.getTableName("KM_INFO_ITEM") + " WHERE INFO_ID = ?", new Object[] { infoId });

			record.set("INFO_ID", infoId);
			record.set("INFO_TOPIC", infoTopic);
			record.set("INFO_STATUS", jsonObject.getString("INFO_STATUS"));
			record.set("INFO_BOUND", "01");
			record.set("DIR_ID", dirId);
			record.set("SEARCH_CODE", jsonObject.getString("SEARCH_CODE"));
			record.set("VALID_DATE", jsonObject.getString("VALID_DATE"));
			record.set("EXPIRE_DATE", jsonObject.getString("EXPIRE_DATE"));
			record.set("IS_TOP", jsonObject.getString("IS_TOP"));
			record.set("IS_NEW", jsonObject.getString("IS_NEW"));
			record.set("NEW_EXPIRE_DATE", jsonObject.getString("NEW_EXPIRE_DATE"));
			record.set("TEMPLATE_ID", jsonObject.getString("TEMPLATE_ID"));
			record.set("LINK_FILE1", jsonObject.getString("LINK_FILE1"));
			record.set("LINK_FILE2", jsonObject.getString("LINK_FILE2"));
			record.set("LINK_FILE3", jsonObject.getString("LINK_FILE3"));
			record.set("LINK_FILE4", jsonObject.getString("LINK_FILE4"));
			record.set("LINK_FILE5", jsonObject.getString("LINK_FILE5"));
			if (StringUtils.isBlank(record.getString("EXPIRE_DATE"))) {// 没有设置失效日期的则失效日期设置无限期
				record.set("EXPIRE_DATE", "2099-01-01 00:00:00");
			}

			if (StringUtils.isNotBlank(getJsonPara("isAudit")) && getJsonPara("isAudit").contains("1")) { // 管理员可直接审核通过
				record.set("INFO_STATUS", 0);// 正常状态
				record.set("AUDIT_USER", this.getUserId());
				record.set("AUDIT_DATE", EasyDate.getCurrentDateString());

				String isNew = record.getString("IS_NEW");
				Date date = EasyDate.getCurrentDate();
				// 判断是否处于生效时间
				String validDate = record.getString("VALID_DATE");
				String expireDate = record.getString("EXPIRE_DATE");
				if (EasyDate.stringToDate("yyyy-MM-dd HH:mm:ss", validDate + " 00:00:00").getTime() > date.getTime()) {
					record.set("INFO_STATUS", "3");// 未到有效期的将状态设置为未到期
				} else if (date.getTime() > EasyDate.stringToDate("yyyy-MM-dd HH:mm:ss", expireDate + " 23:59:59").getTime()) {
					record.set("INFO_STATUS", "4");// 过了失效期的将状态设置为过期
				}

				if ("1".equals(isNew)) {
					String newExpireDate = record.getString("NEW_EXPIRE_DATE");
					if (date.getTime() > EasyDate.stringToDate("yyyy-MM-dd HH:mm:ss", newExpireDate + " 23:59:59").getTime()) {
						record.set("IS_NEW", "0");// 过了失效期的将状态设置为非新知识
					}
				}
			} else {
				record.set("INFO_STATUS", 1);// 新增知识为待审核状态
			}

			record.set("INFO_LEVEL", 1);
			record.set("INFO_KIND", 1);
			record.set("IS_HOT", 0);
			record.set("VISIT_COUNT", 0);// 浏览次数默认0
			record.set("TOPIC_CODE", UnicodeGBK2Alpha.getSimpleCharsOfString(infoTopic));
			record.set("SORT_ORDER", (Integer.parseInt("1" + dirId.substring(dirId.length() - 4, dirId.length())) - 10000));
			record.set("CREATE_USER", getUserId());
			record.set("CREATE_USERNAME", getUserName());
			record.set("UPDATE_USER", getUserId());
			record.set("UPDATE_USERNAME", getUserName());
			record.set("CREATE_DATE", EasyDate.getCurrentDateString());
			record.set("UPDATE_DATE", EasyDate.getCurrentDateString());
			record.set("ENT_ID", this.getEntId());
			record.set("BUSI_ORDER_ID", this.getBusiOrderId());
			record.set("ITEM_COUNT", itemCount);

			query.save(record);
			String entityName = this.getJSONObject().getString("entityName");
			if (StringUtils.isNotBlank(entityName)) {
				String[] entity = entityName.split("-");
				for (String e : entity) {
					EasyRecord entityRecord = new EasyRecord(this.getTableName("KM_INFO_Q"), "ID");
					entityRecord.put("ID", RandomKit.randomStr());
					entityRecord.put("KM_INFO_ID", infoId);
					entityRecord.put("QUESTIONS", e);
					entityRecord.put("IDX_ORDER", 0);
					entityRecord.put("CREATE_ACC", UserUtil.getRequestUserAcc(getRequest()));
					entityRecord.set("CREATE_NAME", getUserName());
					entityRecord.set("CREATE_DEPT", UserUtil.getUser(getRequest()).getDeptCode());
					entityRecord.set("CREATE_DEPT_NAME", UserUtil.getUser(getRequest()).getDeptName());
					entityRecord.set("CREATE_TIME", DateUtil.getCurrentDateStr());
					entityRecord.set("Q_TYPE", Constants.Q_TYPE_TWO);

					query.save(entityRecord);
				}
			}

			String classifyCode = this.getJSONObject().getString("classifyCode");
			if (StringUtils.isNotBlank(classifyCode)) {
				String[] classify = classifyCode.split(",");
				for (String c : classify) {
					EasyRecord classifyRecord = new EasyRecord(this.getTableName("KM_CLASSIFY_REF"), "ID");
					classifyRecord.put("ID", RandomKit.randomStr());
					classifyRecord.put("CLASSIFY_ID", c);
					classifyRecord.put("TYPE", "1");
					classifyRecord.put("REF_ID", infoId);
					query.save(classifyRecord);
				}
			}

			query.commit();
			if("0".equals(record.getString("INFO_STATUS"))) {
				RobotUtils.getInstance().synRobotByInfoId(getDbName(), infoId, UserUtil.getUser(getRequest()), getEntId(), getBusiOrderId());
			}
		} catch (SQLException e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				e1.printStackTrace();
			}
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok("",getI18nValue("操作成功!"));
	}

	public EasyResult actionForItemAdd() {
		JSONObject param = this.getJSONObject();
		String itemId = param.getString("itemId"); // 知识项ID
		String infoId = param.getString("infoId"); // 知识点ID
		String title = param.getString("title"); // 知识项标题
		String templateItemId = param.getString("templateId"); // 知识项模板ID
		String contentId = param.getString("contentId"); // 内容ID
		String channelId = param.getString("channelId"); // 知识渠道
		String isMainId = param.getString("isMainId"); //是否主内容
		String content = param.getString("content"); // 知识项内容(富文本)
		String content2 = param.getString("content2"); // 知识项内容(纯文本)
		EasyQuery query = this.getQuery();
		try {
			if (query.queryForInt("SELECT COUNT(1) FROM " + this.getTableName("KM_INFO_ITEM_CONTENT_REF")
					+ " WHERE INFO_ITEM_ID = ? AND REF_ID IN " + channelId.replace("[", "(").replace("]", ")")
					+ " AND INFO_ITEM_CONTENT_ID <> ?", new Object[] { itemId, contentId }) > 0) {
				return EasyResult.error(999, "该知识项已绑定此渠道");
			}
			
			if(StringUtils.isNotBlank(isMainId)&&"0".equals(isMainId)){
				if (query.queryForInt("SELECT COUNT(1) FROM " + this.getTableName("KM_INFO_ITEM_CONTENT")
					+ " WHERE INFO_ITEM_ID = ? AND IS_MAIN = '0' "
					+ " AND ID <> ?", new Object[] { itemId, contentId }) > 0) {
					return EasyResult.error(999, "该知识项只能绑定一个主内容");
				}
			}
			
			query.begin();
			
			//知识项历史版本保留数量
			String historyVerCount = AppContext.getContext(Constants.APP_NAME).getProperty("historyVerCount", "");
			int historyVerNum = 5;
			if(StringUtils.isNotBlank(historyVerCount)){
				historyVerNum = Integer.parseInt(historyVerCount);
			}

			// 新增/修改知识项
			EasyRecord itemRecord = new EasyRecord(this.getTableName("KM_INFO_ITEM"), "ID");
			itemRecord.put("ID", itemId);
			itemRecord.put("TITLE", title);
			// 判断该知识项是否存在
			EasyRow kmInfoItem = query.queryForRow("SELECT INFO_ID,ID,ITEM_VERSION,TITLE,TEMPLATE_ITEM_ID FROM " + this.getTableName("KM_INFO_ITEM") + " WHERE ID = ?",new Object[] { itemId });
			
			//知识项ID(关联 知识项内容历史版本 表)
			String itemVerId = "";
			
			if(kmInfoItem!=null){
				
				String itemVersion = kmInfoItem.getColumnValue("ITEM_VERSION");
				String titleVer = kmInfoItem.getColumnValue("TITLE");
				String templateItemIdVer = kmInfoItem.getColumnValue("TEMPLATE_ITEM_ID");
				String infoIdVer = kmInfoItem.getColumnValue("INFO_ID");
				String itemIdVer = kmInfoItem.getColumnValue("ID");
				//历史版本+1
				int itemVersionNum = Integer.parseInt(itemVersion);
				int newItemVersion = itemVersionNum+1;
				itemRecord.put("ITEM_VERSION", newItemVersion);
				query.update(itemRecord);
				//新增知识项历史版本
				EasyRecord itemRecordVer = new EasyRecord(this.getTableName("KM_INFO_ITEM_VER"), "ID");
				itemVerId = RandomKit.randomStr();
				itemRecordVer.put("ID",itemVerId);
				itemRecordVer.put("OLD_ITEM_ID", itemIdVer);
				itemRecordVer.put("INFO_ID", infoIdVer);
				itemRecordVer.put("TITLE", titleVer);
				itemRecordVer.put("TEMPLATE_ITEM_ID", templateItemIdVer);
				itemRecordVer.put("IDX_ORDER", 0);
				itemRecordVer.put("CLICK_COUNT", 0);
				itemRecordVer.put("USE_COUNT", 0);
				itemRecordVer.put("UNUSE_COUNT", 0);
				itemRecordVer.put("ENT_ID", getEntId());
				itemRecordVer.put("BUSI_ORDER_ID", getBusiOrderId());
				itemRecordVer.put("ITEM_VERSION", itemVersionNum);
				query.save(itemRecordVer);
				
				//根据历史版本数量 判断是否需要删除过旧 历史版本 historyVerNum
				List<EasyRow> itemVersionList = query.queryForList("SELECT ITEM_VERSION,ID FROM " + this.getTableName("KM_INFO_ITEM_VER") + " WHERE OLD_ITEM_ID = ? AND INFO_ID = ? ORDER BY ITEM_VERSION ",
						new Object[] { itemId,infoId });
				if(itemVersionList!=null&&itemVersionList.size()>historyVerNum){
					//需要删除过旧版本
					int size = itemVersionList.size();
					int sub = size-historyVerNum;
					StringBuffer itemVersionStr = new StringBuffer("");
					for (int i = 0; i < sub; i++) {
						EasyRow row = itemVersionList.get(i);
						String contentIdVer = row.getColumnValue("ID");
						//删除知识项内容过旧版本
						query.execute("DELETE FROM " + this.getTableName("KM_INFO_ITEM_CONTENT_VER")
						+ " WHERE INFO_ITEM_ID = ? ", new Object[] { contentIdVer});
						
						//删除关联过旧版本
						query.execute("DELETE FROM " + this.getTableName("KM_INFO_ITEM_CONTENT_REF_VER")
						+ " WHERE INFO_ITEM_ID = ? ", new Object[] { contentIdVer});
						
						itemVersionStr.append(row.getColumnValue("ITEM_VERSION")).append(",");
					}
					String delItem = itemVersionStr.substring(0, itemVersionStr.length()-1);
					//删除知识项过旧版本
					query.execute("DELETE FROM " + getTableName("KM_INFO_ITEM_VER")
					+ " WHERE OLD_ITEM_ID = ? AND INFO_ID = ? AND ITEM_VERSION IN (" + delItem+")", new Object[] { itemId, infoId});
				}
			}else{
				itemRecord.put("TEMPLATE_ITEM_ID", templateItemId);
				itemRecord.put("INFO_ID", infoId);
				itemRecord.put("IDX_ORDER", 0);
				itemRecord.put("CLICK_COUNT", 0);
				itemRecord.put("USE_COUNT", 0);
				itemRecord.put("UNUSE_COUNT", 0);
				itemRecord.put("ENT_ID", getEntId());
				itemRecord.put("BUSI_ORDER_ID", getBusiOrderId());
				query.save(itemRecord);
			}
			
			// 新增/修改知识内容
			EasyRecord contentRecord = new EasyRecord(this.getTableName("KM_INFO_ITEM_CONTENT"), "ID");
			contentRecord.put("ID", contentId);
			if (StringUtils.isBlank(content)) {
				contentRecord.put("CONTENT_TYPE", "2");
				contentRecord.put("CONTENT", content2);
			} else {
				contentRecord.put("CONTENT_TYPE", "1");
				contentRecord.put("CONTENT", content);
			}
			// 判断知识内容是否存在
			EasyRow kmInfoItemContent = query.queryForRow("SELECT * FROM " + this.getTableName("KM_INFO_ITEM_CONTENT") + " WHERE ID = ?",new Object[] { contentId });
			
			//历史版本内容ID(关联 知识项内容关联历史版本 表)
			String contentVerId = "";
			
			if(kmInfoItemContent!=null){	
				String infoItemIdVer = kmInfoItemContent.getColumnValue("INFO_ITEM_ID");
				String contentTypeVer = kmInfoItemContent.getColumnValue("CONTENT_TYPE");
				String contentVer = kmInfoItemContent.getColumnValue("CONTENT");
				String isMainVer = kmInfoItemContent.getColumnValue("IS_MAIN");
				String needUpdateVer = kmInfoItemContent.getColumnValue("NEED_UPDATE");
				String createAccVer = kmInfoItemContent.getColumnValue("CREATE_ACC");
				String createNameVer = kmInfoItemContent.getColumnValue("CREATE_NAME");
				String createDeptVer = kmInfoItemContent.getColumnValue("CREATE_DEPT");
				String createDeptNameVer = kmInfoItemContent.getColumnValue("CREATE_DEPT_NAME");
				String createTimeVer = kmInfoItemContent.getColumnValue("CREATE_TIME");
				String statusVer = kmInfoItemContent.getColumnValue("STATUS");
				String updateAccVer = kmInfoItemContent.getColumnValue("UPDATE_ACC");
				String updateTimeVer = kmInfoItemContent.getColumnValue("UPDATE_TIME");
				
				contentRecord.put("UPDATE_ACC", UserUtil.getRequestUserAcc(getRequest()));
				contentRecord.put("UPDATE_TIME", DateUtil.getCurrentDateStr());
				contentRecord.put("IS_MAIN",isMainId);
				query.update(contentRecord);
				//新增知识项内容历史版本
				EasyRecord contentRecordVer = new EasyRecord(this.getTableName("KM_INFO_ITEM_CONTENT_VER"), "ID");
				contentVerId = RandomKit.randomStr();
				contentRecordVer.put("ID", contentVerId);
				contentRecordVer.put("INFO_ITEM_ID", itemVerId);
				contentRecordVer.put("CONTENT_TYPE", contentTypeVer);
				contentRecordVer.put("CONTENT", contentVer);
				contentRecordVer.put("IS_MAIN", isMainVer);
				contentRecordVer.put("NEED_UPDATE", needUpdateVer);
				contentRecordVer.put("CREATE_ACC", createAccVer);
				contentRecordVer.put("CREATE_NAME", createNameVer);
				contentRecordVer.put("CREATE_DEPT", createDeptVer);
				contentRecordVer.put("CREATE_DEPT_NAME", createDeptNameVer);
				contentRecordVer.put("CREATE_TIME", createTimeVer);
				contentRecordVer.put("STATUS", statusVer);
				contentRecordVer.put("UPDATE_ACC", updateAccVer);
				contentRecordVer.put("UPDATE_TIME", updateTimeVer);
				query.save(contentRecordVer);
				
			} else {
				contentRecord.put("IS_MAIN",isMainId);
				contentRecord.put("INFO_ITEM_ID", itemId);
				contentRecord.put("CREATE_ACC", UserUtil.getRequestUserAcc(getRequest()));
				contentRecord.put("CREATE_NAME", UserUtil.getUserPrincipal(getRequest()).getUserName());
				contentRecord.put("CREATE_DEPT", UserUtil.getUser(getRequest()).getDeptCode());
				contentRecord.put("CREATE_DEPT_NAME", UserUtil.getUser(getRequest()).getDeptName());
				contentRecord.put("CREATE_TIME", DateUtil.getCurrentDateStr());
				query.save(contentRecord);
			}
			
			//判断 知识项内容关联 表 有没有记录contentVerId
			List<EasyRow> infoItemContentRefList = query.queryForList("SELECT * FROM " + this.getTableName("KM_INFO_ITEM_CONTENT_REF") + " WHERE INFO_ITEM_CONTENT_ID = ? AND INFO_ITEM_ID = ? AND REF_TYPE = '1' ", new Object[] { contentId, itemId, });
			if(infoItemContentRefList!=null&&infoItemContentRefList.size()>0){
				for (int i = 0; i < infoItemContentRefList.size(); i++) {
					String refIdVer = infoItemContentRefList.get(i).getColumnValue("REF_ID");
					String infoItemIdVer = infoItemContentRefList.get(i).getColumnValue("INFO_ITEM_ID");
					String infoItemContentIdVer = infoItemContentRefList.get(i).getColumnValue("INFO_ITEM_CONTENT_ID");
					EasyRecord refVer = new EasyRecord(this.getTableName("KM_INFO_ITEM_CONTENT_REF_VER"), "ID");
					refVer.put("ID", RandomKit.randomStr());
					refVer.put("INFO_ITEM_ID", itemVerId);
					refVer.put("INFO_ITEM_CONTENT_ID", contentVerId);
					refVer.put("REF_TYPE", "1");
					refVer.put("REF_ID", refIdVer);
					query.save(refVer);
				}
			}
			
			
			query.execute("DELETE FROM " + getTableName("KM_INFO_ITEM_CONTENT_REF")
					+ " WHERE INFO_ITEM_CONTENT_ID = ? AND INFO_ITEM_ID = ? AND REF_TYPE = '1' AND REF_ID NOT IN "
					+ channelId.replace("[", "(").replace("]", ")"), new Object[] { contentId, itemId });
			for (String channel : JSONArray.parseArray(channelId, String.class)) {
				EasyRecord record = new EasyRecord(this.getTableName("KM_INFO_ITEM_CONTENT_REF"), "ID");
				String itemContentRef = query.queryForString(
						"SELECT ID FROM " + this.getTableName("KM_INFO_ITEM_CONTENT_REF")
								+ " WHERE INFO_ITEM_CONTENT_ID = ? AND INFO_ITEM_ID = ? AND REF_TYPE = '1' AND REF_ID = ?",
						new Object[] { contentId, itemId, channel });
				if (StringUtils.isBlank(itemContentRef)) {
					itemContentRef = RandomKit.randomStr();
					record.put("ID", RandomKit.randomStr());
					record.put("INFO_ITEM_ID", itemId);
					record.put("INFO_ITEM_CONTENT_ID", contentId);
					record.put("REF_TYPE", "1");
					record.put("REF_ID", channel);
					query.save(record);
				} else {
					record.put("ID", itemContentRef);
					record.put("REF_ID", channel);
					query.update(record);
				}
			}
			
			// 修改知识点同步状态为待完善
			EasyRecord record=new EasyRecord(getTableName("KM_INFO"), "INFO_ID");
			record.put("INFO_ID", infoId);
			record.put("INFO_STATUS", "8");
			query.update(record);
			query.commit();
		} catch (SQLException e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				e1.printStackTrace();
			}
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok("",getI18nValue("操作成功!"));
	}

	public EasyResult actionForItemDel() {
		JSONObject param = this.getJSONObject();
		String itemId = param.getString("itemId"); // 知识内容ID
		String infoId = param.getString("infoId"); // 知识内容ID
		EasyQuery query = this.getQuery();
		try {
			// 判断该知识内容是否存在
			if (query.queryForInt("SELECT COUNT(1) FROM " + this.getTableName("KM_INFO_ITEM") + " WHERE ID = ?", new Object[] { itemId }) > 0) {
				query.execute("DELETE FROM " + this.getTableName("KM_INFO_ITEM_CONTENT_REF") + " WHERE INFO_ITEM_ID = ? ", new Object[] { itemId });
				query.execute("DELETE FROM " + this.getTableName("KM_INFO_ITEM_CONTENT") + " WHERE INFO_ITEM_ID = ?", new Object[] { itemId });
				EasyRecord record = new EasyRecord(this.getTableName("KM_INFO_ITEM"), "ID");
				record.put("ID", itemId);
				query.deleteById(record);
				
				if(query.queryForInt("SELECT COUNT(ID) FROM " + getTableName("KM_INFO_ITEM") + " WHERE INFO_ID = ?", infoId) == 0) {
					// 修改知识点同步状态为待完善
					record=new EasyRecord(getTableName("KM_INFO"), "INFO_ID");
					record.put("INFO_ID", infoId);
					record.put("INFO_STATUS", "7");
					query.update(record);
				}
				query.commit();
			}
		} catch (SQLException e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				e1.printStackTrace();
			}
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok("",getI18nValue("操作成功!"));
	}

	public EasyResult actionForContentDel() {
		JSONObject param = this.getJSONObject();
		String contentId = param.getString("contentId"); // 知识内容ID
		EasyQuery query = this.getQuery();
		try {
			// 判断该知识内容是否存在
			if (query.queryForInt("SELECT COUNT(1) FROM " + this.getTableName("KM_INFO_ITEM_CONTENT") + " WHERE ID = ?", new Object[] { contentId }) > 0) {
				query.begin();
				EasyRecord contentRecord = new EasyRecord(getTableName("KM_INFO_ITEM_CONTENT"), "ID");
				contentRecord.put("ID", contentId);
				query.deleteById(contentRecord);
				query.execute("DELETE FROM " + this.getTableName("KM_INFO_ITEM_CONTENT_REF")
						+ " WHERE INFO_ITEM_CONTENT_ID = ? ", new Object[] { contentId });
				query.commit();
			}
		} catch (SQLException e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				e1.printStackTrace();
			}
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok("",getI18nValue("操作成功!"));
	}

	public EasyResult actionForSynInfo() {
		String infoId = this.getJSONObject().getString("INFO_ID");
		try {
			EasyResult result = RobotUtils.getInstance().synRobotByInfoId(this.getDbName(), infoId, UserUtil.getUser(getRequest()), getEntId(), getBusiOrderId());
			result.setMsg(getI18nValue(result.getMsg()));
			return result;
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
		}
		return EasyResult.error();
	}

	// 已处理方法
	public EasyResult actionForProcessed() {
		EasyResult result = new EasyResult();

		String keyword = this.getRequest().getParameter("keyword");
		String dirname = this.getRequest().getParameter("dirname");
		String status = this.getRequest().getParameter("status");
		String handleacc = this.getRequest().getParameter("handleacc");
		String handleaccname = this.getRequest().getParameter("handleaccname");
		String number = this.getRequest().getParameter("number");
		
		EasyQuery query = this.getQuery();
		
		try {
			// 状态标记为已处理
			if ("1".equals(number)) {
				EasySQL sql = new EasySQL(" UPDATE " + getTableName("km_no_info_record"));
				sql.append(" SET STATUS='1', ");
				sql.append(UserUtil.getRequestUserAcc(getRequest()), " HANDLE_ACC=? ");// 获取当前用户账户
				sql.append(this.getUserName(), " ,HANDLE_ACC_NAME=? ");// 获取当前用户姓名
				sql.append(DateUtil.getCurrentDateStr(), " ,HANDLE_TIME=? ");// 获取当前时间
				sql.append(" where 1=1 ");
				sql.append(keyword, " and KEY_WORD=?");
				sql.append(dirname, " and DIR_NAME=?");
				sql.append(status, " and STATUS=?");
				sql.append(handleacc, " and HANDLE_ACC=?");
				sql.append(handleaccname, " and HANDLE_ACC_NAME=?");

				query.execute(sql.getSQL(), sql.getParams());

				// 状态标记为无需处理
			} else if ("2".equals(number)) {
				EasySQL sql = new EasySQL(" UPDATE " + getTableName("km_no_info_record"));
				sql.append(" SET STATUS='2', ");
				sql.append(UserUtil.getRequestUserAcc(getRequest()), " HANDLE_ACC=? ");// 获取当前用户账户
				sql.append(this.getUserName(), " ,HANDLE_ACC_NAME=? ");// 获取当前用户姓名
				sql.append(DateUtil.getCurrentDateStr(), " ,HANDLE_TIME=? ");// 获取当前时间
				sql.append(" where 1=1 ");
				sql.append(keyword, " and KEY_WORD=?");
				sql.append(dirname, " and DIR_NAME=?");
				sql.append(status, " and STATUS=?");
				sql.append(handleacc, " and HANDLE_ACC=?");
				sql.append(handleaccname, " and HANDLE_ACC_NAME=?");

				query.execute(sql.getSQL(), sql.getParams());
			}

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "处理失败，原因：" + e.getMessage(), e);
			result.addFail("处理失败，原因：" + e.getMessage());
		}
		return result;
	}

	/**
	 * 全量同步
	 * 
	 * @return
	 */
	public EasyResult actionForSynAllInfo() {
		String infoType = this.getJSONObject().getString("infoType");
		String entId = this.getEntId();
		String busiOrderId = this.getBusiOrderId();
		EasyResult result = RobotUtils.getInstance().synAllRobot(infoType, InfoUtil.getInstance().getDirByCache(UserUtil.getRequestUserAcc(getRequest()), "1", "1", getDbName(), entId, busiOrderId), entId, busiOrderId, getDbName());
		result.setMsg(getI18nValue(result.getMsg()));
		// 全量同步后自动重新加载
		RobotUtils.getInstance().reloadRobot(infoType, entId, busiOrderId);
		return result;
	}
	
	/**
	 * 过滤没有权限的目录(只显示有权限的目录)
	 * @return
	 */
	public JSONArray filterAccessData(JSONArray jsonArray,String accessType){
		if(this.isSuperUser()||getUserPrincipal().isAdmin()){//超管、管理员
			return jsonArray;
		}
		
		if(jsonArray != null && jsonArray.size()>0 && StringUtils.isNotEmpty(accessType)){
			List<JSONObject> accessDirList = this.getAccessDirList(accessType);
			Iterator<Object> iterator = jsonArray.iterator();
			while(iterator.hasNext()){
				JSONObject jsonObject = (JSONObject)iterator.next();
				String dirId = jsonObject.getString("DIR_ID");
				boolean hasAccess = false;
				for (int i = 0,len=accessDirList.size(); i < len; i++) {
					String type = accessDirList.get(i).getString("TYPE");
					if("1".equals(type)) {
						if(dirId.equals(accessDirList.get(i).getString("DIR_ID"))){
							hasAccess = true;
							break;
						}
					} else {
						if(dirId.startsWith(accessDirList.get(i).getString("DIR_ID"))){
							hasAccess = true;
							break;
						}
					}
				}
				if(!hasAccess){
					iterator.remove();
				}
			}
		}
		return jsonArray;
	}
	
	/**
	 * 编辑知识点
	 * 
	 * @return
	 */
	public EasyResult actionForUpdate() {
		JSONObject jsonObject = getJSONObject("kmInfo");
		EasyRecord record = new EasyRecord(getTableName("KM_INFO"), "INFO_ID");
		EasyQuery query = this.getQuery();
		try {
			query.begin();

			// 更新知识点
			String infoId = jsonObject.getString("INFO_ID");
			String dirId = jsonObject.getString("DIR_ID");
			String infoTopic = jsonObject.getString("INFO_TOPIC");
			int itemCount = query.queryForInt("SELECT COUNT(1) FROM " + getTableName("KM_INFO_ITEM") + " WHERE INFO_ID = ?", new Object[] { infoId });

			record.set("INFO_ID", infoId);
			record.set("INFO_TOPIC", infoTopic);
			record.set("INFO_STATUS", jsonObject.getString("INFO_STATUS"));
			record.set("INFO_BOUND", "1");
			record.set("DIR_ID", dirId);
			record.set("SEARCH_CODE", jsonObject.getString("SEARCH_CODE"));
			record.set("VALID_DATE", jsonObject.getString("VALID_DATE"));
			record.set("EXPIRE_DATE", jsonObject.getString("EXPIRE_DATE"));
			record.set("IS_TOP", jsonObject.getString("IS_TOP"));
			record.set("IS_NEW", jsonObject.getString("IS_NEW"));
			record.set("NEW_EXPIRE_DATE", jsonObject.getString("NEW_EXPIRE_DATE"));
			record.set("LINK_FILE1", jsonObject.getString("LINK_FILE1"));
			record.set("LINK_FILE2", jsonObject.getString("LINK_FILE2"));
			record.set("LINK_FILE3", jsonObject.getString("LINK_FILE3"));
			record.set("LINK_FILE4", jsonObject.getString("LINK_FILE4"));
			record.set("LINK_FILE5", jsonObject.getString("LINK_FILE5"));
			if (StringUtils.isBlank(record.getString("EXPIRE_DATE"))) {// 没有设置失效日期的则失效日期设置无限期
				record.set("EXPIRE_DATE", "2099-01-01 00:00:00");
			}

			if (StringUtils.isNotBlank(getJsonPara("isAudit")) && getJsonPara("isAudit").contains("1")) {
				record.set("INFO_STATUS", 0);// 正常状态
				record.set("AUDIT_USER", this.getUserId());
				record.set("AUDIT_DATE", EasyDate.getCurrentDateString());

				String isNew = record.getString("IS_NEW");
				Date date = EasyDate.getCurrentDate();
				// 判断是否处于生效时间
				String validDate = record.getString("VALID_DATE");
				String expireDate = record.getString("EXPIRE_DATE");
				if (EasyDate.stringToDate("yyyy-MM-dd HH:mm:ss", validDate + " 00:00:00").getTime() > date.getTime()) {
					record.set("INFO_STATUS", "3");// 未到有效期的将状态设置为未到期
				} else if (date.getTime() > EasyDate.stringToDate("yyyy-MM-dd HH:mm:ss", expireDate + " 23:59:59")
						.getTime()) {
					record.set("INFO_STATUS", "4");// 过了失效期的将状态设置为过期
				}

				if ("1".equals(isNew)) {
					String newExpireDate = record.getString("NEW_EXPIRE_DATE");
					if (date.getTime() > EasyDate.stringToDate("yyyy-MM-dd HH:mm:ss", newExpireDate + " 23:59:59")
							.getTime()) {
						record.set("IS_NEW", "0");// 过了失效期的将状态设置为非新知识
					}
				}
			} else {
				record.set("INFO_STATUS", 1);// 新增知识为待审核状态
			}
			record.set("TOPIC_CODE", UnicodeGBK2Alpha.getSimpleCharsOfString(infoTopic));
			record.set("SORT_ORDER",
					(Integer.parseInt("1" + dirId.substring(dirId.length() - 4, dirId.length())) - 10000));
			record.set("UPDATE_USER", getUserId());
			record.set("UPDATE_MEMO", jsonObject.getString("UPDATE_MEMO"));
			record.set("UPDATE_DATE", EasyDate.getCurrentDateString());
			record.set("ITEM_COUNT", itemCount);

			// 删除现有实体别名
			query.execute("DELETE FROM " + getTableName("KM_INFO_Q") + " WHERE KM_INFO_ID = ?",
					new Object[] { infoId });
			// 添加实体别名
			String entityName = this.getJSONObject().getString("entityName");
			if (StringUtils.isNotBlank(entityName)) {
				String[] entity = entityName.split("-");
				for (String e : entity) {
					EasyRecord entityRecord = new EasyRecord(this.getTableName("KM_INFO_Q"), "ID");
					entityRecord.put("ID", RandomKit.randomStr());
					entityRecord.put("KM_INFO_ID", infoId);
					entityRecord.put("QUESTIONS", e);
					entityRecord.put("IDX_ORDER", 0);
					entityRecord.put("CREATE_ACC", UserUtil.getRequestUserAcc(getRequest()));
					entityRecord.set("CREATE_NAME", getUserName());
					entityRecord.set("CREATE_DEPT", UserUtil.getUser(getRequest()).getDeptCode());
					entityRecord.set("CREATE_DEPT_NAME", UserUtil.getUser(getRequest()).getDeptName());
					entityRecord.set("CREATE_TIME", DateUtil.getCurrentDateStr());
					entityRecord.set("Q_TYPE", Constants.Q_TYPE_TWO);
					query.save(entityRecord);
				}
			}

			// 删除业务分类
			query.execute("DELETE FROM " + getTableName("KM_CLASSIFY_REF") + " WHERE REF_ID = ?",
					new Object[] { infoId });
			// 添加业务分类
			String classifyCode = this.getJSONObject().getString("classifyCode");
			if (StringUtils.isNotBlank(classifyCode)) {
				String[] classify = classifyCode.split(",");
				for (String c : classify) {
					EasyRecord classifyRecord = new EasyRecord(this.getTableName("KM_CLASSIFY_REF"), "ID");
					classifyRecord.put("ID", RandomKit.randomStr());
					classifyRecord.put("CLASSIFY_ID", c);
					classifyRecord.put("TYPE", "1");
					classifyRecord.put("REF_ID", infoId);
					query.save(classifyRecord);
				}
			}

			EasySQL sql = new EasySQL("SELECT UPDATE_MEMO,AUDIT_MEMO,INFO_TEXT,");
			sql.append("INFO_ID,INFO_TOPIC,INFO_STATUS,INFO_BOUND,DIR_ID,SORT_ORDER,");
			sql.append("IS_HOT,SEARCH_CODE,VALID_DATE,EXPIRE_DATE,CREATE_USER,CREATE_DATE,");
			sql.append("UPDATE_USER,AUDIT_USER,AUDIT_DATE,UPDATE_DATE,VISIT_COUNT,STR_FREE1,");
			sql.append("STR_FREE2,EXTERNAL_CODE,EXTERNAL_TYPE,INFO_KIND,INFO_LEVEL,ENT_ID,");
			sql.append("CREATE_USERNAME,UPDATE_USERNAME");
			sql.append("from " + getTableName("KM_INFO") + " WHERE INFO_ID = ?");
			JSONObject obj = query.queryForRow(sql.getSQL(), new Object[] { infoId }, new JSONMapperImpl());
			int num = query.queryForInt("select count(*) from " + getTableName("KM_INFO_HISTORY") + " where INFO_ID=?", new Object[] { infoId });
			EasyRecord hisRecord = new EasyRecord(getTableName("KM_INFO_HISTORY"));
			hisRecord.setColumns(obj);
			hisRecord.set("OPER_USER", getUserId());
			hisRecord.set("OPER_DATE", EasyDate.getCurrentDateString());
			hisRecord.set("OPER_DESC", "更新知识点");
			hisRecord.set("OPER_VERSION", num + 1);
			// 将原记录保存到历史记录里面
			query.save(hisRecord);
			query.update(record);
			query.commit();
			if("0".equals(record.getString("INFO_STATUS"))) {
				RobotUtils.getInstance().synRobotByInfoId(getDbName(), infoId, UserUtil.getUser(getRequest()), getEntId(), getBusiOrderId());
			}
		} catch (SQLException e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				this.error(e1.getMessage(), e1);
			}
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok("",getI18nValue("操作成功!"));
	}

	/**
	 * 批量删除知识点
	 */
	public EasyResult actionForBatchDelete() {
		EasyResult result = new EasyResult();
		JSONObject jsonObject = getJSONObject();
		if(CommonUtil.listIsNull(jsonObject.getJSONArray("infoIds"))) {
			return EasyResult.fail(getI18nValue("请选择要删除的知识点"));
		}
		
		UserModel user = UserUtil.getUser(getRequest());
		String entId = this.getEntId();
		String busiOrderId = this.getBusiOrderId();
		String infoType = jsonObject.getString("infoType");
		
		EasyQuery query = getQuery();
		try {
			
			List<String> infoIds = jsonObject.getJSONArray("infoIds").toJavaList(String.class);
			RobotUtils.getInstance().delRobot(String.join(",", infoIds), null, infoType, this.getEntId(), this.getBusiOrderId(), this.getDbName());
			
			query.begin();
			
			int count = 0;
			for (String infoId : infoIds) {
				InfoUtil.getInstance().operLog(infoId, null, infoType, "", "04", user, this.getDbName(), entId, busiOrderId);
				
				delItemByInfoId(infoId,query);
				
				try {
					List<JSONObject> fileList = query.queryForList("SELECT ID,FILE_PATH FROM " + this.getTableName("C_CF_ATTACHMENT") + " WHERE BUSI_ID IN (SELECT ID FROM " + this.getTableName("KM_INFO_ITEM") + " WHERE INFO_ID = ?) AND IS_DEL = ?", new Object[]{infoId, "N"}, new JSONMapperImpl());
					for (JSONObject fileJson : fileList) {
						String fileId = fileJson.getString("ID");
						File file = new File(fileJson.getString("FILE_PATH"));
						if(file.exists()) {
							file.delete();
						}
						query.execute("UPDATE " + this.getTableName("C_CF_ATTACHMENT") + " SET IS_DEL = ? WHERE FILE_ID = ?", "Y", fileId);
					}
				} catch (Exception e) {
					logger.error(CommonUtil.getClassNameAndMethod(this) + " 清理附件失败, error:" + e.getMessage(), e);
				}
				
				// 删除知识相似问
				query.execute("DELETE FROM " + getTableName("KM_INFO_Q") + " WHERE KM_INFO_ID = ?", infoId);
				// 删除知识点
				count += query.executeUpdate("DELETE FROM " + getTableName("KM_INFO") + " WHERE INFO_ID = ?", infoId);
				
				// 删除关联知识
				query.execute("DELETE FROM " + getTableName("KM_LINK_INFO") + " WHERE INFO_ID = ? OR LINK_INFO_ID = ? ", infoId, infoId);
				
				// 删除业务分类信息
				query.execute("DELETE FROM " + this.getTableName("KM_CLASSIFY_REF") + " WHERE REF_ID = ? ", infoId);
				
			}
			result.setMsg(getI18nValue("成功删除") + count + getI18nValue("条知识点！"));
			query.commit();
		} catch (Exception e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				this.error("删除知识点失败，原因：" + e1.getMessage(), e1);
				result.addFail("删除知识点失败，原因：" + e1.getMessage());
			}
			this.error("删除知识点失败，原因：" + e.getMessage(), e);
			result.addFail("删除知识点失败，原因：" + e.getMessage());
		}
		return result;
	}

	/**
	 * 批量更新知识点状态
	 */
	public EasyResult actionForBatchUpdateStatus() {
		EasyResult result = new EasyResult();
		JSONObject jsonObject = getJSONObject();
		EasyQuery query = getQuery();
		int count = 0;
		JSONArray infoIds = jsonObject.getJSONArray("infoIds");
		String infoStatus = jsonObject.getString("infoStatus");
		
		UserModel user = UserUtil.getUser(getRequest());
		String entId = this.getEntId();
		String busiOrderId = this.getBusiOrderId();
		try {
			query.begin();
			if (infoIds != null && infoIds.size() > 0) {
				for (int i = 0; i < infoIds.size(); i++) {
					count += query.executeUpdate("update " + getTableName("km_info") + " set info_status = ? where info_id = ?", new Object[] { infoStatus, infoIds.getString(i) });
					if("5".equals(infoStatus)) {
						InfoUtil.getInstance().operLog(infoIds.getString(i), "更改知识状态", "03", "", "03", user, this.getDbName(), entId, busiOrderId);
					} else if("0".equals(infoStatus)) {
						InfoUtil.getInstance().operLog(infoIds.getString(i), "更改知识状态", "03", "", "07", user, this.getDbName(), entId, busiOrderId);
					}
				}
				result.setMsg(getI18nValue("成功更新") + count + getI18nValue("条知识点！"));
			} else {
				result.addFail("请先选择需要更新的知识点！");
			}
			query.commit();
			// 更新索引
			if (infoIds != null && infoIds.size() > 0) {
				for (int i = 0; i < infoIds.size(); i++) {
					if("5".equals(infoStatus) || "6".equals(infoStatus)) {
						RobotUtils.getInstance().delRobot(infoIds.getString(i), null, "03", entId, busiOrderId, getDbName());
					} else {
						RobotUtils.getInstance().synRobotByInfoId(getDbName(), infoIds.getString(i), UserUtil.getUser(getRequest()), getEntId(), getBusiOrderId());
					}
				}
			}

		} catch (SQLException e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				this.error("更新知识点失败，原因：" + e1.getMessage(), e1);
				result.addFail("更新知识点失败，原因：" + e1.getMessage());
			}
			this.error("更新知识点失败，原因：" + e.getMessage(), e);
			result.addFail("更新知识点失败，原因：" + e.getMessage());
		}
		return result;
	}

	/**
	 * 加入回收站||还原回收知识点
	 */
	public EasyResult actionForRecycleInfo() {
		EasyResult result = new EasyResult();
		JSONObject jsonObject = getJSONObject();
		EasyQuery query = getQuery();
		
		String entId = this.getEntId();
		String busiOrderId = this.getBusiOrderId();
		String schema = this.getDbName();
		UserModel user = UserUtil.getUser(getRequest());
		int count = 0;
		JSONArray infoIds = jsonObject.getJSONArray("infoIds");
		JSONArray infoStatuss = jsonObject.getJSONArray("infoStatuss");
		String type = jsonObject.getString("type");// 1、加入回收站，2、还原
		try {
			query.begin();
			if (infoIds != null && infoIds.size() > 0) {
				for (int i = 0; i < infoIds.size(); i++) {
					String infoId = infoIds.getString(i);
					int infoStatus = "1".equals(type) ? infoStatuss.getInteger(i) + 100 : infoStatuss.getInteger(i) - 100;
					count += query.executeUpdate("update " + getTableName("km_info") + " set info_status = ? where info_id = ?", new Object[] { infoStatus, infoId });
				}
				result.setMsg(getI18nValue("成功更新") + count + getI18nValue("条知识点！"));
			} else {
				result.addFail(getI18nValue("请先选择需要更新的知识点！"));
			}
			query.commit();
			// 更新索引
			if (infoIds != null && infoIds.size() > 0) {
				for (int i = 0; i < infoIds.size(); i++) {
					String infoId = infoIds.getString(i);
					if("1".equals(type)) {
						RobotUtils.getInstance().delRobot(infoId, null, "03", entId, busiOrderId, schema);
					} else {
						RobotUtils.getInstance().synRobotByInfoId(schema, infoId, user, entId, busiOrderId);
					}
				}
			}

		} catch (SQLException e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				this.error("更新知识点失败，原因：" + e1.getMessage(), e1);
				result.addFail("更新知识点失败，原因：" + e1.getMessage());
			}
			this.error("更新知识点失败，原因：" + e.getMessage(), e);
			result.addFail("更新知识点失败，原因：" + e.getMessage());
		}
		return result;
	}

	/**
	 * 批量移动知识点到某目录下
	 */
	public EasyResult actionForMoveInfo() {
		EasyResult result = new EasyResult();
		JSONObject jsonObject = getJSONObject();
		EasyQuery query = getQuery();
		int count = 0;
		if(CommonUtil.listIsNull(jsonObject.getJSONArray("moveIds"))) {
			return EasyResult.fail(getI18nValue("请选择要移动的知识点"));
		}
		
		String dirId = jsonObject.getString("dirId");
		String entId = this.getEntId();
		String busiOrderId = this.getBusiOrderId();
		String schema = this.getDbName();
		
		List<String> infoIds = jsonObject.getJSONArray("moveIds").toJavaList(String.class);
		RobotUtils.getInstance().delRobot(String.join(",", infoIds), null, "03", this.getEntId(), this.getBusiOrderId(), this.getDbName());
		
		UserModel user = UserUtil.getUser(getRequest());
		try {
			query.begin();
			for (String infoId : infoIds) {
				count += query.executeUpdate("UPDATE " + this.getTableName("KM_INFO") + " SET DIR_ID = ? WHERE INFO_ID = ?", new Object[] { dirId, infoId });
				InfoUtil.getInstance().operLog(infoId, "移动知识点", "03", null, "08", user, schema, entId, busiOrderId);
			}
			result.setMsg(getI18nValue("成功更新") + count + getI18nValue("条知识点！"));
			query.commit();
		} catch (SQLException e) {
			try {
				query.roolback();
			} catch (SQLException e1) {}
			logger.error("更新知识点失败，原因：" + e.getMessage(), e);
			result.addFail("批量移动知识点失败，需要同步相关知识恢复");
		}
		
		RobotUtils.getInstance().synDirRobot(dirId, entId, busiOrderId, schema);
		return result;
	}

	/**
	 * 移动某一目录下的知识点到另一目录下(目录也移动)
	 */
	public EasyResult actionForMoveDirInfo() {
		EasyResult result = new EasyResult();
		JSONObject jsonObject = getJSONObject();
		EasyQuery query = getQuery();
		String moveDirId = jsonObject.getString("moveDirId");
		String targetDirId = jsonObject.getString("targetDirId");
		try {
			if (StringUtils.isEmpty(moveDirId)) {
				result.addFail(getI18nValue("请先选择需要移动知识点的目录！"));
			} else if (StringUtils.isEmpty(targetDirId)) {
				result.addFail(getI18nValue("请先选择需要移动到的知识点的目录！"));
			} else if (targetDirId.equals(moveDirId)) {
				result.addFail(getI18nValue("请勿移动到相同目录！"));
			} else {
				
				String entId = this.getEntId();
				String busiOrderId = this.getBusiOrderId();
				String schema = this.getDbName();
				String newDirId = getNewDirIdByPDirId(targetDirId);
				
				List<JSONObject> infoList = query.queryForList("SELECT INFO_ID FROM " + this.getTableName("KM_INFO") + " WHERE DIR_ID LIKE '" + moveDirId + "%'", new Object[]{}, new JSONMapperImpl());
				String infoId = ListUtils.listToString(infoList, "INFO_ID");
				
				// 删除对应知识点
				RobotUtils.getInstance().delRobot(infoId, null, "03", entId, busiOrderId, schema);
				
				query.executeUpdate("UPDATE " + getTableName("KM_DIR") + " SET DIR_ID=CONCAT(?,SUBSTR(DIR_ID,LENGTH(?)+1,LENGTH(DIR_ID))),UPDATE_USER=?,UPDATE_DATE=? WHERE DIR_ID LIKE '" + moveDirId + "%'", new Object[] { newDirId, moveDirId, this.getUserId(), EasyDate.getCurrentDateString() });
				query.executeUpdate("UPDATE " + getTableName("KM_INFO") + " SET DIR_ID = CONCAT(?,SUBSTR(DIR_ID,LENGTH(?)+1,LENGTH(DIR_ID))) WHERE DIR_ID LIKE '" + moveDirId + "%'", new Object[] { newDirId, moveDirId });
				
				// 同步新知识目录下的所有知识点
				RobotUtils.getInstance().synDirRobot(newDirId, entId, busiOrderId, schema);
				
				// 设置目录版本号
				cache.put(CEConstants.CK_CC_KM_AUTH_DIR_VERSION, System.currentTimeMillis());
			}
		} catch (SQLException e) {
			this.error("更新知识点失败，原因：" + e.getMessage(), e);
			result.addFail("更新知识点失败，原因：" + e.getMessage());
		}
		return result;
	}

	/**
	 * 获取新的目录ID
	 * 
	 * @param pdirId 父目录ID
	 * @return 目录ID
	 * @throws SQLException
	 */
	public String getNewDirIdByPDirId(String pDirId) throws SQLException {
		if (pDirId == null || "-1".equals(pDirId) || "1".equals(pDirId))
			pDirId = "";
		// 如果没有父节点，则获取最大的一个节点，如果最大节点小于1000则代表不存在节点，返回1000
		String newDirId = this.getQuery().queryForString("SELECT MAX(DIR_ID) as tmax FROM " + getTableName("KM_DIR")
				+ " WHERE DIR_ID like '" + pDirId + "____'", new Object[] {});
		if (StringUtils.isEmpty(newDirId))
			return pDirId + "1000";
		BigDecimal bd = new BigDecimal(newDirId);
		return bd.add(new BigDecimal(1)).toString();
	}

	/**
	 * 审核知识点
	 */
	@InfAuthCheck(resId = "cc-km-access", msg = "您无权访问!")
	public EasyResult actionForAudit() {
		JSONObject jsonObject = getJSONObject("kmInfo");
		try {
			String[] infoIds = jsonObject.getString("INFO_ID").split(",");
			EasyRecord record = new EasyRecord(getTableName("KM_INFO"), "INFO_ID");
			record.set("UPDATE_MEMO", ""); // 清空修改说明
			record.set("INFO_STATUS", jsonObject.get("INFO_STATUS"));
			record.set("AUDIT_MEMO", jsonObject.get("AUDIT_MEMO"));
			record.set("AUDIT_USER", this.getUserId());
			record.set("AUDIT_DATE", EasyDate.getCurrentDateString());
			for (int i = 0; i < infoIds.length; i++) {
				record.setPrimaryValues(infoIds[i]);
				/**
				 * 根据生效时间、实效时间新知识、有效时间修改知识点的状态
				 */
				EasyQuery query = this.getQuery();
				record = this.updateInfoForValidate(record);
				if("8".equals(record.getString("INFO_STATUS"))) {
					record.put("INFO_STATUS", "0");
					query.update(record);
					RobotUtils.getInstance().synRobotByInfoId(getDbName(), record.getString("INFO_ID"), UserUtil.getUser(getRequest()), this.getEntId(), this.getBusiOrderId());
				}else {
					query.update(record);
				}
			}
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok("",getI18nValue("操作成功!"));
	}

	/**
	 * 根据有效时间和实效时间、新知识有效时间更新知识点状态，如果目录失效或未到期，优先目录
	 * 
	 * @param infoId
	 * @throws SQLException
	 */
	public EasyRecord updateInfoForValidate(EasyRecord record) throws SQLException {
		EasyQuery query = this.getQuery();
		JSONObject obj = query.queryForRow("SELECT VALID_DATE,NEW_EXPIRE_DATE,EXPIRE_DATE,IS_NEW,DIR_ID from " + getTableName("KM_INFO") + " WHERE INFO_ID=?", new Object[] { record.getString("INFO_ID") }, new JSONMapperImpl());
		String isNew = obj.getString("IS_NEW");
		Date date = EasyDate.getCurrentDate();
		
		String validDate = obj.getString("VALID_DATE");
		String expireDate = obj.getString("EXPIRE_DATE");
		
		String newDate = DateUtil.getCurrentDateStr("yyyy-MM-dd");
		if(DateUtil.getDate("yyyy-MM-dd", expireDate).before(DateUtil.getDate("yyyy-MM-dd", newDate)) ) {
			record.set("INFO_STATUS", "4");
		} else if(DateUtil.getDate("yyyy-MM-dd", newDate).before(DateUtil.getDate("yyyy-MM-dd", validDate))) {
			record.set("INFO_STATUS", "3");
		}

		if ("1".equals(isNew)) {
			String newExpireDate = obj.getString("NEW_EXPIRE_DATE");
			if (StringUtils.isBlank(newExpireDate) || date.getTime() > EasyDate.stringToDate("yyyy-MM-dd HH:mm:ss", newExpireDate + " 23:59:59").getTime()) {
				record.set("IS_NEW", "0");// 过了失效期的将状态设置为非新知识
			}
		}

		return record;
	}

	/**
	 * 删除目录以及目录下的知识点
	 * 
	 * @return
	 */
	@InfAuthCheck(resId = "cc-km-manage-dirEdit", msg = "无权访问")
	public EasyResult actionForDeleteDir() {
		EasyQuery query = this.getQuery();
		String dirId = getJsonPara("dirId");
		if (StringUtils.isBlank(dirId)) {
			return EasyResult.fail(getI18nValue("请选择一个目录进行操作!"));
		}
		try {
			if(query.queryForInt("SELECT COUNT(1) FROM " + this.getTableName("KM_INFO") + " WHERE DIR_ID LIKE ?", new Object[]{dirId + "%"}) > 0) {
				return EasyResult.fail(getI18nValue("该目录或者子目录下还存在知识点，无法删除"));
			}

			String dirName = this.getQuery().queryForString("SELECT DIR_NAME FROM " + this.getTableName("KM_DIR") + " WHERE DIR_ID = ?", dirId);
			
			this.getQuery().execute("delete from "+this.getTableName("KM_DIR")+" where DIR_ID LIKE '" + dirId + "%'", new Object[]{});
			
			String key = "KM-DIR-" +this.getEntId()+"-" + dirId.substring(0,dirId.length()-4) + "-" + dirName;
			CommonLogger.logger.info(CommonUtil.getClassNameAndMethod(this) + " key:" + key);
			cache.delete(key);
			
			query.executeUpdate("DELETE FROM " + getTableName("KM_DIR") + " WHERE DIR_ID LIKE ?", new Object[] { dirId + "%" });
			
			//设置版本号
			cache.put(CEConstants.CK_CC_KM_AUTH_DIR_VERSION,System.currentTimeMillis());
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
			return EasyResult.fail(getI18nValue("删除失败"));
		}
		return EasyResult.ok(null, getI18nValue("删除成功"));
	}

	/**
	 * 名词删除
	 * 
	 * @return
	 */
	public EasyResult actionForNounDelete() {
		UserModel user = UserUtil.getUser(this.getRequest());
		EasyQuery query = this.getQuery();
		String dirId = getJsonPara("dirId");
		try {
			JSONObject info = query.queryForRow("SELECT * FROM " + getTableName("KM_INDUSTRY_TERM") + " WHERE ID = ?",
					new Object[] { dirId }, new JSONMapperImpl());
			query.execute("delete from " + getTableName("KM_INDUSTRY_TERM") + " where ID = ?",
					new Object[] { dirId });
			try {
				IService service = ServiceContext.getService(ServiceID.KM_INFO_INTERFACE);
				JSONObject param = new JSONObject();
				param.put("command", ServiceCommand.KM_INFO_NOUN);
				param.put("state", "delete");
				param.put("ENT_ID", user.getEpCode());
				param.put("BUSI_ORDER_ID", user.getBusiOrderId());
				param.put("CONTENT", info.getString("CONTENT"));
				JSONObject result = service.invoke(param);
				if (!GWConstants.RET_CODE_SUCCESS.equals(result.getString("respCode"))) {
					logger.error(CommonUtil.getClassNameAndMethod(this) + " 同步单条知识失败:删除知识失败," + result);
					return EasyResult.error(999, getI18nValue("操作失败"));
				}
			} catch (ServiceException e) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + " 同步单条知识失败:删除知识异常," + e.getMessage(), e);
			}
		} catch (SQLException e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				this.error(e1.getMessage(), e1);
				return EasyResult.fail(getI18nValue("操作失败"));
			}
			this.error(e.getMessage(), e);
			return EasyResult.fail(getI18nValue("操作失败"));
		}
		
		LogUtil.insertLog(this.getDbName(), new Yqlogger(user, Constants.APP_NAME, Yqlogger.OPER_TYPE_DEL, "删除行业名词"));
		return EasyResult.ok(null, getI18nValue("操作成功!"));
	}

	/**
	 * 关联工单
	 * 
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public EasyResult actionForQuoteWork() {
		String infoId = getJsonPara("infoId");
		String infoTopic = getJsonPara("infoTopic");
		Set<HashMap<String, String>> set = new HashSet<HashMap<String, String>>();
		Object obj = EhcacheKit.get(CacheName.halfHour.get(),
				"kmQuoteWorkInfo" + this.getUserPrincipal().getLoginAcct());
		if (obj != null) {
			set = (Set<HashMap<String, String>>) obj;
		}
		HashMap<String, String> map = new HashMap<String, String>();
		map.put(infoId, infoTopic);
		set.add(map);
		EhcacheKit.put(CacheName.halfHour.get(), "kmQuoteWorkInfo" + this.getUserPrincipal().getLoginAcct(), set);
		Set<HashMap<String, String>> set1 = EhcacheKit.get(CacheName.halfHour.get() , "kmQuoteWorkInfo" + this.getUserPrincipal().getLoginAcct());
		logger.info("actionForQuoteWork>>>>key" + ("kmQuoteWorkInfo" + this.getUserPrincipal().getLoginAcct()) + " 数量:" + set1.size());
		return EasyResult.ok("",getI18nValue("操作成功!"));
	}

	/**
	 * ==========================================================旧版资料库导入==============================================================
	 * 
	 * 导入知识列表，只是zip文件，里面包含多个知识列表excel以及多个知识点的excel文件
	 * 
	 * @return
	 */
	public EasyResult actionForUpload() {
		UserModel user = UserUtil.getUser(this.getRequest());
		impLogger.info("开始导入知识库列表：---------------------------------------------------------------------------------");
		EasyQuery query = this.getQuery();
		
		//返回给前端用户看的结果日志
		StringBuilder sb = new StringBuilder("<div style='padding:15px'>");
		//知识点文件导入成功失败量
		int infoCount = 0;
		int infoErrorCount = 0;
		int infoSuccCount = 0;
		
		//知识项文件导入成功失败量
		int itemCount = 0;
		int itemErrorCount = 0;
		int itemSuccCount = 0;
		
		
		try {
			
			LogUtil.insertLog(this.getDbName(), new Yqlogger(user, Constants.APP_NAME, Yqlogger.OPER_TYPE_IMPORT, "导入知识列表"));
			InfoUtil.getInstance().operLog(null, null, "03", "导入资料库", "10", user, this.getDbName(), this.getEntId(), this.getBusiOrderId());
			
			impLogger.info("开始加载所有的目录...");
			EasySQL dirSql = new EasySQL("SELECT DIR_ID,DIR_NAME ONE FROM " + getTableName("KM_DIR"));
			dirSql.append(" WHERE status = 0 ");
			dirSql.append(getEntId(), " AND ENT_ID = ? ");
			List<JSONObject> list1 = query.queryForList(dirSql.getSQL(), dirSql.getParams(), new JSONMapperImpl());

			for (JSONObject j : list1) {
				String val = j.getString("DIR_ID");
				String key = "KM-DIR-" + this.getEntId() + "-" + val.substring(0, val.length()-4) + "-" + j.getString("ONE");
				cache.put(key, val, 1800);
				impLogger.info("加入目录到缓存:" + key + " = " + val);
			}

			impLogger.info("开始加载所有的渠道...");
			EasySQL sql1 = new EasySQL("SELECT CHANNEL_KEY,NAME FROM " + getTableName("KM_CHANNEL") + "  WHERE 1=1 ");
			sql1.append(getEntId(), " AND ENT_ID = ? ");
			sql1.append(getBusiOrderId(), " AND BUSI_ORDER_ID = ? ");
			sql1.append("01", "AND STATUS = ?");
			List<JSONObject> listJson = query.queryForList(sql1.getSQL(), sql1.getParams(), new JSONMapperImpl());
			Map<String, String> map = new HashMap<String, String>();
			if (listJson != null && listJson.size() > 0) {
				for (int j = 0; j < listJson.size(); j++) {
					String CHANNEL_KEY = listJson.get(j).getString("CHANNEL_KEY");
					String NAME = listJson.get(j).getString("NAME");
					map.put(NAME, CHANNEL_KEY);
					impLogger.info("加载渠道到内存map中:" + NAME + " = " + CHANNEL_KEY);
				}
			}
			if (map.size() < 1) {
				impLogger.info("渠道不能为空!");
				return EasyResult.error(501, "导入失败,渠道不能为空!");
			}

			// 获取通用模板ID
			EasySQL sqlTemp = new EasySQL("select ID from " + getTableName("KM_TEMPLATE") + " WHERE NAME = '通用模板' AND STATUS = '01' ");
			sqlTemp.append(getEntId(), " AND ENT_ID = ? ");
			sqlTemp.append(getBusiOrderId(), " AND BUSI_ORDER_ID = ? ");
			List<JSONObject> listTemp = query.queryForList(sqlTemp.getSQL(), sqlTemp.getParams(), new JSONMapperImpl());
			String tempId = "";
			if (listTemp != null && listTemp.size() > 0) {
				tempId = (String) listTemp.get(0).get("ID");
			}
			impLogger.info("加载通用模板id..." + tempId);

			Part part = getFile("file");
			String name = part.getSubmittedFileName();
			impLogger.info("接收导入文件名:" + part.getSubmittedFileName());

			if (name.endsWith(".zip")) {
				String basePath = CEConstants.getAttachmentRootPath();
				if (StringUtils.isBlank(basePath)) {
					impLogger.error("上传失败:cc-base未配置附件存放目录!");
					return EasyResult.fail("上传失败:cc-base未配置附件存放目录!");
				}

				basePath = basePath + File.separator + "cc-km" + File.separator + "zip_temp";

				File file = new File(basePath);
				if (!file.exists()) {
					file.mkdirs();
					impLogger.info("创建目录:" + file.getAbsolutePath());
				}

				// 原文件存在則刪除
				String newFileName = basePath + File.separator + "kmimp-" + user.getUserAcc() + "-" + name;
				File f = new File(newFileName);
				if (f.exists()) {
					f.delete();
					impLogger.info("刪除原文件:" + newFileName);
				}
				// 上传文件
				InputStream in = part.getInputStream();
				byte[] buffer = new byte[1024];
				int len = 0;
				// 文件最终上传的位置
				OutputStream out = new FileOutputStream(newFileName);
				while ((len = in.read(buffer)) != -1) {
					out.write(buffer, 0, len);
				}
				out.close();
				in.close();

				File zipFile = new File(newFileName);
				impLogger.info("文件保存成功:" + newFileName + "," + zipFile.exists());

				String dir = basePath + File.separator + "kmimp-" + user.getUserAcc();

				// 删除该目录下的原文件
				FileUtil.deleFileByDir(dir);

				unZip(zipFile, dir);
				impLogger.info("文件解压成功:" + dir);

				List<File> kmInfoList = new ArrayList<File>();
				List<File> kmItemList = new ArrayList<File>();

				List<File> fileList = FileUtil.getDirFiles(dir);
				if (CommonUtil.listIsNull(fileList)) {
					impLogger.error("上传失败:压缩包里没有需要导入的文件!");
					return EasyResult.fail("上传失败:压缩包里没有需要导入的文件!");
				}

				for (File f1 : fileList) {
					String f1Name = f1.getName();
					if(!f1Name.endsWith(".xlsx")) {
						return EasyResult.fail("上传失败！</br>压缩包中包含的文件格式只能是：.xlsx");
					}
					if (f1Name.startsWith("M")) {
						kmInfoList.add(f1);
					} else {
						kmItemList.add(f1);
					}
				}

				impLogger.info("要导入知识列表:" + kmInfoList.size() + ",要导入知识项:" + kmItemList.size());

				int kmInfoLoadSize = 1;
				for (File f1 : kmInfoList) {
					try {
						infoCount++;
						impLogger.info("开始导入知识列表[" + (kmInfoLoadSize++) + "/" + kmInfoList.size() + "]:" + f1.getAbsolutePath());
						InputStream fin = new FileInputStream(f1);
						Workbook workbook = WorkbookFactory.create(fin);
						Sheet sheet = workbook.getSheetAt(0);
						int num = sheet.getLastRowNum();
						if (num < 1) {
							impLogger.info("文件 [ "+f1.getName()+" ] 读取数据为空，请导入有效数据!");
							sb.append("<font color='red'>文件 [ "+f1.getName()+" ] 读取数据为空，请导入有效数据!</font></br>");
							break;
						}
						if (num > 10000) {
							impLogger.info("文件 [ "+f1.getName()+" ] 导入数据过大，请分批导入!");
							sb.append("<font color='red'>文件 [ "+f1.getName()+" ] 导入数据过大，请分批导入!</font></br>");
							break;
						}
						
						sb.append("</br><font color='black'>导入知识点文件 [ "+f1.getName()+" ] 的结果如下：</font></br>");
						EasyResult result = impSaveKmInfo(PortraitUtil.getUploadExcelData(num, 14, sheet), tempId);
						impLogger.info("知识列表导入结果:" + result.toJsonString());
						sb.append(result.getString("sb"));
						
						infoErrorCount += result.getIntValue("infoErrorCount");
						infoSuccCount += result.getIntValue("infoSuccCount");
						
					} catch (Exception e) {
						impLogger.error("导入知识列表异常:" + e.getMessage(), e);
					}
				}
				//sb.append("</br><font color='black'>知识点的文件导入完毕！ 总量："+infoCount+" 成功：<font colot='red'>"+infoSuccCount+"</font> 失败：<font colot='red'>"+infoErrorCount+"</font>");

				int kmItemLoadSize = 1;
				for (File f1 : kmItemList) {
					FileInputStream fin = new FileInputStream(f1);
					try {
						itemCount++;
						impLogger.info("开始导入具体知识[" + (kmItemLoadSize++) + "/" + kmItemList.size() + "]:" + f1.getAbsolutePath());
						sb.append("</br><font color='black'>导入知识项文件 [ "+f1.getName()+" ] 的结果如下：</font></br>");
						JSONObject result = impSaveKmItem(fin, map, tempId);
						impLogger.info("知识项导入结果:" + result.toJSONString());
						sb.append(result.getString("sb"));
						
						int succ = result.getIntValue("itemSuccCount");
						if(succ>0){
							itemSuccCount += 1;
						}else{
							itemErrorCount += 1;
						}
						
						result = null;

					} catch (Exception e) {
						impLogger.info("导入知识项异常:" + e.getMessage(), e);
					} finally {
						if (fin != null) {
							try {
								fin.close();
							} catch (Exception e2) {
							}
						}
					}
				}
				//sb.append("</br><font color='black'>知识项文件导入完毕！ 总量："+itemCount+" 成功：<font colot='red'>"+itemSuccCount+"</font> 失败：<font colot='red'>"+itemErrorCount+"</font>");

				getQuery().execute(" UPDATE " + getTableName("KM_INFO") + " SET ITEM_COUNT = (SELECT COUNT(1) FROM " + getTableName("KM_INFO_ITEM") + " T1 WHERE T1.INFO_ID = KM_INFO.INFO_ID) ");

				impLogger.info("导入完成!");
				sb.append("</div>");
				return EasyResult.ok(null, sb.toString());

			} else {
				String fileName = part.getName();
				// 知识列表
				if (fileName.startsWith("M-")) {
					Workbook workbook = WorkbookFactory.create(part.getInputStream());
					Sheet sheet = workbook.getSheetAt(0);
					int num = sheet.getLastRowNum();
					if (num < 1) {
						impLogger.info("读取数据为空，请导入有效数据!");
					}
					if (num > 10000) {
						impLogger.info("导入数据过大，请分批导入!");
					} else {
						EasyResult result = impSaveKmInfo(PortraitUtil.getUploadExcelData(num, 14, sheet), tempId);
						impLogger.info("知识列表导入结果:" + result.toJsonString());
						
						infoErrorCount += result.getIntValue("infoErrorCount");
						infoSuccCount += result.getIntValue("infoSuccCount");
						
						return EasyResult.ok(null, "导入完成!<br>知识点导入结果,成功:"+infoSuccCount+",失败:"+infoErrorCount);
					}
				} else {// 单个知识项
					JSONObject result = impSaveKmItem(part.getInputStream(), map, tempId);
					impLogger.info("知识项导入结果:" + result.toJSONString());
					
					itemSuccCount += result.getIntValue("itemSuccCount");
					itemErrorCount += result.getIntValue("itemErrorCount");
					
					return EasyResult.ok(null, "导入完成!<br>知识项导入结果,成功:"+itemSuccCount+",失败:"+itemErrorCount);
				}

				return EasyResult.fail("导入失败,数据量异常!");
			}

		} catch (Exception e) {
			logger.error("上传数据异常：" + e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	}

	/**
	 * 将excel中的知识列表记录导入到数据库中
	 * 
	 * @param list
	 *            excel里转换出来的知识列表集合(不包含知识项)
	 * @param tempId
	 *            模板id
	 * @return
	 */
	private EasyResult impSaveKmInfo(List<List<String>> list, String tempId) {
		EasyResult result = new EasyResult();
		
		UserModel user = UserUtil.getUser(getRequest());
		EasyQuery query = this.getQuery();
		if (list == null || list.size() == 0) {
			result.addFail("读取数据为空!");
			return result;
		}
		int len = list.size();
		int errorCount = 0;
		StringBuilder sb = new StringBuilder();		
		
		String pattern = "^[a-z0-9A-Z]+$";
		try {
			if (len > 0) {
				// query.begin();
				String msg = "";
				// 定义一级目录LIST

				for (int i = 0; i < list.size(); i++) {

					boolean scuuess = true;// 是否正确

					Map<String, Object> m = new HashMap<String, Object>();
					boolean lineScuuess = true;// 判断行内容
					String lineMsg = "";
					String val = "";
					for (int j = 0; j < list.get(i).size(); j++) {
						val = list.get(i).get(j);
						switch (j) {
						case 0:
							if (StringUtils.isBlank(val)) {
								scuuess = false;
								lineScuuess = false;
								lineMsg = lineMsg + " 知识编号不能为空";
								sb.append("<font color='red'>第"+(i+1)+"行 第"+(j+1)+"列的知识编号为空，跳过该知识点！</font></br>");
								break;
							} else if(!Pattern.matches(pattern, val)) {
								scuuess = false;
								lineScuuess = false;
								lineMsg = lineMsg + " 知识编号不合法";
								sb.append("<font color='red'>第"+(i+1)+"行 第"+(j+1)+"列的知识编号含有特殊字符，跳过该知识点！</font></br>");
								break;
							}
							// 判断知识编号是否重复,知识编号在各个企业里需要全局唯一
							EasySQL idSql = new EasySQL("select INFO_ID,ENT_ID from " + getTableName("KM_INFO")+ " WHERE INFO_ID =? ");
							EasyRow infoRow = query.queryForRow(idSql.getSQL(), val);
							if (infoRow != null ) {
								//该知识编号在其他企业里存在时，则不允许导入
								String infoEntId = infoRow.getColumnValue("ENT_ID");
								if(!this.getEntId().equals(infoEntId)){
									scuuess = false;
									lineScuuess = false;
									lineMsg = lineMsg + " 知识编号重复,已存在于企业:"+infoEntId;
									sb.append("<font color='red'>第"+(i+1)+"行 第"+(j+1)+"列知识编号重复,已存在于企业: "+infoEntId+" ，跳过该知识点！</font></br>");
									break;
								}
								delKmInfoById(val);
							}
							m.put("INFO_ID", val); // 知识编号
							break;
						case 1:
							if (StringUtils.isBlank(val)) {
								scuuess = false;
								lineScuuess = false;
								lineMsg = lineMsg + " 标题不能为空";
								sb.append("<font color='red'>第"+(i+1)+"行 第"+(j+1)+"列的标题为空，跳过该知识点！</font></br>");
								break;
							}
							m.put("INFO_TOPIC", val); // 标题
							break;
						case 2:
							//关键字不能包含知识点标题
							val.replace(String.valueOf(m.get("INFO_TOPIC")),"");
							m.put("SEARCH_CODE", val); // 关键字
							break;
						case 3:
							if (StringUtils.isBlank(val)) {
								scuuess = false;
								lineScuuess = false;
								lineMsg = lineMsg + " 知识分类一级不能为空";
								sb.append("<font color='red'>第"+(i+1)+"行 第"+(j+1)+"列的知识分类一级为空，跳过该知识点！</font></br>");
								break;
							}
							if (StringUtils.isNotBlank(val)) {
								m.put("KM_ONE", val); // 知识分类二级
							}
							break;
						case 4:
							if (StringUtils.isNotBlank(val)) {
								m.put("KM_TWO", val); // 知识分类二级
							}
							break;
						case 5:
							if (StringUtils.isNotBlank(val)) {
								m.put("KM_THREE", val); // 知识分类三级
							}
							break;
						case 6:
							if (StringUtils.isNotBlank(val)) { // 有效期设置（内容为永久/自定义）
								if ("永久".equals(val)) {
									m.put("VALID_DATE", "1900-01-01"); // 开始日期
									m.put("EXPIRE_DATE", "2999-12-31"); // 结束日期
									break;
								} else if ("自定义".equals(val)) {
									break;
								}
							}
							m.put("VALID_DATE", "1900-01-01"); // 开始日期
							m.put("EXPIRE_DATE", "2999-12-31"); // 结束日期
							break;
						case 7:
							if (StringUtils.isNotBlank(val)) {
								if(DateUtil.getDate("yyyy-MM-dd", val) != null) {
									m.put("VALID_DATE", DateUtil.getDateStr(DateUtil.getDate("yyyy-MM-dd", val)).substring(0, 10)); // 开始日期（选填）
									break;
								}
							}
							m.put("VALID_DATE", "1900-01-01"); // 开始日期
							break;
						case 8:
							if (StringUtils.isNotBlank(val)) {
								if(DateUtil.getDate("yyyy-MM-dd", val) != null) {
									m.put("EXPIRE_DATE", DateUtil.getDateStr(DateUtil.getDate("yyyy-MM-dd", val)).substring(0, 10)); // 结束日期（选填）
									break;
								}
							}
							m.put("EXPIRE_DATE", "2999-12-31"); // 结束日期
							break;
						case 12:
							if (StringUtils.isNotBlank(val)) { // 对内/对外
								if ("对内".equals(val)) {
									m.put("INFO_BOUND", "0"); // 内部0
									break;
								} else if ("对外".equals(val)) {
									m.put("INFO_BOUND", "1"); // 外部1
									break;
								} else {
									m.put("INFO_BOUND", "0"); // 内部0
									break;
								}
							} else {
								m.put("INFO_BOUND", "0"); // 内部0
								break;
							}
						default:
							break;
						}

					}
					
					// 判断当前知识点属于那一层级
					String kmOne = (String) m.get("KM_ONE");
					String kmTwo = (String) m.get("KM_TWO");
					String kmThree = (String) m.get("KM_THREE");

					String dirId = null;
					if (StringUtils.isNotBlank(kmOne)) {
						dirId = getDirId(kmOne, dirId, 1);
					}
					if (StringUtils.isNotBlank(kmTwo) && dirId != null) {
						dirId = getDirId(kmTwo, dirId, 2);
					}
					if (StringUtils.isNotBlank(kmThree) && dirId != null) {
						dirId = getDirId(kmThree, dirId, 3);
					}
					if (StringUtils.isBlank(dirId) && scuuess) {
						impLogger.error("导入第" + (i + 1) + "行失败" + "<br>获取目录id为空:" + kmOne + "," + kmTwo + "," + kmThree);
						sb.append("<font color='red'>第"+(i+1)+"行导入失败，获取目录id为空！</font></br>");
						errorCount++;
						continue;
					}
					
					if(DateUtil.getDate("yyyy-MM-dd", (String)m.get("VALID_DATE")).getTime() > DateUtil.getDate("yyyy-MM-dd", (String)m.get("EXPIRE_DATE")).getTime()) {
						impLogger.error("导入第" + (i + 1) + "行失败" + "<br>日期不合法");
						sb.append("<font color='red'>第"+(i+1)+"行导入失败，日期不合法！</font></br>");
						errorCount++;
						continue;
					}

					
					String tempDirId = InfoUtil.getInstance().getDirByCache(UserUtil.getRequestUserAcc(this.getRequest()), dirId, 
														"1", this.getDbName(), this.getEntId(), this.getBusiOrderId());
					if(!ADD_DIR_ID.contains(dirId) && StringUtils.isBlank(tempDirId)) {
						impLogger.error("导入第" + (i + 1) + "行失败" + "<br>没有目录权限");
						errorCount++;
						continue;
					}
					
					m.put("DIR_ID", dirId);

					impLogger.info("找到知识目录id:" + dirId + "," + scuuess);

					if (scuuess) {
						EasyRecord record = new EasyRecord(getTableName("KM_INFO"), new String[] { "INFO_ID" });
						record.setPrimaryValues(new Object[] { (String) m.get("INFO_ID") });
						record.set("TEMPLATE_ID", tempId);
						record.set("INFO_TOPIC", (String) m.get("INFO_TOPIC"));
						if (StringUtils.isNotBlank((String) m.get("SEARCH_CODE"))) {
							record.set("SEARCH_CODE", (String) m.get("SEARCH_CODE")); // 关键字
						} else {
							//record.set("SEARCH_CODE", (String) m.get("INFO_TOPIC")); // 关键字为空，将标题设置为关键字
						}
						record.set("DIR_ID", (String) m.get("DIR_ID"));
						record.set("VALID_DATE", (String) m.get("VALID_DATE"));
						record.set("EXPIRE_DATE", (String) m.get("EXPIRE_DATE"));
						record.set("INFO_BOUND", (String) m.get("INFO_BOUND"));
						record.set("SORT_ORDER", (Integer.parseInt("1" + dirId.substring(dirId.length() - 4, dirId.length())) - 10000));
						record.set("INFO_STATUS", "7"); // 新增知识为待完善
						record.set("INFO_LEVEL", 1);
						record.set("INFO_KIND", 1);
						record.set("IS_HOT", 0);
						record.set("IS_NEW", 1); // 新知识1
						SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
						Date date = new Date();
						Calendar calendar = Calendar.getInstance();
						calendar.setTime(date); // 设置为当前时间
						calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) + 1);
						date = calendar.getTime();
						record.set("NEW_EXPIRE_DATE", dateFormat.format(date)); // 有效期为当前时间往后一个月
						record.set("VISIT_COUNT", 0); // 浏览次数默认0
						record.set("CREATE_USER", user.getUserAcc());
						record.set("CREATE_USERNAME", user.getUserName());
						record.set("CREATE_DATE", EasyDate.getCurrentDateString());
						record.set("ENT_ID", this.getEntId());
						record.set("KM_INFO_TYPE", "03");//03-资料库
						record.set("BUSI_ORDER_ID", this.getBusiOrderId());

						try {
							query.save(record);
							msg = msg + "第" + (i + 1) + "行成功." + "<br>";
							sb.append("<font color='black'>第"+(i+1)+"行导入成功！</font></br>");
						} catch (SQLException e) {
							CommonLogger.logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
							// query.roolback();
							errorCount++;
						}
						// if ((i + 1 != errorCount) && ((i - errorCount + 1) %
						// 1000 == 0)) {
						// query.commit();
						// query.begin();
						// }
					} else {
						errorCount++;
						if (!lineScuuess) {
							msg = msg + "第" + (i + 1) + "行出错：" + lineMsg + "<br>";
						}
					}
					m.clear();
					m = null;
				}
				// query.commit();
				if (errorCount == 0) {
					result = EasyResult.ok(null, "添加成功");
				} else if (len - errorCount > 0) {
					result = EasyResult.fail("添加成功" + (len - errorCount) + "条,存在下列问题：<br>" + msg);
				} else {
					result = EasyResult.fail(msg);
				}
			}

		} catch (Exception e) {
			errorCount++;
			impLogger.error("添加数据失败！失败原因：" + e.getMessage(), e);
			result.addFail("添加数据失败！失败原因：" + e.getMessage());
			// try {
			// query.roolback();
			// } catch (SQLException e1) {
			// logger.error("添加数据失败！失败原因：" + e1.getMessage());
			// }
		}
		impLogger.info("当前文件要导入的数量：" + len + ",成功量:" + (len - errorCount) + ",失败量:" + errorCount);
		sb.append("当前文件要导入的数量：" + len + ",成功量:<font color='red'>" + (len - errorCount) + "</font>,失败量:<font color='red'>" + errorCount+"</font></br></br>");
		
		result.put("sb", sb);
		result.put("infoErrorCount", errorCount);
		result.put("infoSuccCount", len - errorCount);
		return result;
	}
	
	/**
	 * 知识项导入
	 * 
	 * @param in
	 *            输入流
	 * @param channelMap
	 *            渠道集合
	 * @param tempId
	 *            知识模板id
	 * @return
	 */
	public EasyResult impSaveKmItem(InputStream in, Map<String, String> channelMap, String tempId) {
		UserModel user = UserUtil.getUser(this.getRequest());
		EasyQuery query = this.getQuery();
		int len = 0;
		int success = 0;
		StringBuilder sb = new StringBuilder();
		
		try {
			// query.begin();

			Workbook workbook = WorkbookFactory.create(in);
			List<List<String>> list = new ArrayList<>();
			Sheet sheet = workbook.getSheetAt(0);
			int maxLine = sheet.getLastRowNum();
			if (maxLine < 1) {
				impLogger.error("读取数据为空，请导入有效数据!");
				return EasyResult.fail("读取数据为空，请导入有效数据!");
			}
			if (maxLine > 10000) {
				impLogger.info("导入数据过大，请分批导入!");
				return EasyResult.fail("导入数据过大，请分批导入!");
			}

			/**
			 * 
			 * Set<String> names = channelMap.keySet(); String defaultChannelKey
			 * = null; for(String name: names){ String ckey =
			 * channelMap.get(name); if(StringUtils.isNotBlank(ckey)){
			 * defaultChannelKey = ckey; break; } }
			 * 
			 * if(StringUtils.isBlank(defaultChannelKey)){
			 * impLogger.info("无法找到默认的渠道key!"); return
			 * EasyResult.fail("无法找到默认的渠道key!"); }
			 */

			int lastCellNum = 0;
			
			String pattern = "^[a-z0-9A-Z]+$";
			
			List<String> titleList = new ArrayList<String>();
			for (int ii = 0; ii <= maxLine; ii++) {
				List<String> rows = new ArrayList<>();
				Row row = sheet.getRow(ii);
				if (ii == 0) {
					lastCellNum = row.getLastCellNum();
				}
				if (row != null) {
					for (int j = 0; j < lastCellNum; j++) {
						Cell cell = row.getCell(j);
						if (cell != null) {
							String val = Utils.getCellValue(cell);
							if (StringUtils.isBlank(val)) {
								rows.add("");
							} else {
								rows.add(val);
							}
						} else {
							rows.add("");
						}
					}
					list.add(rows);
				}
			}

			String infoId = list.get(0).get(1);
			String infoStatus = "1";
			impLogger.info(" 开始导入知识点的知识项,知识点id:" + infoId);

			EasySQL sql1 = new EasySQL( "select DIR_ID from " + getTableName("KM_INFO") + " WHERE INFO_ID=? AND ENT_ID=? ");
			EasyRow row = query.queryForRow(sql1.getSQL(), infoId, this.getEntId());
			if (row == null) {
				impLogger.error(" 知识点不存在,该知识点的知识项无法导入,知识点id:" + infoId);
				sb.append("<font color='red'>当前文件的知识点不存在，该知识点的知识项无法导入!</font></br>");
				return EasyResult.fail("知识点不存在,该知识点的知识项无法导入!");
			}
			String dirId = row.getColumnValue("DIR_ID");

			if (StringUtils.isBlank(dirId)) {
				impLogger.error(" 知识点的目录id为空,该知识点的知识项无法导入,知识点id:" + infoId);
				sb.append("<font color='red'>知识点的目录id为空,该知识点的知识项无法导入,知识点id: "+infoId+"</font></br>");
				return EasyResult.fail("知识点的目录id为空,该知识点的知识项无法导入!");
			}

			
			impLogger.info(" 知识点的目录id:" + dirId + ",要导入的知识项：" + (list.size() - 3));

			deleteInfoItemByInfoId(infoId);

			len = list.size() - 3;

			for (int i = 3; i < list.size(); i++) {
				String channelKey = null;
				Map<Object, Object> m = new HashMap<Object, Object>();
				for (int j = 0; j < list.get(0).size(); j++) {
					String val = list.get(i).get(j);
					switch (j) {
					case 0:// 知识项编号
						if (StringUtils.isBlank(val)) {
							break;
						} else if(!Pattern.matches(pattern, val)) {
							m.put("ID", "error");
							break;
						}
						m.put("ID", val);
						break;
					case 1:// 知识项标题
						if (StringUtils.isBlank(val)) {
							break;
						}
						m.put("TITLE", val);
						break;
					case 2:// 渠道标签
						/**
						 * update 20191018 当没填写渠道时，默认关联所有渠道
						 * if(StringUtils.isBlank(val)){ val = "内部"; }
						 * if(StringUtils.isNotBlank(val)){ channelKey =
						 * String.valueOf( channelMap.get(val) );
						 * if(StringUtils.isBlank(channelKey) ||
						 * "null".equalsIgnoreCase(channelKey)){ channelKey =
						 * defaultChannelKey; impLogger.error("
						 * 无法找到名称为["+val+"]的渠道,默认关联第一个渠道["+defaultChannelKey+"]!"+(i+1));
						 * } } m.put("CHANNEL_NAME",val);
						 */
						if (StringUtils.isNotBlank(val)) {
							channelKey = String.valueOf(channelMap.get(val));
							if (StringUtils.isNotBlank(channelKey)) {
								m.put("CHANNEL_NAME", val);
							} else {
								channelKey = null;
							}
						}
						break;
					case 3:// 文本类型 1 纯文本、2 富文本
						m.put("CONTENT_TYPE", val);
						break;
					case 4:// 知识项内容
						m.put("CONTENT", val);
						break;
					case 5: // FAQ相似问句
						val.replace(String.valueOf(m.get("TITLE")),"");// 跳过与标题相同的相似问
						m.put("QUESTIONS", val);
						break;
					case 6: // 关键字
						val.replace(String.valueOf(m.get("TITLE")),"");// 跳过与标题相同的关键字
						m.put("KEY_CODE", val);
						break;
					default:
						break;
					}
				}
				
				String str = JSONObject.toJSON(m).toString();
				JSONObject jsonObject = JSONObject.parseObject(str);

				String id = String.valueOf(m.get("ID"));
				if (StringUtils.isBlank(id) || "null".equalsIgnoreCase(id)) {
					impLogger.error("第" + (i + 1) + "行 的 知识项编号为空,不导入!");
					sb.append("<font color='red'>第"+(i+1)+"行的 知识项编号为空,不导入!</font></br>");
					continue;
				}
				
				if("error".equals(id)) {
					impLogger.error("第" + (i + 1) + "行 的 知识项编号不合法,不导入!");
					sb.append("<font color='red'>第"+(i+1)+"行的 知识项编号不合法,不导入!</font></br>");
					continue;
				}
				
				String title = String.valueOf(m.get("TITLE"));
				if (StringUtils.isBlank(title) || "null".equalsIgnoreCase(title)) {
					impLogger.error("第" + (i + 1) + "行 的 知识项标题为空,不导入!");
					sb.append("<font color='red'>第"+(i+1)+"行的 知识项标题为空,不导入!</font></br>");
					continue;
				}
				
				if(titleList.contains(title)) {
					impLogger.error("第" + (i + 1) + "行 的 知识项标题重复,不导入!");
					sb.append("<font color='red'>第"+(i+1)+"行的 知识项标题重复,不导入!</font></br>");
					continue;
				}
				
				// 知识项内容为空时，取默认内容
				if (StringUtils.isBlank(jsonObject.getString("CONTENT"))) {
					impLogger.error("第" + (i + 1) + "行 的 知识项内容为空,不导入!");
					sb.append("<font color='red'>第"+(i+1)+"行的 知识项内容为空,不导入!</font></br>");
					continue;
				}
				
				String tempDirId = InfoUtil.getInstance().getDirByCache(UserUtil.getRequestUserAcc(this.getRequest()), dirId, 
						"1", this.getDbName(), this.getEntId(), this.getBusiOrderId());
				if(!ADD_DIR_ID.contains(dirId) && StringUtils.isBlank(tempDirId)) {
					impLogger.error("第" + (i + 1) + "行的目录没有权限，不导入!");
					continue;
				}
				
				if(StringUtils.isBlank(jsonObject.getString("CONTENT"))) {
					infoStatus = "7";
				} else {

					impLogger.info(CommonUtil.getClassNameAndMethod(this) + " content:" + jsonObject.getString("CONTENT"));
				}

				// 知识项ID
				String itemId = infoId + m.get("ID");

				// 添加内容表
				List<String> batch = new ArrayList<String>();
				String itemContentId = RandomKit.randomStr();// 内容表Id

				// 是否已保存过该知识项，一个知识项会有多个渠道的内容，会存在多行记录
				int itemCount = query.queryForInt("select count(1) from " + getTableName("KM_INFO_ITEM") + " where ID = ?", itemId);

				// 保存知识项内容
				String contentSql = "INSERT INTO " + getTableName("KM_INFO_ITEM_CONTENT")
						+ "(ID,INFO_ITEM_ID,CONTENT_TYPE,CONTENT,IS_MAIN,NEED_UPDATE,CREATE_ACC,CREATE_NAME,CREATE_DEPT,CREATE_DEPT_NAME,CREATE_TIME)values('"
						+ itemContentId + "','" + itemId + "','1','" + jsonObject.getString("CONTENT") + "','0','1','"
						+ user.getUserAcc() + "','" + user.getUserName() + "','" + user.getDeptCode() + "','"
						+ user.getDeptName() + "','" + DateUtil.getCurrentDateStr() + "')";
				batch.add(contentSql);

				// 保存知识项内容与渠道关联关系
				if (StringUtils.isNotBlank(channelKey)) {
					String refId = RandomKit.randomStr();// 内容表关联Id
					String ref = "INSERT INTO " + getTableName("KM_INFO_ITEM_CONTENT_REF")
							+ "(ID,INFO_ITEM_ID,INFO_ITEM_CONTENT_ID,REF_TYPE,REF_ID)values('" + refId + "','" + itemId
							+ "','" + itemContentId + "','1','" + channelKey + "')";
					batch.add(ref);
				} else {
					for (String channelName : channelMap.keySet()) {
						String cKey = channelMap.get(channelName);
						String refId = RandomKit.randomStr();// 内容表关联Id
						String ref = "INSERT INTO " + getTableName("KM_INFO_ITEM_CONTENT_REF")
								+ "(ID,INFO_ITEM_ID,INFO_ITEM_CONTENT_ID,REF_TYPE,REF_ID)values('" + refId + "','"
								+ itemId + "','" + itemContentId + "','1','" + cKey + "')";
						batch.add(ref);
					}
				}

				if (itemCount < 1) {
					// 保存知识项
					String sql = "INSERT INTO " + getTableName("KM_INFO_ITEM")
							+ "(ID,INFO_ID,TITLE,TEMPLATE_ITEM_ID,CLICK_COUNT,USE_COUNT,UNUSE_COUNT,ENT_ID,BUSI_ORDER_ID)values('"
							+ itemId + "','" + infoId + "','" + title + "','" + tempId
							+ "',0,0,0,'" + getEntId() + "','" + getBusiOrderId() + "')";
					batch.add(sql);

					// 保存知识相似问
					String[] questionArray = jsonObject.getString("QUESTIONS").split("\n");
					
					for (int j = 0; j < questionArray.length; j++) {
						batch.add("INSERT INTO " + getTableName("KM_INFO_ITEM_Q")
								+ "(ID,KM_ITEM_ID,QUESTIONS,IDX_ORDER,CREATE_ACC,CREATE_NAME,CREATE_DEPT,CREATE_DEPT_NAME,CREATE_TIME,Q_TYPE)values('"
								+ RandomKit.randomStr() + "','" + itemId + "','" + questionArray[j] + "'," + (j + 1) + ",'"
								+ user.getUserAcc() + "','" + user.getUserName() + "','" + user.getDeptCode() + "','"
								+ user.getDeptName() + "','" + DateUtil.getCurrentDateStr() + "','2')");
					}
					
					// 保存知识相似问
					String[] keyCodeArray = jsonObject.getString("KEY_CODE").split("\n");
					for (int j = 0; j < keyCodeArray.length; j++) {
						batch.add("INSERT INTO " + getTableName("KM_INFO_ITEM_Q")
								+ "(ID,KM_ITEM_ID,QUESTIONS,IDX_ORDER,CREATE_ACC,CREATE_NAME,CREATE_DEPT,CREATE_DEPT_NAME,CREATE_TIME,Q_TYPE)values('"
								+ RandomKit.randomStr() + "','" + itemId + "','" + keyCodeArray[j] + "'," + (j + 1) + ",'"
								+ user.getUserAcc() + "','" + user.getUserName() + "','" + user.getDeptCode() + "','"
								+ user.getDeptName() + "','" + DateUtil.getCurrentDateStr() + "','1')");
					}
				}

				titleList.add(title);
				
				query.executeBatch(batch);

				m.clear();
				m = null;
				batch.clear();
				batch = null;
				success++;
			}
			
			if(success == 0) {
				infoStatus = "7";
			}
			
			EasyRecord infoRecord = new EasyRecord(getTableName("KM_INFO"), "INFO_ID").setPrimaryValues(infoId);
			infoRecord.put("INFO_STATUS", infoStatus);
			query.update(infoRecord);
			
			// query.commit();
			impLogger.error(" 知识项导入成功,总条数:" + len + ",成功数:" + success + ",失败数:" + (len - success));
			sb.append("当前文件要导入的数量：" + len + ",成功量:<font color='red'>" + success + "</font>,失败量:<font color='red'>" + (len-success)+"</font></br></br>");

			list.clear();
			list = null;
			EasyResult result = EasyResult.ok("", "知识项导入成功！");
			result.put("itemErrorCount", len - success);
			result.put("itemSuccCount", success);
			result.put("sb", sb);
			return result;
		} catch (Exception e) {
			// try {
			// query.roolback();
			// } catch (SQLException e1) {
			// e1.printStackTrace();
			// }
			impLogger.error("知识库项导入数据异常：" + e.getMessage(), e);
			return EasyResult.error(500, "知识库导入失败！");
		}
	}

	
	/**
	 * 
	 * zip解压
	 * 
	 * @param srcFile
	 *            zip源文件
	 * 
	 * @param destDirPath
	 *            解压后的目标文件夹
	 * 
	 * @throws RuntimeException
	 *             解压失败会抛出运行时异常
	 * 
	 */
	public void unZip(File srcFile, String destDirPath) throws RuntimeException {

		long start = System.currentTimeMillis();
		// 判断源文件是否存在
		if (!srcFile.exists()) {
			throw new RuntimeException(srcFile.getPath() + "所指文件不存在");
		}
		// 开始解压
		ZipFile zipFile = null;
		try {
			Charset gbk = Charset.forName("GBK");
			zipFile = new ZipFile(srcFile, gbk);
			Enumeration<?> entries = zipFile.entries();
			while (entries.hasMoreElements()) {
				ZipEntry entry = (ZipEntry) entries.nextElement();
				impLogger.info("解压" + entry.getName());
				// 如果是文件夹，就创建个文件夹
				if (entry.isDirectory()) {
					String dirPath = destDirPath + "/" + entry.getName();
					File dir = new File(dirPath);
					dir.mkdirs();
				} else {
					// 如果是文件，就先创建一个文件，然后用io流把内容copy过去
					File targetFile = new File(destDirPath + "/" + entry.getName());
					// 保证这个文件的父文件夹必须要存在
					if (!targetFile.getParentFile().exists()) {
						targetFile.getParentFile().mkdirs();
					}
					targetFile.createNewFile();
					// 将压缩文件内容写入到这个文件中
					InputStream is = zipFile.getInputStream(entry);
					FileOutputStream fos = new FileOutputStream(targetFile);
					int len;
					byte[] buf = new byte[1024];
					while ((len = is.read(buf)) != -1) {
						fos.write(buf, 0, len);
					}

					// 关流顺序，先打开的后关闭
					fos.close();
					is.close();
				}

			}

			long end = System.currentTimeMillis();

			impLogger.info("解压完成，耗时：" + (end - start) + " ms");

		} catch (Exception e) {

			throw new RuntimeException("unzip error from ZipUtils", e);

		} finally {

			if (zipFile != null) {

				try {

					zipFile.close();

				} catch (IOException e) {

					e.printStackTrace();

				}
			}
		}
	}

	

	/**
	 * 删除知识相关的知识项数据，不包含知识本身
	 * 
	 * @param infoId
	 *            知识id
	 * @throws SQLException
	 */
	private void deleteInfoItemByInfoId(String infoId) throws SQLException {
		EasySQL sql4 = new EasySQL(" delete FROM " + getTableName("KM_INFO_ITEM_CONTENT_REF")
				+ "   WHERE EXISTS (SELECT 1 FROM " + getTableName("KM_INFO_ITEM") + " T2 WHERE T2.ID = "
				+ getTableName("KM_INFO_ITEM_CONTENT_REF") + ".INFO_ITEM_ID AND T2.INFO_ID=?) ");
		EasySQL sql2 = new EasySQL(" delete FROM " + getTableName("KM_INFO_ITEM_CONTENT")
				+ "   WHERE EXISTS (SELECT 1 FROM " + getTableName("KM_INFO_ITEM") + " T2 WHERE T2.ID = "
				+ getTableName("KM_INFO_ITEM_CONTENT") + ".INFO_ITEM_ID AND T2.INFO_ID=?) ");
		EasySQL sql3 = new EasySQL(" delete FROM " + getTableName("KM_INFO_ITEM_Q")
				+ "   WHERE EXISTS (SELECT 1 FROM " + getTableName("KM_INFO_ITEM") + " T2 WHERE T2.ID = "
				+ getTableName("KM_INFO_ITEM_Q") + ".KM_ITEM_ID AND T2.INFO_ID=?) ");
		EasySQL sql = new EasySQL(" delete from " + getTableName("KM_INFO_ITEM") + "  WHERE  INFO_ID=? ");

		EasyQuery query = getQuery();

		query.execute(sql4.getSQL(), infoId);
		query.execute(sql3.getSQL(), infoId);
		query.execute(sql2.getSQL(), infoId);
		query.execute(sql.getSQL(), infoId);

	}

	/**
	 * 删除知识，以及知识相关的所有数据
	 * 
	 * @param infoId
	 *            知识id
	 * @throws SQLException
	 */
	private void delKmInfoById(String infoId) throws SQLException {
		EasyQuery query = getQuery();
		deleteInfoItemByInfoId(infoId);
		query.execute(" delete from " + getTableName("KM_INFO_Q") + " WHERE KM_INFO_ID = ? ", infoId);
		query.execute(" delete from " + getTableName("KM_INFO") + " WHERE INFO_ID = ? ", infoId);
	}


	/**
	 * 获取目前，如果不存在则自动创建并加入到缓存
	 * 
	 * @param name
	 *            目录名称
	 * @param pid
	 *            目录父id
	 * @param i
	 *            目录层级 支持 1-3
	 * @return
	 * @throws SQLException
	 */
	private String getDirId(String name, String pid, int i) throws SQLException {
		EasyQuery query = this.getQuery();
		String key = "KM-DIR-" +this.getEntId()+"-" + (StringUtils.isBlank(pid)?"":pid) + "-" + name;
		String dirId = cache.get(key);
		if (StringUtils.isNotBlank(dirId)) {
			cache.put(key, dirId, 1800);
			return dirId;
		} else {
			EasySQL dirSql = new EasySQL("SELECT DIR_ID,DIR_NAME ONE FROM " + getTableName("KM_DIR"));
			dirSql.append(" WHERE status = 0 ");
			dirSql.append(name, " AND DIR_NAME = ? ");
			dirSql.appendRLike(pid, " AND DIR_ID LIKE ? ");
			dirSql.append(getEntId(), " AND ENT_ID = ? ");
			List<JSONObject> list1 = query.queryForList(dirSql.getSQL(), dirSql.getParams(), new JSONMapperImpl());
			if (CommonUtil.listIsNotNull(list1)) {
				JSONObject j = list1.get(0);
				String val = j.getString("DIR_ID");
				cache.put(key, val, 1800);
				impLogger.info("查询并加入目录到缓存:" + key + " = " + val);
				return val;
			}
		}
		
		impLogger.info(CommonUtil.getClassNameAndMethod(this) + " 开关：" + Constants.IMPORT_IS_ADD_DIR);
		if("y".equals(Constants.IMPORT_IS_ADD_DIR)) {
			
			// 缓存里没有，数据库里也没有，则新增；由于id是按四位计算，格式如10011001，不能按企业过滤，否则数据冲突
			EasySQL sql1 = new EasySQL(" select max(dir_id) as mid from " + getTableName("KM_DIR"));
			sql1.append(" WHERE   status = 0 ");
			sql1.append(" AND length(dir_id) = " + i * 4);
			if (StringUtils.isNotBlank(pid)) {
				sql1.appendRLike(pid, "  AND dir_id like '" + pid + "%'");
			}
			EasyRow row = query.queryForRow(sql1.getSQL(), sql1.getParams());
			if (row == null) {
				impLogger.error("查询最大的目录id失败:" + name + "," + pid + "," + i);
				return null;
			}
			
			// 获取为空时，默认取一个
			String maxDirId = row.getColumnValue("mid");
			if (maxDirId == null || "".equals(maxDirId) || "null".equalsIgnoreCase(maxDirId)) {
				if (StringUtils.isNotBlank(pid)) {
					maxDirId = pid + "1001";
				} else {
					maxDirId = "1001";
				}
			}
			
			impLogger.error("查询出最大的目录id:" + maxDirId + "," + sql1.getSQL());
			
			String base = "";
			String end = "";
			if (maxDirId.length() == 4) {
				end = String.valueOf(Integer.parseInt(maxDirId) + 1);
			} else if (maxDirId.length() == 8) {
				base = maxDirId.substring(0, 4);
				end = String.valueOf(Integer.parseInt(maxDirId.substring(4)) + 1);
			} else if (maxDirId.length() == 12) {
				base = maxDirId.substring(0, 8);
				end = String.valueOf(Integer.parseInt(maxDirId.substring(8)) + 1);
			}
			
			String newDirId = base + end;
			
			impLogger.info("计算出目录id:name=" + name + ",pid=" + pid + ",i=" + i + ",maxDirId=" + maxDirId + ",newDirId=" + newDirId + ",base=" + base + ",end=" + end);
			
			EasyRecord record = new EasyRecord(getTableName("KM_DIR"), "DIR_ID");
			record.put("DIR_ID", newDirId);
			record.put("DIR_NAME", name);
			record.put("STATUS", 0);
			record.put("HAS_CHILD", 0);
			record.put("SORT_ORDER", 0);
			record.put("DIR_BOUND", 0);
			
			record.put("DIR_LEVEL", i - 0);
			record.put("DIR_MEMO", name);
			record.put("SEARCH_CODE", name);
			record.put("SEARCH_TOPIC", name);
			record.put("VALID_DATE", "2019-09-10");
			record.put("EXPIRE_DATE", "2099-09-09");
			record.put("CREATE_DATE", DateUtil.getCurrentDateStr());
			record.put("DIR_GUID", "1");
			record.put("CREATE_USER", this.getUserId());
			record.put("ENT_ID", this.getEntId());
			query.save(record);
			ADD_DIR_ID.add(newDirId);
			
			cache.put(key, newDirId, 1800);
			
			/**
			 * 为创建目录的人所在的角色和部门添加编辑权限
			 */
			EasySQL sql = new EasySQL("SELECT kru.ROLE_ID FROM "+this.getTableName("km_role_user")+" kru LEFT JOIN "+this.getTableName("km_access_role")+" kar ON kar.ROLE_ID=kru.ROLE_ID WHERE 1=1");
			sql.append(getUserId()," and kru.user_id=? ");
			sql.append(" and kar.ROLE_ID NOT IN (SELECT t2.ROLE_ID FROM "+this.getTableName("km_role_user")+" t1 LEFT JOIN "+this.getTableName("km_access_role")+" t2 ON t1.ROLE_ID=t2.ROLE_ID WHERE 1=1 ");
			sql.append(record.get("DIR_ID")," and t2.DIR_ID=? ");
			sql.append(getUserId(),"AND t1.USER_ID=? GROUP BY t2.ROLE_ID) GROUP BY kru.ROLE_ID");
			List<JSONObject> roleList = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			
			if(roleList!=null && roleList.size()>0){
				for(JSONObject obj : roleList){
					EasyRecord roleRecord=new EasyRecord(this.getTableName("KM_ACCESS_ROLE"), "ROLE_ID","ACCESS_TYPE");
					roleRecord.set("ROLE_ID", obj.getString("ROLE_ID"));
					roleRecord.set("ACCESS_TYPE", "1");
					roleRecord.set("UPDATE_DATE", EasyDate.getCurrentDateString());
					roleRecord.set("ENT_ID", this.getEntId());
					roleRecord.set("DIR_ID", record.get("DIR_ID"));
					query.save(roleRecord);
				}
				
			}
			
			//设置版本号
			cache.put(CEConstants.CK_CC_KM_AUTH_DIR_VERSION,System.currentTimeMillis());
			
			impLogger.info("创建目录,并加入缓存:key:" + key);
			return newDirId;
		} else {
			return null;
		}
	}

	/**
	 * 指标导入
	 * 
	 * @return
	 */
	public JSONObject actionForTolead() {
		impLogger.info("开始导入单条知识....................");
		EasyQuery query = this.getQuery();
		// String dirId=this.getRequest().getParameter("dirId");
		try {
			Part part = getFile("file");
			// 获取通用模板ID
			EasySQL sqlTemp = new EasySQL("select ID from " + getTableName("KM_TEMPLATE") + " WHERE NAME = '通用模板' AND STATUS = '01' ");
			sqlTemp.append(getEntId(), " AND ENT_ID = ? ");
			sqlTemp.append(getBusiOrderId(), " AND BUSI_ORDER_ID = ? ");
			List<JSONObject> listTemp = query.queryForList(sqlTemp.getSQL(), sqlTemp.getParams(), new JSONMapperImpl());
			String tempId = "";
			if (listTemp != null && listTemp.size() > 0) {
				tempId = (String) listTemp.get(0).get("ID");
			}
			impLogger.info("加载通用模板id..." + tempId);

			impLogger.info("开始加载所有的渠道...");
			EasySQL sql1 = new EasySQL("select CHANNEL_KEY,NAME from " + getTableName("KM_CHANNEL") + "  WHERE 1=1 ");
			sql1.append("01", "AND STATUS = ?");
			sql1.append(getEntId(), " AND ENT_ID = ? ");
			sql1.append(getBusiOrderId(), " AND BUSI_ORDER_ID = ? ");
			List<JSONObject> listJson = query.queryForList(sql1.getSQL(), sql1.getParams(), new JSONMapperImpl());
			Map<String, String> map = new HashMap<String, String>();
			if (listJson != null && listJson.size() > 0) {
				for (int j = 0; j < listJson.size(); j++) {
					String CHANNEL_KEY = listJson.get(j).getString("CHANNEL_KEY");
					String NAME = listJson.get(j).getString("NAME");
					map.put(NAME, CHANNEL_KEY);
					impLogger.info("加载渠道到内存map中:" + NAME + " = " + CHANNEL_KEY);
				}
			}
			if (map.size() < 1) {
				impLogger.info("渠道不能为空!");
				return EasyResult.error(501, "导入失败,渠道不能为空!");
			}

			impLogger.info("开始导入具体知识");
			
			EasyResult result = impSaveKmItem(part.getInputStream(), map, tempId);
			
			impLogger.info("知识项导入结果:" + result.toJSONString());

			return result;
		} catch (Exception e) {
			logger.error("知识库项上传数据异常：" + e.getMessage(), e);
			return EasyResult.error(500, "知识库导入失败！");
		}
	}

	
	public EasyResult actionForIssupUpd() {
		EasyQuery query = this.getQuery();
		try {
			query.begin();
			updIssup(query);
			query.commit();
			return EasyResult.ok("",getI18nValue("操作成功!"));
		} catch (Exception e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				e1.printStackTrace();
			}
		}
		return EasyResult.error(999, "修改相似问失败");
	}

	private void updIssup(EasyQuery query) throws SQLException {
		JSONObject param = this.getJSONObject("kmIssup");
		EasyRecord record = new EasyRecord(getTableName("KM_INFO_ITEM_Q"), "ID");
		record.put("ID", param.getString("ID"));
		record.put("QUESTIONS", param.getString("QUESTIONS"));
		record.put("IDX_ORDER", param.getString("IDX_ORDER"));
		record.put("STATUS", param.getString("STATUS"));
		record.set("UPDATE_ACC", UserUtil.getUserPrincipal(getRequest()).getLoginAcct());
		record.set("UPDATE_TIME", DateUtil.getCurrentDateStr());
		query.update(record);
	}

	public EasyResult actionForIssupAdd() {
		EasyQuery query = this.getQuery();
		try {
			query.begin();
			JSONObject param = this.getJSONObject("kmIssup");
			
			EasySQL sql=new EasySQL();
			sql.append("select count(*) from "+getTableName("KM_INFO_ITEM_Q")+" where 1=1 ");
			sql.append(param.getString("itemId")," and KM_ITEM_ID=?");
			sql.append(param.getString("QUESTIONS")," and QUESTIONS=?");
			sql.append("2"," and Q_TYPE=?");
			
		    int number = getQuery().queryForInt(sql.getSQL(),sql.getParams());
		    //如果数据库已经有同样的相似问，不能重复插入
			if (number>0) {
				return EasyResult.fail("请不要插入重复数据");
			}else {
			//如果数据库没有同样的相似问，可以插入	
			String id = RandomKit.randomStr();
			EasyRecord record = new EasyRecord(getTableName("KM_INFO_ITEM_Q"), "ID");
			record.put("ID", id);
			record.put("KM_ITEM_ID", param.getString("itemId"));
			record.put("QUESTIONS", param.getString("QUESTIONS"));
			record.put("IDX_ORDER", param.getString("IDX_ORDER"));
			record.put("STATUS", param.getString("STATUS"));
			record.set("CREATE_ACC", UserUtil.getUserPrincipal(getRequest()).getLoginAcct());
			record.set("CREATE_NAME", UserUtil.getUserPrincipal(getRequest()).getUserName());
			record.set("CREATE_DEPT", UserUtil.getUser(getRequest()).getDeptCode());
			record.set("CREATE_DEPT_NAME", UserUtil.getUser(getRequest()).getDeptName());
			record.set("CREATE_TIME", DateUtil.getCurrentDateStr());
			record.set("Q_TYPE", "2");
			query.save(record);
			query.commit();
			return EasyResult.ok("",getI18nValue("操作成功!"));
		}
		} catch (Exception e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				e1.printStackTrace();
			}
		}
		return EasyResult.error(999, "添加相似问失败");
	}


	public EasyResult actionForIssupDel() {
		EasyQuery query = getQuery();
		try {
			query.begin();
			String id = this.getJSONObject().getString("ID");
			if (StringUtils.isNotBlank(id)) {
				String sql1 = "DELETE FROM " + getTableName("KM_INFO_ITEM_Q") + " WHERE ID = ?";
				query.execute(sql1, new Object[] { id });
				query.commit();
				return EasyResult.ok("",getI18nValue("操作成功!"));
			}
			query.roolback();
		} catch (Exception e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				e1.printStackTrace();
			}
		}
		return EasyResult.error(999, "删除相似问失败");
	}

	public EasyResult actionForAddRecord() {
		try {
			EasyQuery query = getQuery();
			JSONObject obj = this.getJSONObject();
			String SIMILAR_QUESTION = obj.getString("SIMILAR_QUESTION");
			String id = RandomKit.randomStr();
			EasyRecord record = new EasyRecord(getTableName("KM_FAQ"), "ID");
			record.setColumns(getJSONObject("kminfo"));
			record.set("ID", id);
			record.set("TYPE", "1");
			record.set("SIMILAR_QUESTION", SIMILAR_QUESTION);
			record.set("CREATE_TIME", EasyDate.getCurrentDateString());
			record.set("CREATE_NAME", getUserName());
			record.set("CREATE_DEPT", UserUtil.getUser(getRequest()).getDeptCode());
			record.set("CREATE_DEPT_NAME", UserUtil.getUser(getRequest()).getDeptName());
			record.set("CREATE_ACC", UserUtil.getUser(getRequest()).getUserAcc());
			record.set("ENT_ID", this.getEntId());
			query.save(record);
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok("",getI18nValue("操作成功!"));
	}

	public EasyResult actionForUpdateRecord() {
		JSONObject obj = this.getJSONObject();
		EasyRecord record = new EasyRecord(getTableName("KM_FAQ"), "ID");
		record.setColumns(getJSONObject("kminfo"));
		record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		record.set("UPDATE_ACC", UserUtil.getUser(getRequest()).getUserAcc());
		String SIMILAR_QUESTION = obj.getString("SIMILAR_QUESTION");
		record.set("SIMILAR_QUESTION", SIMILAR_QUESTION);
		try {
			this.getQuery().update(record);
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok("",getI18nValue("操作成功!"));
	}

	public EasyResult actionForNounUpdate() {
		UserModel user = UserUtil.getUser(this.getRequest());
		EasyRecord record = new EasyRecord(getTableName("KM_INDUSTRY_TERM"), "ID");
		record.setColumns(getJSONObject("kminfo"));
		record.remove("OLDINDUSTRYTERM");
		EasyQuery query = this.getQuery();
		try {
			boolean isExists = query.queryForExist("select count(1) from "+getTableName("KM_INDUSTRY_TERM")+" where ENT_ID=? and BUSI_ORDER_ID='"+this.getBusiOrderId()+"' and CONTENT=?", new Object[]{this.getEntId(),record.getString("CONTENT")});
			if (isExists) {
				return EasyResult.error(1, getI18nValue("行业名词已存在，无需修改"));
			}
			this.getQuery().update(record);
		  try {
				IService service = ServiceContext.getService(ServiceID.KM_INFO_INTERFACE);
				JSONObject param = new JSONObject();
				param.put("command", ServiceCommand.KM_INFO_NOUN);
				param.put("state", "update");
				param.put("ENT_ID", user.getEpCode());
				param.put("BUSI_ORDER_ID", user.getBusiOrderId());
				param.put("oldIndustryTerm", getJSONObject("kminfo").getString("OLDINDUSTRYTERM"));
				param.put("CONTENT", getJSONObject("kminfo").getString("CONTENT"));
				JSONObject result = service.invoke(param);
				if (!GWConstants.RET_CODE_SUCCESS.equals(result.getString("respCode"))) {
					logger.error(CommonUtil.getClassNameAndMethod(this) + " 同步单条知识失败:删除知识失败," + result);
					return EasyResult.error(999, getI18nValue("同步失败,系统异常"));
				}
			} catch (ServiceException e) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + " 同步单条知识失败:删除知识异常," + e.getMessage(), e);
			}
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok("",getI18nValue("操作成功!"));
	}

	public EasyResult actionForNounAdd() {
		try {
			UserModel user = UserUtil.getUser(this.getRequest());
			HttpServletRequest request = this.getRequest();
			EasyRecord record = new EasyRecord(getTableName("KM_INDUSTRY_TERM"), "ID");
			String id = RandomKit.randomStr();
			record.setColumns(getJSONObject("kminfo"));
			EasyQuery query = getQuery();
			boolean isExists = query.queryForExist("select count(1) from "+getTableName("KM_INDUSTRY_TERM")+" where ENT_ID=? and BUSI_ORDER_ID='"+this.getBusiOrderId()+"' and CONTENT=?", new Object[]{this.getEntId(),record.getString("CONTENT")});
			if (isExists) {
				return EasyResult.error(500, getI18nValue("行业名词已存在"));
			}
			record.set("ID", id);
			SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");// 设置日期格式
			record.set("CREATE_TIME", df.format(new Date()));
			record.set("CREATE_NAME", getUserName());
			record.set("CREATE_DEPT", UserUtil.getUser(getRequest()).getDeptCode());
			record.set("CREATE_DEPT_NAME", UserUtil.getUser(getRequest()).getDeptName());
			record.set("CREATE_ACC", UserUtil.getUser(getRequest()).getUserAcc());
			record.set("ENT_ID", this.getEntId());
			record.set("BUSI_ORDER_ID", this.getBusiOrderId());
			record.remove("OLDINDUSTRYTERM");
			query.save(record);
			
			String faqquestion = request.getParameter("faqquestion");
			if (StringUtils.isNoneBlank(faqquestion)) {
				//在km_no_info_record表中添加描述
				EasySQL sql = new EasySQL(" UPDATE " + getTableName("km_no_info_record"));
				sql.append(" SET STATUS='1', ");
				String desc2=" /添加到行业名词";
				sql.append(desc2," HANDLE_DESC=concat((CASE WHEN HANDLE_DESC IS NOT NULL THEN HANDLE_DESC ELSE '' END),?)");
				sql.append(UserUtil.getRequestUserAcc(getRequest()), ", HANDLE_ACC=? ");// 获取当前用户账户
				sql.append(this.getUserName(), " ,HANDLE_ACC_NAME=? ");// 获取当前用户姓名
				sql.append(DateUtil.getCurrentDateStr(), " ,HANDLE_TIME=? ");// 获取当前时间
				sql.append(" where 1=1 ");
				sql.append(request.getParameter("faqquestion"), " and KEY_WORD=?");
				query.execute(sql.getSQL(), sql.getParams());
			}
			try {
				IService service = ServiceContext.getService(ServiceID.KM_INFO_INTERFACE);
				JSONObject param = new JSONObject();
				param.put("command", ServiceCommand.KM_INFO_NOUN);
				param.put("state", "add");
				param.put("ENT_ID", user.getEpCode());
				param.put("BUSI_ORDER_ID", user.getBusiOrderId());
				param.put("CONTENT", getJSONObject("kminfo").getString("CONTENT"));
				JSONObject result = service.invoke(param);
				if (!GWConstants.RET_CODE_SUCCESS.equals(result.getString("respCode"))) {
					logger.error(CommonUtil.getClassNameAndMethod(this) + " 同步单条知识失败:删除知识失败," + result);
					return EasyResult.error(999, getI18nValue("同步失败,系统异常"));
				}
			} catch (ServiceException e) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + " 同步单条知识失败:删除知识异常," + e.getMessage(), e);
			}
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok("",getI18nValue("操作成功!"));
	}

	public JSONObject actionForPhraseImport() {
		UserModel user = UserUtil.getUser(this.getRequest());
		EasyQuery query = this.getQuery();
		String countParam="";
		JSONArray array=new JSONArray();
		try {
			String deptCode = UserUtil.getUser(getRequest()).getDept().getDeptCode();// 所属人部门编号
			Part part = getFile("file");
			Workbook workbook = WorkbookFactory.create(part.getInputStream());
			List<List<String>> list = new ArrayList<>();
			Sheet sheet = workbook.getSheetAt(0);
			int maxLine = sheet.getLastRowNum();
			int lastCellNum = 0;
			for (int ii = 0; ii <= maxLine; ii++) {
				List<String> rows = new ArrayList<>();
				Row row = sheet.getRow(ii);
				if (ii == 0) {
					lastCellNum = row.getLastCellNum();
				}
				if (row != null) {
					for (int j = 0; j < lastCellNum; j++) {
						Cell cell = row.getCell(j);
						if (cell != null) {
							String val = Utils.getCellValue(cell);
							if (StringUtils.isBlank(val)) {// isBlank用于判断单元格是否为空，如果为空，则返回TRUE；否则返回FALSE
								rows.add("");
							} else {
								rows.add(val);
							}
						} else {
							rows.add("");
						}
					}
					list.add(rows);
				}
			}
			for (int i = 1; i < list.size(); i++) {
				Map<Object, Object> m = new HashMap<Object, Object>();
				for (int j = 0; j < list.get(0).size(); j++) {
					switch (j) {
					case 0:
						m.put("CONTENT", list.get(i).get(j).trim());
						break;
					case 2:
					default:
						break;
					}
				}
				Date day = new Date();
				SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
				String date = df.format(day);
				String id = RandomKit.randomStr();// 分类细项ID
				String longUser = UserUtil.getUser(getRequest()).getUserAcc();// 获取登陆用户//获取登陆用户
				String longName = UserUtil.getUser(getRequest()).getUserName();// 获取登陆用户名称
				String deptCodeName = UserUtil.getUser(getRequest()).getDept().getDeptName();
				String str = JSONObject.toJSON(m).toString();
				JSONObject jsonObject = JSONObject.parseObject(str);
				jsonObject.put("ID", id);
				jsonObject.put("CREATE_ACC", longUser);
				jsonObject.put("CREATE_NAME", longName);
				jsonObject.put("CREATE_DEPT", deptCode);
				jsonObject.put("CREATE_DEPT_NAME", deptCodeName);
				jsonObject.put("CREATE_TIME", date);
				jsonObject.put("BUSI_ORDER_ID", this.getBusiOrderId());
				jsonObject.put("ENT_ID", this.getEntId());
				JSONObject json=new JSONObject();
				json.put("industryTerm",jsonObject.getString("CONTENT"));
				if("".equals(countParam)){
					countParam=jsonObject.getString("CONTENT");
				}else{
					countParam+=";"+jsonObject.getString("CONTENT");
				}
				array.add(json);
				EasyRecord recorSocre = new EasyRecord(getTableName("KM_INDUSTRY_TERM"), "ID").setColumns(jsonObject);
				
				int count = query.queryForInt("select count(1) from "+getTableName("KM_INDUSTRY_TERM")+" where ENT_ID=? and BUSI_ORDER_ID='"+this.getBusiOrderId()+"' and CONTENT=?", new Object[]{this.getEntId(),recorSocre.getString("CONTENT")});
				if (count > 0) {
					logger.info(CommonUtil.getClassNameAndMethod(this) + " 行业名词[" + recorSocre.getString("CONTENT") + "]重复，跳过此条记录");
					return EasyResult.ok("", getI18nValue("行业名词")+"[" + recorSocre.getString("CONTENT") + "]"+getI18nValue("重复，修改后重试！"));
				}
				
				query.save(recorSocre);
			}
			 try {
					IService service = ServiceContext.getService(ServiceID.KM_INFO_INTERFACE);
					JSONObject param = new JSONObject();
					param.put("command", ServiceCommand.KM_INFO_NOUN);
					param.put("state", "noun");
					param.put("ENT_ID", user.getEpCode());
					param.put("BUSI_ORDER_ID", user.getBusiOrderId());
					param.put("countParam",countParam);
					param.put("CONTENT", array);
					JSONObject result = service.invoke(param);
					if (!GWConstants.RET_CODE_SUCCESS.equals(result.getString("respCode"))) {
						logger.error(CommonUtil.getClassNameAndMethod(this) + " 同步导入名词失败," + result);
						return EasyResult.error(999, "同步导入名词失败");
					}
				} catch (ServiceException e) {
					logger.error(CommonUtil.getClassNameAndMethod(this) + " 同步导入名词失败," + e.getMessage(), e);
				}
			 LogUtil.insertLog(this.getDbName(), new Yqlogger(user, Constants.APP_NAME, Yqlogger.OPER_TYPE_IMPORT, "导入行业名词"));
			return EasyResult.ok("", getI18nValue("行业名词导入成功！"));
		} catch (Exception e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + "回滚异常:" + e1.getMessage(), e1);
			}
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 行业名词导入异常:" + e.getMessage(), e);
			return EasyResult.ok("", getI18nValue("行业名词导入失败！"));
		}
	}
	
	/**
	 * 全量同步
	 */
	public EasyResult actionForNounSynAllInfo(){
		UserModel user = UserUtil.getUser(this.getRequest());
		EasySQL sql = new EasySQL("SELECT ");
		sql.append("CONTENT from "+getTableName("KM_INDUSTRY_TERM") + " ");
		JSONArray array=new JSONArray();
		String countParam="";
		try {
			List<EasyRow> list=this.getQuery().queryForList(sql.getSQL(),new Object[]{});
			if (list != null &&list.size()>=0) {
				for (EasyRow easyRow : list) {
					JSONObject obj=new JSONObject();
					obj.put("industryTerm",easyRow.getColumnValue("CONTENT"));
					if("".equals(countParam)){
						countParam=easyRow.getColumnValue("CONTENT");
					}else{
						countParam+=";"+easyRow.getColumnValue("CONTENT");
					}
					array.add(obj);
				}
				 try {
						IService service = ServiceContext.getService(ServiceID.KM_INFO_INTERFACE);
						JSONObject param = new JSONObject();
						param.put("command", ServiceCommand.KM_INFO_NOUN);
						param.put("state", "noun");
						param.put("ENT_ID", user.getEpCode());
						param.put("BUSI_ORDER_ID", user.getBusiOrderId());
						param.put("countParam", countParam);
						param.put("CONTENT", array);
						JSONObject result = service.invoke(param);
						if (!GWConstants.RET_CODE_SUCCESS.equals(result.getString("respCode"))) {
							logger.error(CommonUtil.getClassNameAndMethod(this) + " 全量同步导入名词失败," + result);
							return EasyResult.error(999, "全量同步导入名词失败");
						}
					} catch (ServiceException e) {
						logger.error(CommonUtil.getClassNameAndMethod(this) + " 全量同步导入名词失败," + e.getMessage(), e);
					}
			}
		} catch (SQLException e) {
			e.printStackTrace();
			return EasyResult.error(e.getErrorCode(), getI18nValue("同步失败,系统异常"));
		}
		return EasyResult.ok(null, getI18nValue("同步成功"));
	}
	
	public void actionForExportDayCallStat() throws Exception {
		HttpServletRequest request = this.getRequest();
		EasyQuery query = this.getQuery();
		query.setMaxRow(50000);
		EasySQL sql = new EasySQL("SELECT ");
		sql.append(" ID,CONTENT,CREATE_ACC,CREATE_NAME,CREATE_DEPT,CREATE_DEPT_NAME,CREATE_TIME from " + getTableName("KM_INDUSTRY_TERM") + "  where 1=1 ");
		sql.appendLike(request.getParameter("CONTENT"), " and CONTENT like ? ");
		sql.append(request.getParameter("FAQ_ROBOT_ID"), " and FAQ_ROBOT_ID = ? ");
		sql.append(this.getEntId(), " and ENT_ID = ? ");
		String start = request.getParameter("startDate");
		String startDate = "";
		String endDate = "";
		String depCode = UserUtil.getUser(request).getEpCode();
		Map<String, Object> mkmFaq = DictCache.getNCMapByGroupCode(depCode, "KM_FAQ_ROBOT_ID");
		if (!"".equals(start)) {
			startDate = start.substring(0, 10).trim();
			endDate = start.substring(13, 23).trim();
		}
		if (StringUtils.isNotBlank(startDate)) {
			sql.append(startDate, " AND CREATE_TIME >= ? ");
		}
		if (StringUtils.isNotBlank(endDate)) {
			sql.append(endDate, " AND CREATE_TIME <= ? ");
		}
		sql.append(this.getBusiOrderId(), " and BUSI_ORDER_ID = ? ");
		sql.append(" ORDER BY CREATE_TIME DESC");
		List<Map<String, String>> data = query.queryForList(sql.getSQL(), sql.getParams(), new MapRowMapperImpl());

		List<String> headers = new ArrayList<String>();
		headers.add(getI18nValue("行业名词"));
		headers.add(getI18nValue("创建时间"));
		headers.add(getI18nValue("创建人账号"));
		headers.add(getI18nValue("创建人名称"));

		List<ExcelHeaderStyle> styles = new ArrayList<ExcelHeaderStyle>();
		for (String header : headers) {
			ExcelHeaderStyle style = new ExcelHeaderStyle();
			style.setData(header);
			style.setWidth(3600);
			style.setBackgroundColor(IndexedColors.GREY_25_PERCENT.index);
			styles.add(style);
		}

		List<List<String>> excelData = new ArrayList<List<String>>();
		if (data != null && data.size() > 0) {
			for (Map<String, String> map : data) {
				List<String> list = new ArrayList<String>();
				list.add(parseParam(map.get("CONTENT"), ""));
				list.add(parseParam(map.get("CREATE_TIME"), ""));
				list.add(parseParam(map.get("CREATE_ACC"), ""));
				list.add(parseParam(map.get("CREATE_NAME"), ""));
				excelData.add(list);
			}
		}
		File file = FileKit.createTempFile(RandomKit.randomStr() + ".xlsx");
		ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
		renderFile(file, getI18nValue("行业名词") + EasyDate.getCurrentDateString() + ".xlsx");
		UserModel user = UserUtil.getUser(getRequest());
		LogUtil.insertLog(this.getDbName(), new Yqlogger(user, Constants.APP_NAME, Yqlogger.OPER_TYPE_EXPORT, "导出行业名词"));
	}

	private String parseParam(String val, String defVal) {
		if (StringUtils.isBlank(val)) {
			return defVal;
		}
		return val;
	}
 
	//查无结果优化导出
	@InfAuthCheck(resId = "cc-km-analyze-query-stat", msg = "没有权限")
	public void actionForExportExcel() throws Exception {
		HttpServletRequest request = this.getRequest();
		EasyQuery query = this.getQuery();
		query.setMaxRow(50000);
		EasySQL sql= new EasySQL("SELECT KEY_WORD,DIR_NAME,COUNT(*) QUERY_COUNT,MAX(QUERY_TIME) QUERY_TIME,STATUS,HANDLE_ACC,HANDLE_ACC_NAME,HANDLE_TIME FROM "+getTableName("km_no_info_record")+" where 1=1");
		sql.appendLike(request.getParameter("keyWord")," and KEY_WORD like ?");
		sql.append(request.getParameter("createTime")," and QUERY_TIME >= ?");
		sql.append(request.getParameter("createToTime")," and QUERY_TIME <= ?");
		sql.append(getEntId()," and ENT_ID=?");
		sql.append(request.getParameter("handleStatus")," and STATUS=?");
		sql.append(" GROUP BY KEY_WORD,DIR_NAME,STATUS,HANDLE_ACC,HANDLE_ACC_NAME,HANDLE_TIME HAVING 1=1");
		sql.append(request.getParameter("bQueryNum")," and COUNT(*) >= ?");
		sql.append(request.getParameter("eQueryNum")," and COUNT(*) <= ?");
		sql.append(" ORDER BY QUERY_TIME DESC");
		
		List<Map<String, String>> data = query.queryForList(sql.getSQL(), sql.getParams(), new MapRowMapperImpl());

		List<String> headers = new ArrayList<String>();
		headers.add(getI18nValue("查询关键词"));
		headers.add(getI18nValue("查询目录"));
		headers.add(getI18nValue("查询次数"));
		headers.add(getI18nValue("最近查询时间"));
		headers.add(getI18nValue("处理状态"));
		headers.add(getI18nValue("处理人账号"));
		headers.add(getI18nValue("处理人姓名"));
		headers.add(getI18nValue("处理时间"));

		List<ExcelHeaderStyle> styles = new ArrayList<ExcelHeaderStyle>();
		for (String header : headers) {
			ExcelHeaderStyle style = new ExcelHeaderStyle();
			style.setData(header);
			style.setWidth(3600);
			style.setBackgroundColor(IndexedColors.GREY_25_PERCENT.index);
			styles.add(style);
		}

		List<List<String>> excelData = new ArrayList<List<String>>();
		if (data != null && data.size() > 0) {
			for (Map<String, String> map : data) {
				List<String> list = new ArrayList<String>();
				list.add(parseParam(map.get("KEY_WORD"), ""));
				list.add(parseParam(map.get("DIR_NAME"), ""));
				list.add(parseParam(map.get("QUERY_COUNT"), ""));
				list.add(parseParam(map.get("QUERY_TIME"), ""));
				
				list.add(getI18nValue(DictCache.getDictVal(this.getEntId(), "KM_HANDLE_STATUS", map.get("STATUS"))));
				
				list.add(parseParam(map.get("HANDLE_ACC"), ""));
				list.add(parseParam(map.get("HANDLE_ACC_NAME"), ""));
				list.add(parseParam(map.get("HANDLE_TIME"), ""));
				excelData.add(list);
			}
		}
		File file = FileKit.createTempFile(RandomKit.randomStr() + ".xlsx");
		ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
		renderFile(file, getI18nValue("查无结果优化") + EasyDate.getCurrentDateString() + ".xlsx");
		
		UserModel user = UserUtil.getUser(getRequest());
		LogUtil.insertLog(this.getDbName(), new Yqlogger(user, Constants.APP_NAME, Yqlogger.OPER_TYPE_EXPORT, "导出查无结果"));
	}
	
	/**
	 * 行业名词批量保存
	 * @return
	 */
	public EasyResult actionForSyncUser(){
		UserModel user = UserUtil.getUser(this.getRequest());
		JSONObject param = this.getJSONObject();
		JSONArray json = param.getJSONArray("json");
		String entId = this.getEntId();
		ArrayList<Object[]> list = new ArrayList<>();
		String countParam="";
		JSONArray array=new JSONArray();
		for(int i = 0; i < json.size(); i++){
			JSONObject js = (JSONObject)json.get(i);
			Date day = new Date();
			SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
			String date = df.format(day);
			String userName = (String)js.get("KEY_WORD");
			String longUser = UserUtil.getUser(getRequest()).getUserAcc();// 获取登陆用户//获取登陆用户
			String longName = UserUtil.getUser(getRequest()).getUserName();// 获取登陆用户名称
			String deptCodeName = UserUtil.getUser(getRequest()).getDept().getDeptName();
			String deptCode = UserUtil.getUser(getRequest()).getDept().getDeptCode();
			JSONObject json1=new JSONObject();
			json1.put("industryTerm",userName);
			if("".equals(countParam)){
				countParam=userName;
			}else{
				countParam+=";"+userName;
			}
			array.add(json1);
			list.add(new Object[]{RandomKit.randomStr(),userName,longUser,longName,deptCode,deptCodeName,date,entId,this.getBusiOrderId()});
		}
		EasyQuery query = this.getQuery();
			try {
				for(int i = 0; i < json.size(); i++){
					JSONObject js2 = (JSONObject)json.get(i);
					String userName2 = (String)js2.get("KEY_WORD");
					
					boolean isExists = query.queryForExist("select count(1) from "+getTableName("KM_INDUSTRY_TERM")+" where ENT_ID=? and BUSI_ORDER_ID='"+this.getBusiOrderId()+"' and CONTENT=?", new Object[]{this.getEntId(),userName2});
					if (isExists) {
						return EasyResult.error(1, getI18nValue("行业名词")+userName2+getI18nValue("已存在"));
					}else {
						query.execute("insert into "+getTableName("KM_INDUSTRY_TERM")+" (ID,CONTENT,CREATE_ACC,CREATE_NAME,CREATE_DEPT,CREATE_DEPT_NAME,CREATE_TIME,ENT_ID,BUSI_ORDER_ID) values(?,?,?,?,?,?,?,?,?)", list.get(i));
						//在km_no_info_record表中添加描述
						EasySQL sql = new EasySQL(" UPDATE " + getTableName("km_no_info_record"));
						sql.append(" SET STATUS='1', ");
						String desc2=" /添加到行业名词";
						sql.append(desc2," HANDLE_DESC=concat((CASE WHEN HANDLE_DESC IS NOT NULL THEN HANDLE_DESC ELSE '' END),?)");
						sql.append(UserUtil.getRequestUserAcc(getRequest()), ", HANDLE_ACC=? ");// 获取当前用户账户
						sql.append(this.getUserName(), " ,HANDLE_ACC_NAME=? ");// 获取当前用户姓名
						sql.append(DateUtil.getCurrentDateStr(), " ,HANDLE_TIME=? ");// 获取当前时间
						sql.append(" where 1=1 ");
						sql.append(userName2, " and KEY_WORD=?");
						query.execute(sql.getSQL(), sql.getParams());
					}
				}
			try {
				IService service = ServiceContext.getService(ServiceID.KM_INFO_INTERFACE);
				JSONObject param1 = new JSONObject();
				param1.put("command", ServiceCommand.KM_INFO_NOUN);
				param1.put("state", "noun");
				param1.put("ENT_ID", user.getEpCode());
				param1.put("BUSI_ORDER_ID", user.getBusiOrderId());
				param1.put("countParam", countParam);
				param1.put("CONTENT", array);
				JSONObject result = service.invoke(param1);
				if (!GWConstants.RET_CODE_SUCCESS.equals(result.getString("respCode"))) {
					logger.error(CommonUtil.getClassNameAndMethod(this) + " 全量同步导入名词失败," + result);
					return EasyResult.error(999, "全量同步导入名词失败");
				}
			} catch (ServiceException e) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + " 全量同步导入名词失败," + e.getMessage(), e);
			}
		} catch (Exception e) {
			try {
				query.roolback();
				logger.error(e.getMessage(), e);
			} catch (SQLException e1) {
				logger.error(e1.getMessage(), e1);
				this.error(e1.getMessage(), e1);
			}
			this.error(e.getMessage(), e);
			return EasyResult.fail("批量添加行业名词失败，失败原因："+e.getMessage());
		}
		
		return EasyResult.ok("",getI18nValue("操作成功!"));
	}
	
	
	/**
	 * 同步行业名词
	 * 
	 * @return
	 */
	public EasyResult actionForSynHyWord() {
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 开始全量同步行业名词:entId = " + getEntId() + ", busiOrderId = " + getBusiOrderId());
		EasyQuery query = this.getQuery();
		try {
			
			//获取所有知识点标题
			EasySQL infoSql= new EasySQL();
			infoSql.append("SELECT INFO_TOPIC FROM "+getTableName("KM_INFO") +"  WHERE 1=1 ");
			infoSql.append(getEntId()," AND ENT_ID = ? ");
			infoSql.append(getBusiOrderId()," AND BUSI_ORDER_ID = ? ");
			List<EasyRow> infoList = query.queryForList(infoSql.getSQL(), infoSql.getParams());
			
			//获取所有知识项标题
			EasySQL itemSql= new EasySQL();
			itemSql.append("SELECT TITLE FROM "+getTableName("KM_INFO_ITEM") +"  WHERE 1=1 ");
			itemSql.append(getEntId()," AND ENT_ID = ? ");
			itemSql.append(getBusiOrderId()," AND BUSI_ORDER_ID = ? ");
			List<EasyRow> itemList = query.queryForList(itemSql.getSQL(),itemSql.getParams());
			
			//查询现有行业名词
			EasySQL sql= new EasySQL();
			sql.append("SELECT CONTENT FROM "+getTableName("KM_INDUSTRY_TERM") +"  WHERE 1=1 ");
			sql.append(getEntId()," AND ENT_ID = ? ");
			sql.append(getBusiOrderId()," AND BUSI_ORDER_ID = ? ");
			List<EasyRow> hyList = query.queryForList(sql.getSQL(), sql.getParams());
			
			//数据库已经存在的行业名词SET集合
			Set<String> hySets = new HashSet<String>();
			
			if (hyList != null &&hyList.size()>=0) {
				for (EasyRow easyRow : hyList) {
					String content = easyRow.getColumnValue("CONTENT");
					if(StringUtils.isNotBlank(content)){
						hySets.add(content);
					}
				}
			}
			
			//知识点、知识项SET集合
			Set<String> addHySets = new HashSet<String>();
			
			if (infoList != null &&infoList.size()>=0) {
				for (EasyRow easyRow : infoList) {
					String infoTopic = easyRow.getColumnValue("INFO_TOPIC");
					if(StringUtils.isNotBlank(infoTopic)){
						if(!hySets.contains(infoTopic)){
							addHySets.add(infoTopic);
						}
					}
				}
			}
			
			if (itemList != null &&itemList.size()>=0) {
				for (EasyRow easyRow : itemList) {
					String title = easyRow.getColumnValue("TITLE");
					if(StringUtils.isNotBlank(title)){
						if(!hySets.contains(title)){
							addHySets.add(title);
						}
					}
				}
			}
			
			//插入数据
			ArrayList<Object[]> list = new ArrayList<>();
			if(addHySets!=null&&addHySets.size()>0){
				SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");// 设置日期格式
				//String faqRobotId = "";
				int idxOrder = 1 ;
				String createAcc = UserUtil.getUser(getRequest()).getUserAcc();
				String createName = getUserName();
				String createDept = UserUtil.getUser(getRequest()).getDeptCode();
				String createDeptName = UserUtil.getUser(getRequest()).getDeptName();
				String createTime = df.format(new Date());
				String entId = getEntId();
				String busiOrderId = getBusiOrderId();
				for(String hyWord: addHySets) {
					String id = RandomKit.randomStr();
					String content = hyWord;
					list.add(new Object[]{id,content,idxOrder,createAcc,createName,createDept,createDeptName,createTime,entId,busiOrderId});
				}
			}
			
			if(list!=null&&list.size()>0){
				query.executeBatch("insert into "+getTableName("KM_INDUSTRY_TERM")+" (ID,CONTENT,IDX_ORDER,CREATE_ACC,CREATE_NAME,CREATE_DEPT,CREATE_DEPT_NAME,CREATE_TIME,ENT_ID,BUSI_ORDER_ID) values(?,?,?,?,?,?,?,?,?,?)", list);
			}
			
		} catch (Exception e) {
			logger.info(CommonUtil.getClassNameAndMethod(this) + " 全量同步行业名词出现异常:" + e.getMessage(), e);
			return EasyResult.error();
		}
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 结束全量同步行业名词:entId = " + getEntId() + ", busiOrderId = " + getBusiOrderId());
		return EasyResult.ok("",getI18nValue("操作成功!"));
	}
	
	public String actionForTurnItemHistoryList() {
		
		DBTypes types = this.getQuery().getTypes();
		
		String id = this.getRequest().getParameter("id");
		//知识项历史版本保留数量
		String historyVerCount = AppContext.getContext(Constants.APP_NAME).getProperty("historyVerCount", "");
		int historyVerNum = 5;
		if(StringUtils.isNotBlank(historyVerCount)){
			historyVerNum = Integer.parseInt(historyVerCount);
		}
		List<JSONObject> itemVersionList = null;
		EasyQuery query = this.getQuery();
		try {
			List<JSONObject> channelList = this.getQuery().queryForList("SELECT CHANNEL_KEY,NAME FROM " + getTableName("KM_CHANNEL") +  " WHERE STATUS = '01' AND ENT_ID = ? AND BUSI_ORDER_ID = ?", new Object[]{getEntId(),getBusiOrderId()},new JSONMapperImpl());
			this.setAttr("channelList", channelList);
			query.setMaxRow(historyVerNum);
			
			//获取知识项历史版本数量
			EasySQL itemSql = new EasySQL();
			itemSql.append("select * from " + getTableName("KM_INFO_ITEM_VER")+" t1 ");
			itemSql.append(" where 1=1 ");
			itemSql.append(id," and t1.OLD_ITEM_ID =? ");
			itemSql.append(getEntId()," and t1.ent_id = ? ");
			itemSql.append(getBusiOrderId()," and t1.busi_order_id = ? ");
			itemSql.append(" order by t1.ITEM_VERSION desc ");
			itemVersionList = query.queryForList(itemSql.getSQL(), itemSql.getParams(),new JSONMapperImpl());
			
			query.setMaxRow(3000);
			//查询所有的知识内容（根据知识内容历史版本ID）
			EasySQL sqlVer = new EasySQL();
			sqlVer.append("select * from " + getTableName("KM_INFO_ITEM_CONTENT_VER")+" t1 ");
			sqlVer.append(" where 1=1 ");
			sqlVer.append(id," and t1.INFO_ITEM_ID =? ");
			List<JSONObject> contentVerList = query.queryForList(sqlVer.getSQL(), sqlVer.getParams(),new JSONMapperImpl());
			
			//查询所有的知识渠道（根据知识内容历史版本ID）
			EasySQL sqlChannelVer = new EasySQL();
			sqlChannelVer.append("select * from " + getTableName("KM_INFO_ITEM_CONTENT_REF_VER")+" t1 ");
			sqlChannelVer.append(" where 1=1 ");
			sqlChannelVer.append(id," and t1.INFO_ITEM_ID =? ");
			List<JSONObject> channelVerList = query.queryForList(sqlChannelVer.getSQL(), sqlChannelVer.getParams(),new JSONMapperImpl());
			
			
			if(itemVersionList!=null&&itemVersionList.size()>0){
				for (JSONObject jo : itemVersionList) {
					String itemVerId = jo.getString("ID");
					EasySQL sql = new EasySQL();
					sql.append("select * from " + getTableName("KM_INFO_IC_VER")+" t1 ");
					sql.append(" left join " + getTableName("KM_INFO_ITEM_CONTENT_VER")+" t2 on t1.ITEM_VER_ID = t2.INFO_ITEM_VER_ID  and t1.ITEM_CONTENT_VER_ID = t2.id ");
					
					String groupConcat = "GROUP_CONCAT";
					if(types==DBTypes.ORACLE){
						groupConcat = "WM_CONCAT";
					}else{
						groupConcat = "GROUP_CONCAT";
					}
					sql.append(" left join (select "+groupConcat+"(ref_id) ref_id,info_item_id, info_item_content_id,"
							+ " INFO_ITEM_CONTENT_VER_ID,INFO_ITEM_VER_ID from " + getTableName("KM_INFO_ITEM_CONTENT_REF_VER")
							+ " group by info_item_id,info_item_content_id,INFO_ITEM_CONTENT_VER_ID,INFO_ITEM_VER_ID) t3 "
							+ " on t2.id = t3.INFO_ITEM_CONTENT_VER_ID and t3.INFO_ITEM_VER_ID = t2.INFO_ITEM_VER_ID ");
					
					//sql.append(" left join " + getTableName("KM_INFO_ITEM_CONTENT_REF_VER")+" t3 on t2.id = t3.INFO_ITEM_CONTENT_VER_ID and t3.INFO_ITEM_VER_ID = t2.INFO_ITEM_VER_ID ");
					sql.append(" where 1=1 ");
					sql.append(id," and t1.ITEM_ID =? ");
					sql.append(itemVerId," and t1.ITEM_VER_ID =? ");
					sql.append(" order by t2.CREATE_TIME  ");
					List<JSONObject> contentVersionList = query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
					
					List<JSONObject> newContentVersionList = new ArrayList<JSONObject>();
					
					//关联上个版本的知识内容
					if(contentVersionList!=null&&contentVersionList.size()>0){
						for (JSONObject contentVer : contentVersionList) {
							String contentVerId = contentVer.getString("ID");
							String itemContentVerId = contentVer.getString("ITEM_CONTENT_VER_ID");
							if(StringUtils.isBlank(contentVerId)){
								for (JSONObject jsonObject : contentVerList) {
									if(itemContentVerId.equals(jsonObject.getString("ID"))){
										contentVer =  jsonObject;
										contentVer.put("IS_MAIN", jsonObject.getString("IS_MAIN"));
										if(channelVerList!=null&&channelVerList.size()>0){
											for (JSONObject joChannel : channelVerList) {
												if(itemContentVerId.equals(joChannel.getString("INFO_ITEM_CONTENT_VER_ID"))){
													contentVer.put("REF_ID", joChannel.getString("REF_ID"));
													//newContentVersionList.add(contentVer);
													break;
												}
											}
										}
										newContentVersionList.add(contentVer);
										break;
									}
								}
							}else{
								newContentVersionList.add(contentVer);
							}
						}
					}
					jo.put("itemList", newContentVersionList);
				}
				this.setAttr("itemList", itemVersionList);
			}
		} catch (Exception e) {
			logger.info(CommonUtil.getClassNameAndMethod(this) + " 获取知识项历史版本出现异常:"
					+ e.getMessage(), e);
		}finally {
			query.setMaxRow(3000);
		}
		return "/pages/km/info/history/km-info-item-history-list.jsp";
	}
	
	public String actionForTurnItemContentHistoryList() {
		DBTypes types = this.getQuery().getTypes();
		String contentId = this.getRequest().getParameter("contentId");
		//知识项历史版本保留数量
		String historyVerCount = AppContext.getContext(Constants.APP_NAME).getProperty("historyVerCount", "");
		int historyVerNum = 5;
		if(StringUtils.isNotBlank(historyVerCount)){
			historyVerNum = Integer.parseInt(historyVerCount);
		}
		EasyQuery query = this.getQuery();
		try {
			List<JSONObject> channelList = this.getQuery().queryForList("SELECT CHANNEL_KEY,NAME FROM " + getTableName("KM_CHANNEL") +  " WHERE STATUS = '01' AND ENT_ID = ? AND BUSI_ORDER_ID = ?", new Object[]{getEntId(),getBusiOrderId()},new JSONMapperImpl());
			this.setAttr("channelList", channelList);
			query.setMaxRow(historyVerNum);
			EasySQL sql = new EasySQL();
			sql.append("select * from " + getTableName("KM_INFO_ITEM_CONTENT_VER")+" t1 ");
			
			String groupConcat = "GROUP_CONCAT";
			if(types==DBTypes.ORACLE){
				groupConcat = "WM_CONCAT";
			}else{
				groupConcat = "GROUP_CONCAT";
			}
			sql.append(" left join (select "+groupConcat+"(ref_id) ref_id,info_item_id, info_item_content_id,"
					+ " INFO_ITEM_CONTENT_VER_ID,INFO_ITEM_VER_ID from " + getTableName("KM_INFO_ITEM_CONTENT_REF_VER")
					+ " group by info_item_id,info_item_content_id,INFO_ITEM_CONTENT_VER_ID,INFO_ITEM_VER_ID) t2 "
					+ " on t1.id = t2.INFO_ITEM_CONTENT_VER_ID and t2.INFO_ITEM_VER_ID = t1.INFO_ITEM_VER_ID ");
			
			
			//sql.append(" left join " + getTableName("KM_INFO_ITEM_CONTENT_REF_VER")+" t2 on t1.id = t2.INFO_ITEM_CONTENT_VER_ID and t2.INFO_ITEM_VER_ID = t1.INFO_ITEM_VER_ID ");
			sql.append(" where 1=1 ");
			sql.append(contentId," and t1.INFO_ITEM_CONTENT_ID =? ");
			sql.append(" order by t1.ITEM_VERSION desc ");
			
			List<JSONObject> contentList = query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			this.setAttr("contentList", contentList);
		} catch (Exception e) {
			logger.info(CommonUtil.getClassNameAndMethod(this) + " 获取知识内容历史版本出现异常:"
					+ e.getMessage(), e);
		}finally {
			query.setMaxRow(3000);
		}
		return "/pages/km/info/history/km-info-item-content-history-list.jsp";
	}
	
	/**
	 * 生成历史版本
	 * @throws SQLException 
	 */
	private static ArrayList<Object> getHistoryVer(EasyQuery query,String schema,String entId,String busiOrderId,String itemId,String infoId, String contentIdAdd,String contentIdUpd,String contentIdDel,DBTypes types,JSONObject json,String bakup,UserModel user) throws SQLException {
		//历史版本begin
		
		String oldItemVersion = "";
		boolean isUpdateItem = false;
		ArrayList<Object> resultList = new ArrayList<Object>();
		
		//知识项历史版本保留数量
		String historyVerCount = AppContext.getContext(Constants.APP_NAME).getProperty("historyVerCount", "");
		int historyVerNum = 5;
		if(StringUtils.isNotBlank(historyVerCount)){
			historyVerNum = Integer.parseInt(historyVerCount);
		}
		
		//根据历史版本数量 判断是否需要删除过旧 历史版本 historyVerNum
		List<EasyRow> itemVersionList = query.queryForList("SELECT ITEM_VERSION,ID FROM " + schema+(".KM_INFO_ITEM_VER") + " WHERE OLD_ITEM_ID = ? AND INFO_ID = ? ORDER BY ITEM_VERSION ", new Object[] { itemId,infoId });
		if(itemVersionList!=null&&itemVersionList.size()>historyVerNum){
			//需要删除过旧版本
			int size = itemVersionList.size()+1;
			int sub = size-historyVerNum;
			StringBuffer itemVersionStr = new StringBuffer("");
			for (int i = 0; i < sub; i++) {
				EasyRow row = itemVersionList.get(i);
				String itemIdVer = row.getColumnValue("ID");
				//删除知识项内容过旧版本
				query.execute("DELETE FROM " + schema+(".KM_INFO_ITEM_CONTENT_VER") + " WHERE INFO_ITEM_VER_ID = ? ", new Object[] { itemIdVer});
				
				//删除关联过旧版本
				query.execute("DELETE FROM " + schema+(".KM_INFO_ITEM_CONTENT_REF_VER") + " WHERE INFO_ITEM_VER_ID = ? ", new Object[] { itemIdVer});
				
				//删除关联关系过旧版本
				query.execute("DELETE FROM " + schema+(".KM_INFO_IC_VER") + " WHERE ITEM_VER_ID = ? ", new Object[] { itemIdVer});
				
				itemVersionStr.append(row.getColumnValue("ITEM_VERSION")).append(",");
			}
			String delItem = itemVersionStr.substring(0, itemVersionStr.length()-1);
			//删除知识项过旧版本
			query.execute("DELETE FROM " + schema+(".KM_INFO_ITEM_VER") + " WHERE OLD_ITEM_ID = ? AND INFO_ID = ? AND ITEM_VERSION IN (" + delItem+")", new Object[] { itemId, infoId});
		}
		
		//知识项历史版本
		//新增知识项不生成历史版本，修改知识项生成历史版本（内容无变化不生成历史版本）
		if(StringUtils.isNotBlank(itemId)) {
			
			//目前数据库数据提取(知识项)
			EasySQL oldItemSql = new EasySQL("");
			oldItemSql.append(itemId,"SELECT ID,TITLE,INFO_ID,NEED_UPDATE,TEMPLATE_ITEM_ID,IDX_ORDER,CLICK_COUNT,USE_COUNT,UNUSE_COUNT,ITEM_VERSION FROM "+schema+(".KM_INFO_ITEM")+" WHERE ID = ? ");
			EasyRow oldItem = query.queryForRow(oldItemSql.getSQL(), oldItemSql.getParams());
			//目前数据库数据提取(内容项)
			EasySQL oldContentSql = new EasySQL("");
			
			oldContentSql.append(itemId,"SELECT ID,INFO_ITEM_ID,CONTENT_TYPE,CONTENT,IS_MAIN,NEED_UPDATE,CREATE_ACC,CREATE_NAME,CREATE_DEPT,CREATE_DEPT_NAME,CREATE_TIME,STATUS,UPDATE_ACC,UPDATE_TIME FROM "+schema+(".KM_INFO_ITEM_CONTENT")+" WHERE INFO_ITEM_ID = ? ");
			List<EasyRow> oldContentList = query.queryForList(oldContentSql.getSQL(), oldContentSql.getParams());
			
			List<JSONObject> oldChannelList = query.queryForList("SELECT REF_ID,INFO_ITEM_CONTENT_ID FROM "+schema+(".KM_INFO_ITEM_CONTENT_REF")+" WHERE REF_TYPE = ? AND INFO_ITEM_ID = ? ", new Object[]{"1", itemId}, new JSONMapperImpl());
			Map<String, String> oldChannelMap = new HashMap<String,String>();
			if(oldChannelList!=null&&oldChannelList.size()>0){
				for (JSONObject channel : oldChannelList) {
					String itemContentId = channel.getString("INFO_ITEM_CONTENT_ID");
					if(StringUtils.isBlank(oldChannelMap.get("itemContentId"))) {
						oldChannelMap.put(itemContentId, channel.getString("REF_ID"));
					} else {

						oldChannelMap.put(itemContentId, oldChannelMap.get("itemContentId") + "," + channel.getString("REF_ID"));
					}
				}
			}
			
			List<JSONObject> oldChannelVerList = query.queryForList("SELECT REF_ID,INFO_ITEM_CONTENT_VER_ID FROM "+schema+(".KM_INFO_ITEM_CONTENT_REF_VER")+" WHERE REF_TYPE = ? AND INFO_ITEM_ID = ? ", new Object[]{"1", itemId}, new JSONMapperImpl());
			Map<String, String> oldChannelVerMap = new HashMap<String,String>();
			if(oldChannelVerList!=null&&oldChannelVerList.size()>0){
				for (JSONObject channel : oldChannelVerList) {
					String itemContentVerId = channel.getString("INFO_ITEM_CONTENT_VER_ID");
					if(StringUtils.isBlank(oldChannelVerMap.get(itemContentVerId))) {
						oldChannelVerMap.put(itemContentVerId, channel.getString("REF_ID"));
					} else {
						oldChannelVerMap.put(itemContentVerId, oldChannelVerMap.get(itemContentVerId) + "," + channel.getString("REF_ID"));
					}
				}
			}
			
			if(oldItem!=null){
				String oldTitle = oldItem.getColumnValue("TITLE");
				String oldId = oldItem.getColumnValue("ID");
				String oldInfoId = oldItem.getColumnValue("INFO_ID");
				//String oldNeedUpdate = oldItem.getColumnValue("NEED_UPDATE");
				String oldTemplateItemId = oldItem.getColumnValue("TEMPLATE_ITEM_ID");
				String oldIdxOrder = oldItem.getColumnValue("IDX_ORDER");
				String oldClickCount = oldItem.getColumnValue("CLICK_COUNT");
				String oldUseCount = oldItem.getColumnValue("USE_COUNT");
				String oldUnuseCount = oldItem.getColumnValue("UNUSE_COUNT");
				oldItemVersion = oldItem.getColumnValue("ITEM_VERSION");
				JSONArray contentList = json.getJSONArray("contentList");
				ArrayList<String> updateList = new ArrayList<String>();
				Map<String, EasyRow> contentMap = new HashMap<String,EasyRow>();
				Map<String, EasyRow> updateMap = new HashMap<String,EasyRow>();
				
				
				if(StringUtils.isNotBlank(contentIdUpd)){
					for (EasyRow row : oldContentList) {
						String oldContentId = row.getColumnValue("ID");
						String oldIsMain = row.getColumnValue("IS_MAIN");
						String oldContentType = row.getColumnValue("CONTENT_TYPE");
						String oldContent = row.getColumnValue("CONTENT");
						contentMap.put(oldContentId, row);
						boolean isUpdate = false;
						for(int i = 0; i < contentList.size(); i++ ) {
							JSONObject contentInfo = contentList.getJSONObject(i);
							String contentId = contentInfo.getString("CONTENT_ID");
							//contentMap.put(contentId, row);
							if(oldContentId.equals(contentId)){
								String isMain = contentInfo.getString("IS_MAIN");
								String contentType = contentInfo.getString("CONTENT_TYPE");
								String content = contentInfo.getString("CONTENT");
								String channelList = contentInfo.getString("channelList");
								if(!oldIsMain.equals(isMain)){
									isUpdate = true;
									break;
								}
								if(!oldContentType.equals(contentType)){
									isUpdate = true;
									break;
								}
								if(!oldContent.equals(content)){
									isUpdate = true;
									break;
								}
								String oldRefId = oldChannelMap.get(contentId);
								if(!channelList.equals(oldRefId)){
									isUpdate = true;
									break;
								}
							}
						}
						//true：知识项内容发生变化 flase:知识项内容没有变化
						if(isUpdate){
							updateList.add(oldContentId);
							updateMap.put(oldContentId, row);
						}
					}
				}
				//内容项无变化，不生成历史版本
				if(updateList.size()==0&&StringUtils.isBlank(contentIdAdd)&&StringUtils.isBlank(contentIdDel)){
					isUpdateItem = false;
				}else{
					
					if(StringUtils.isNotBlank(contentIdUpd)){
						//生成历史版本
						isUpdateItem = true;
						//新增知识项历史版本
						int itemVersionNum = Integer.parseInt(StringUtils.isNotBlank(oldItemVersion) ? oldItemVersion : "0");// 历史版本号码
						EasyRecord itemRecordVer = new EasyRecord(schema+(".KM_INFO_ITEM_VER"), "ID");
						String itemVerId = RandomKit.randomStr();
						itemRecordVer.put("ID",itemVerId);
						itemRecordVer.put("OLD_ITEM_ID", oldId);
						itemRecordVer.put("INFO_ID", oldInfoId);
						itemRecordVer.put("TITLE", oldTitle);
						//itemRecordVer.put("NEED_UPDATE", oldNeedUpdate);
						itemRecordVer.put("TEMPLATE_ITEM_ID", oldTemplateItemId);
						itemRecordVer.put("IDX_ORDER", StringUtils.isNotBlank(oldIdxOrder)?oldIdxOrder:0);
						itemRecordVer.put("CLICK_COUNT", StringUtils.isNotBlank(oldClickCount)?oldIdxOrder:0);
						itemRecordVer.put("USE_COUNT", StringUtils.isNotBlank(oldUseCount)?oldIdxOrder:0);
						itemRecordVer.put("UNUSE_COUNT", StringUtils.isNotBlank(oldUnuseCount)?oldIdxOrder:0);
						itemRecordVer.put("ENT_ID", entId);
						itemRecordVer.put("BUSI_ORDER_ID", busiOrderId);
						itemRecordVer.put("ITEM_VERSION", itemVersionNum);
						//修改说明、修改人、修改时间
						itemRecordVer.put("BAKUP", bakup);
						itemRecordVer.put("UPDATE_ACC", user.getUserAcc());
						itemRecordVer.put("UPDATE_TIME",DateUtil.getCurrentDateStr());
						
						query.save(itemRecordVer);
						
						//目前数据库数据提取(知识内容历史版本)
						
						ArrayList<String> sqlContentVerList = new ArrayList<String>();
						
						EasySQL itemContentVerSql2 = new EasySQL("");
						itemContentVerSql2.append("SELECT * FROM "+schema+(".KM_INFO_ITEM_CONTENT_VER")+" WHERE 1=1 ");
						itemContentVerSql2.append(itemId," AND INFO_ITEM_ID = ? ORDER BY ITEM_VERSION DESC ");
						List<EasyRow> itemContentVerList2 = query.queryForList(itemContentVerSql2.getSQL(), itemContentVerSql2.getParams());
						
						
						EasySQL itemContentVerSql = new EasySQL("");
						itemContentVerSql.append("SELECT INFO_ITEM_CONTENT_ID,MAX(ITEM_VERSION) ITEM_VERSION FROM "+schema+(".KM_INFO_ITEM_CONTENT_VER")+" WHERE 1=1 ");
						itemContentVerSql.append(itemId," AND INFO_ITEM_ID = ? GROUP BY INFO_ITEM_CONTENT_ID ");
						List<EasyRow> itemContentVerList = query.queryForList(itemContentVerSql.getSQL(), itemContentVerSql.getParams());
						
						if(itemContentVerList!=null&&itemContentVerList.size()>0){
							for (EasyRow easyRow : itemContentVerList) {
								String infoItemContentId = easyRow.getColumnValue("INFO_ITEM_CONTENT_ID");
								String countVersion = StringUtils.isNoneBlank(easyRow.getColumnValue("ITEM_VERSION"))?easyRow.getColumnValue("ITEM_VERSION"):"1";
								int countVersionNum = Integer.parseInt(countVersion);
								boolean equals = true;
								try {
									equals = infoItemContentId.equals(contentMap.get(infoItemContentId).getColumnValue("ID"));
								} catch (Exception e) {
									equals = false;
								}
								if(equals){
									boolean equals2 = true;
									try {
										equals2 = infoItemContentId.equals(updateMap.get(infoItemContentId).getColumnValue("ID"));
									} catch (Exception e) {
										equals2 = false;
									}
									if(equals2){
										//判断初始化历史版本的时候是否已经生成一条历史版本
										EasyRow row = updateMap.get(infoItemContentId);
										boolean isUpdate = false;
										
										EasySQL itemContentVerNewSql = new EasySQL("");
										itemContentVerNewSql.append("SELECT IS_MAIN,CONTENT_TYPE,CONTENT,ID FROM "+schema+(".KM_INFO_ITEM_CONTENT_VER")+" WHERE 1=1 ");
										itemContentVerNewSql.append(infoItemContentId," AND INFO_ITEM_CONTENT_ID = ? ");
										itemContentVerNewSql.append(itemId," and INFO_ITEM_ID = ? order by ITEM_VERSION DESC ");
										EasyRow itemContentVerNewRow = query.queryForRow(itemContentVerNewSql.getSQL(), itemContentVerNewSql.getParams());
										
										String isMain = itemContentVerNewRow.getColumnValue("IS_MAIN");
										String contentType = itemContentVerNewRow.getColumnValue("CONTENT_TYPE");
										String content = itemContentVerNewRow.getColumnValue("CONTENT");
										String newContentVerId = itemContentVerNewRow.getColumnValue("ID");
										if(!isMain.equals(row.getColumnValue("IS_MAIN"))){
											isUpdate = true;
										}
										if(!contentType.equals(row.getColumnValue("CONTENT_TYPE"))){
											isUpdate = true;
										}
										if(!content.equals(row.getColumnValue("CONTENT"))){
											isUpdate = true;
										}
										String oldRefVerId = oldChannelVerMap.get(newContentVerId);
										String oldRefId = oldChannelMap.get(infoItemContentId);
										if(!oldRefId.equals(oldRefVerId)){
											isUpdate = true;
										}
										if(isUpdate){
											//内容版本表增加
											String contentVerId = RandomKit.randomStr();
											/*String sql = "INSERT INTO "+schema+(".KM_INFO_ITEM_CONTENT_VER")+" VALUES("+contentVerId + ",'" + itemId + "','" +row.getColumnValue("CONTENT_TYPE")+"','"+row.getColumnValue("CONTENT")+"','"
													+row.getColumnValue("IS_MAIN")+"','"+row.getColumnValue("NEED_UPDATE")+"','"+row.getColumnValue("CREATE_ACC")+"','"+row.getColumnValue("CREATE_NAME")+"','"
													+row.getColumnValue("CREATE_DEPT")+"','"+row.getColumnValue("CREATE_DEPT_NAME")+"','"+row.getColumnValue("CREATE_TIME")+"','"+row.getColumnValue("STATUS")+"','"
													+row.getColumnValue("UPDATE_ACC")+"','"+row.getColumnValue("UPDATE_TIME")+"','"+(countVersionNum+1)+"','"+row.getColumnValue("ID")+"','"+itemVerId+"' "
													+")";
											sqlContentVerList.add(sql);*/
											//预编译写法
											query.execute("INSERT INTO "+schema+(".KM_INFO_ITEM_CONTENT_VER") 
													+" (ID,INFO_ITEM_ID,CONTENT_TYPE,CONTENT,IS_MAIN,NEED_UPDATE,CREATE_ACC,CREATE_NAME,CREATE_DEPT,CREATE_DEPT_NAME,CREATE_TIME,STATUS,UPDATE_ACC,UPDATE_TIME,ITEM_VERSION,INFO_ITEM_CONTENT_ID,INFO_ITEM_VER_ID) "
													+" VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
													 contentVerId,itemId,row.getColumnValue("CONTENT_TYPE"),row.getColumnValue("CONTENT"),row.getColumnValue("IS_MAIN"),row.getColumnValue("NEED_UPDATE")
													,row.getColumnValue("CREATE_ACC"),row.getColumnValue("CREATE_NAME"),row.getColumnValue("CREATE_DEPT"),row.getColumnValue("CREATE_DEPT_NAME"),row.getColumnValue("CREATE_TIME")
													,row.getColumnValue("STATUS"),row.getColumnValue("UPDATE_ACC"),row.getColumnValue("UPDATE_TIME"),(countVersionNum+1),row.getColumnValue("ID"),itemVerId);
											
											
											String refIds = oldChannelMap.get(row.getColumnValue("ID"));
											if(StringUtils.isNotBlank(refIds)){
												String[] refIdArr = refIds.split(",");
												for (String refId : refIdArr) {
													String sqlChannel = "INSERT INTO "+schema+(".KM_INFO_ITEM_CONTENT_REF_VER")
															+" (ID,INFO_ITEM_ID,INFO_ITEM_CONTENT_ID,REF_TYPE,REF_ID,INFO_ITEM_VER_ID,INFO_ITEM_CONTENT_VER_ID) "
															+" VALUES("+RandomKit.randomStr() + ",'" + itemId + "','" +row.getColumnValue("ID")+"','1','"+refId+ "','" + itemVerId + "','" +contentVerId+"')";
													sqlContentVerList.add(sqlChannel);
												}
											}
											//关联表需要增加
											String sqlIC = "INSERT INTO "+schema+(".KM_INFO_IC_VER")
													+" (ID,ITEM_VER_ID,ITEM_CONTENT_VER_ID,ITEM_ID,ITEM_CONTENT_ID,ADD_FLAG) "
													+" VALUES("+RandomKit.randomStr() + ",'" + itemVerId + "','" +contentVerId+"','"+itemId+"','"+row.getColumnValue("ID")+"','Y'"+")";
											sqlContentVerList.add(sqlIC);
										}else{
											//关联表需要增加
											String sqlIC = "INSERT INTO "+schema+(".KM_INFO_IC_VER")
													+" (ID,ITEM_VER_ID,ITEM_CONTENT_VER_ID,ITEM_ID,ITEM_CONTENT_ID,ADD_FLAG) "
													+" VALUES("+RandomKit.randomStr() + ",'" + itemVerId + "','" +newContentVerId+"','"+itemId+"','"+row.getColumnValue("ID")+"','N'"+")";
											sqlContentVerList.add(sqlIC);
										}
									}else{
										//关联表需要增加
										String contentVerId = "";
										for (EasyRow row : itemContentVerList2) {
											String itemVersion2 = row.getColumnValue("ITEM_VERSION");
											String infoItemContentId2 = row.getColumnValue("INFO_ITEM_CONTENT_ID");
											if(infoItemContentId.equals(infoItemContentId2)&&countVersion.equals(itemVersion2)){
												contentVerId = row.getColumnValue("ID");
												break;
											}
										}
										String sqlIC = "INSERT INTO "+schema+(".KM_INFO_IC_VER")
												+" (ID,ITEM_VER_ID,ITEM_CONTENT_VER_ID,ITEM_ID,ITEM_CONTENT_ID,ADD_FLAG) "
												+" VALUES("+RandomKit.randomStr() + ",'" + itemVerId + "','" +contentVerId+"','"+itemId+"','"+infoItemContentId+"','N'"+")";
										sqlContentVerList.add(sqlIC);
									}
								}else{
									//内容版本表和关联表都需要增加
									EasyRow row = contentMap.get(infoItemContentId);
									if(row!=null){
										String contentVerId = RandomKit.randomStr();
										/*String sql = "INSERT INTO "+schema+(".KM_INFO_ITEM_CONTENT_VER")+" VALUES("+contentVerId + ",'" + itemId + "','" +row.getColumnValue("CONTENT_TYPE")+"','"+row.getColumnValue("CONTENT")+"','"
												+row.getColumnValue("IS_MAIN")+"','"+row.getColumnValue("NEED_UPDATE")+"','"+row.getColumnValue("CREATE_ACC")+"','"+row.getColumnValue("CREATE_NAME")+"','"
												+row.getColumnValue("CREATE_DEPT")+"','"+row.getColumnValue("CREATE_DEPT_NAME")+"','"+row.getColumnValue("CREATE_TIME")+"','"+row.getColumnValue("STATUS")+"','"
												+row.getColumnValue("UPDATE_ACC")+"','"+row.getColumnValue("UPDATE_TIME")+"','"+1+"','"+row.getColumnValue("ID")+"','"+itemVerId+"' "
												+")";*/
										
										//预编译写法
										query.execute("INSERT INTO "+schema+(".KM_INFO_ITEM_CONTENT_VER")
												 +" (ID,INFO_ITEM_ID,CONTENT_TYPE,CONTENT,IS_MAIN,NEED_UPDATE,CREATE_ACC,CREATE_NAME,CREATE_DEPT,CREATE_DEPT_NAME,CREATE_TIME,STATUS,UPDATE_ACC,UPDATE_TIME,ITEM_VERSION,INFO_ITEM_CONTENT_ID,INFO_ITEM_VER_ID) "
												 +" VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
												 contentVerId,itemId,row.getColumnValue("CONTENT_TYPE"),row.getColumnValue("CONTENT"),row.getColumnValue("IS_MAIN"),row.getColumnValue("NEED_UPDATE")
												,row.getColumnValue("CREATE_ACC"),row.getColumnValue("CREATE_NAME"),row.getColumnValue("CREATE_DEPT"),row.getColumnValue("CREATE_DEPT_NAME"),row.getColumnValue("CREATE_TIME")
												,row.getColumnValue("STATUS"),row.getColumnValue("UPDATE_ACC"),row.getColumnValue("UPDATE_TIME"),"1",row.getColumnValue("ID"),itemVerId);
										
										String sqlIC = "INSERT INTO "+schema+(".KM_INFO_IC_VER") 
												+" (ID,ITEM_VER_ID,ITEM_CONTENT_VER_ID,ITEM_ID,ITEM_CONTENT_ID,ADD_FLAG) "
												+" VALUES("+RandomKit.randomStr() + ",'" + itemVerId + "','" +contentVerId+"','"+itemId+"','"+row.getColumnValue("ID")+"','Y'"+")";
										String refIds = oldChannelMap.get(row.getColumnValue("ID"));
										if(StringUtils.isNotBlank(refIds)){
											String[] refIdArr = refIds.split(",");
											for (String refId : refIdArr) {
												String sqlChannel = "INSERT INTO "+schema+(".KM_INFO_ITEM_CONTENT_REF_VER")
														+" (ID,INFO_ITEM_ID,INFO_ITEM_CONTENT_ID,REF_TYPE,REF_ID,INFO_ITEM_VER_ID,INFO_ITEM_CONTENT_VER_ID) "
														+" VALUES("+RandomKit.randomStr() + ",'" + itemId + "','" +row.getColumnValue("ID")+"','1','"+refId+ "','" + itemVerId + "','" +contentVerId+"')";
												sqlContentVerList.add(sqlChannel);
											}
										}
										//sqlContentVerList.add(sql);
										sqlContentVerList.add(sqlIC);
									}
								}
							}
						}else{
							for (EasyRow row : oldContentList) {
								String oldContentId = row.getColumnValue("ID");
								String oldIsMain = row.getColumnValue("IS_MAIN");
								String oldContentType = row.getColumnValue("CONTENT_TYPE");
								String oldContent = row.getColumnValue("CONTENT");
								String contentVerId = RandomKit.randomStr();
								/*String sql = "INSERT INTO "+schema+(".KM_INFO_ITEM_CONTENT_VER")+" VALUES("+contentVerId + ",'" + itemId + "','" +oldContentType+"','"+oldContent+"','"
										+oldIsMain+"','"+row.getColumnValue("NEED_UPDATE")+"','"+row.getColumnValue("CREATE_ACC")+"','"+row.getColumnValue("CREATE_NAME")+"','"
										+row.getColumnValue("CREATE_DEPT")+"','"+row.getColumnValue("CREATE_DEPT_NAME")+"','"+row.getColumnValue("CREATE_TIME")+"','"+row.getColumnValue("STATUS")+"','"
										+row.getColumnValue("UPDATE_ACC")+"','"+row.getColumnValue("UPDATE_TIME")+"','"+1+"','"+oldContentId+"','"+itemVerId+"' "
										+")";*/
								//预编译写法
								query.execute("INSERT INTO "+schema+(".KM_INFO_ITEM_CONTENT_VER")
										+" (ID,INFO_ITEM_ID,CONTENT_TYPE,CONTENT,IS_MAIN,NEED_UPDATE,CREATE_ACC,CREATE_NAME,CREATE_DEPT,CREATE_DEPT_NAME,CREATE_TIME,STATUS,UPDATE_ACC,UPDATE_TIME,ITEM_VERSION,INFO_ITEM_CONTENT_ID,INFO_ITEM_VER_ID) "
										+" VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
										 contentVerId,itemId,oldContentType,oldContent,oldIsMain,row.getColumnValue("NEED_UPDATE")
										,row.getColumnValue("CREATE_ACC"),row.getColumnValue("CREATE_NAME"),row.getColumnValue("CREATE_DEPT"),row.getColumnValue("CREATE_DEPT_NAME"),row.getColumnValue("CREATE_TIME")
										,row.getColumnValue("STATUS"),row.getColumnValue("UPDATE_ACC"),row.getColumnValue("UPDATE_TIME"),"1",row.getColumnValue("ID"),itemVerId);
								
								
								String sqlIC = "INSERT INTO "+schema+(".KM_INFO_IC_VER")
										+" (ID,ITEM_VER_ID,ITEM_CONTENT_VER_ID,ITEM_ID,ITEM_CONTENT_ID,ADD_FLAG) "
										+" VALUES("+RandomKit.randomStr() + ",'" + itemVerId + "','" +contentVerId+"','"+itemId+"','"+oldContentId+"','Y'"+")";
								String refIds = oldChannelMap.get(oldContentId);
								if(StringUtils.isNotBlank(refIds)){
									String[] refIdArr = refIds.split(",");
									for (String refId : refIdArr) {
										String sqlChannel = "INSERT INTO "+schema+(".KM_INFO_ITEM_CONTENT_REF_VER")
												+" (ID,INFO_ITEM_ID,INFO_ITEM_CONTENT_ID,REF_TYPE,REF_ID,INFO_ITEM_VER_ID,INFO_ITEM_CONTENT_VER_ID) "
												+" VALUES("+RandomKit.randomStr() + ",'" + itemId + "','" +oldContentId+"','1','"+refId+ "','" + itemVerId + "','" +contentVerId+"')";
										sqlContentVerList.add(sqlChannel);
									}
								}
								//sqlContentVerList.add(sql);
								sqlContentVerList.add(sqlIC);
							}
							
						}
						//新增知识内容历史版本
						query.executeBatch(sqlContentVerList);
					}
				}
			}
		}else{
		}
		//历史版本end
		
		resultList.add(isUpdateItem);
		resultList.add(oldItemVersion);
		return resultList;
	}
	
	
}
