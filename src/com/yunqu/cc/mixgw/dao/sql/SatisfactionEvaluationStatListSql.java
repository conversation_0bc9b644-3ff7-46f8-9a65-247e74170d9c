package com.yunqu.cc.mixgw.dao.sql;

import java.util.HashMap;
import java.util.Map;

import org.apache.log4j.Logger;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.base.QueryFactory;

/**
 * 满意度评价统计报表
 * <AUTHOR>
 *
 */
public class SatisfactionEvaluationStatListSql {
	
	private static Logger logger = CommonLogger.logger;

	public static EasySQL satisfactionEvaluationStatListSql(String schema, String entId, String busiOrderId, String limitDate, String skillId, String agentId, JSONArray hourArray, String stType, String systemPhone) {
		/**
		 * 统计维度（默认按天）
		 * 01：按天
		 * 02：按月
		 * 03：按年
		 */
		String dateQuerySql = "T1.DATE_ID";
		if("02".equals(stType)){
			dateQuerySql = "T1.MONTH_ID";
		}
		if("03".equals(stType)){
			dateQuerySql = "SUBSTRING(T1.MONTH_ID,1,4)";
		}
		EasySQL sql = new EasySQL("SELECT ");
		// 日期
		sql.append(dateQuerySql + " AS DATE_ID,");
		// 坐席账号
		sql.append("T2.USER_ACCT AS AGENT_ID,");
		// 坐席名称
		sql.append("T2.USERNAME AS USERNAME,");
		// 接通数
		sql.append("SUM((CASE WHEN T1.CALL_TYPE_ID  = 2 THEN T1.CONN_SUCC_COUNT ELSE 0 END)) AS AGENT_SUCC_COUNT,");
		// 邀请评价量
		sql.append("SUM(CASE WHEN AGENT_RELEASE = 2 AND CALL_TYPE_ID = '2' THEN CONN_SUCC_COUNT ELSE 0 END) AS TRAN_COUNT,");
		// 已评价数
		sql.append("SUM((CASE WHEN T1.SATISF_ID <> 0 THEN T1.CONN_SUCC_COUNT ELSE 0 END)) AS EVALUATION_COUNT,");
		// 非常满意
		sql.append("SUM(CASE T1.SATISF_ID WHEN 1 THEN T1.CONN_SUCC_COUNT ELSE 0 END) AS GREAT_SATISFACTION,");
		// 满意
		sql.append("SUM(CASE T1.SATISF_ID WHEN 2 THEN T1.CONN_SUCC_COUNT ELSE 0 END) AS SATISFACTION,");
		// 一般
		sql.append("SUM(CASE T1.SATISF_ID WHEN 3 THEN T1.CONN_SUCC_COUNT ELSE 0 END) AS GENERAL,");
		// 对服务不满意
		sql.append("SUM(CASE WHEN T1.SATISF_ID = 4 THEN T1.CONN_SUCC_COUNT ELSE 0 END) AS SERVICE_DISSATISFACTION,");
		// 对结果不满意
		sql.append("SUM(CASE WHEN T1.SATISF_ID = 5 THEN T1.CONN_SUCC_COUNT ELSE 0 END) AS RESULT_DISSATISFACTION ");
		
		Map<String, String> ycstatTableInfo = getYcstatTableByTarget("CC_RPT_CALL_STAT");
		sql.append("FROM " + getStatTableName(ycstatTableInfo.get("TARGET_TABLE_NAME")) + " T1 ");
		sql.append("LEFT JOIN CC_USER T2 ON T1.ENT_ID = T2.ENT_ID AND T1.AGENT_ID = T2.USER_ID ");
		sql.append("WHERE 1 = 1 ");
		sql.append(entId, "AND T1.ENT_ID = ? ", false);
		sql.append(busiOrderId, "AND T1.BUSI_ORDER_ID = ? ", false);
		// TODO 查询条件：坐席
		sql.append(agentId, "AND T1.AGENT_ID = ? ");
		sql.append(skillId, "AND T1.GROUP_ID = ? ");
		sql.append(systemPhone, " and t1.CALLER = ?");
		if(hourArray != null){
			sql.append("AND T1.HOUR_ID IN (" + StringUtils.join(hourArray,",") + ")");
		}
		if (StringUtils.notBlank(limitDate)) {
			String[] split = limitDate.split("~");
			String startDate = split[0].trim().replace("-", "");
			String endDate = split[1].trim().replace("-", "");
			sql.append(startDate, " AND T1.DATE_ID >= ? ");
			sql.append(endDate, " AND T1.DATE_ID <= ? ");
		}
		sql.append("GROUP BY " + dateQuerySql + ", T2.USER_ACCT, T2.USERNAME");
		
		sql.append("UNION ALL");
		
		sql.append("SELECT ");
		
		// 日期
		sql.append("'汇总' AS DATE_ID,");
		// 坐席账号
		sql.append("'--' AS AGENT_ID,");
		// 坐席名称
		sql.append("'--' AS USERNAME,");
		// 接通数
		sql.append("IFNULL(SUM((CASE WHEN T1.CALL_TYPE_ID  = 2 THEN T1.CONN_SUCC_COUNT ELSE 0 END)), '0') AS AGENT_SUCC_COUNT,");
		// 邀请评价量
		sql.append("IFNULL(SUM(CASE WHEN AGENT_RELEASE = 2 AND CALL_TYPE_ID = '2' THEN CONN_SUCC_COUNT ELSE 0 END), '0') AS TRAN_COUNT,");
		// 已评价数
		sql.append("IFNULL(SUM((CASE WHEN T1.SATISF_ID <> 0 THEN T1.CONN_SUCC_COUNT ELSE 0 END)), '0') AS EVALUATION_COUNT,");
		// 非常满意
		sql.append("IFNULL(SUM(CASE T1.SATISF_ID WHEN 1 THEN T1.CONN_SUCC_COUNT ELSE 0 END), '0') AS GREAT_SATISFACTION,");
		// 满意
		sql.append("IFNULL(SUM(CASE T1.SATISF_ID WHEN 2 THEN T1.CONN_SUCC_COUNT ELSE 0 END), '0') AS SATISFACTION,");
		// 一般
		sql.append("IFNULL(SUM(CASE T1.SATISF_ID WHEN 3 THEN T1.CONN_SUCC_COUNT ELSE 0 END), '0') AS GENERAL,");
		// 对服务不满意
		sql.append("IFNULL(SUM(CASE WHEN T1.SATISF_ID = 4 THEN T1.CONN_SUCC_COUNT ELSE 0 END), '0') AS SERVICE_DISSATISFACTION,");
		// 对结果不满意
		sql.append("IFNULL(SUM(CASE WHEN T1.SATISF_ID = 5 THEN T1.CONN_SUCC_COUNT ELSE 0 END), '0') AS RESULT_DISSATISFACTION ");
		
		sql.append("FROM " + getStatTableName(ycstatTableInfo.get("TARGET_TABLE_NAME")) + " T1 ");
		sql.append("LEFT JOIN CC_USER T2 ON T1.ENT_ID = T2.ENT_ID AND T1.AGENT_ID = T2.USER_ID ");
		sql.append("WHERE 1 = 1 ");
		sql.append(entId, "AND T1.ENT_ID = ? ", false);
		sql.append(busiOrderId, "AND T1.BUSI_ORDER_ID = ? ", false);
		sql.append(agentId, "AND T1.AGENT_ID = ? ");
		sql.append(skillId, "AND T1.GROUP_ID = ? ");
		sql.append(systemPhone, " and t1.CALLER = ?");
		if(hourArray != null){
			sql.append("AND T1.HOUR_ID IN (" + StringUtils.join(hourArray,",") + ")");
		}
		if (StringUtils.notBlank(limitDate)) {
			String[] split = limitDate.split("~");
			String startDate = split[0].trim().replace("-", "");
			String endDate = split[1].trim().replace("-", "");
			sql.append(startDate, " AND T1.DATE_ID >= ? ");
			sql.append(endDate, " AND T1.DATE_ID <= ? ");
		}
		sql.append("ORDER BY DATE_ID,AGENT_ID");
		
		CommonLogger.logger.info(CommonUtil.getClassNameAndMethod(SatisfactionEvaluationStatListSql.class) + "满意度评价分析：sql:" + sql.getSQL() + ", param:" + JSONObject.toJSONString(sql.getParams()));
		return sql;
	}
	
	/**
	 * 获取统计库中的最新统计表和统计时间
	 * @param tableName
	 * @return
	 */
	private static Map<String, String> getYcstatTableByTarget(String tableName) {
		Map<String, String> tableInfo = null;
		try {
			String sql = "SELECT TARGET_TABLE_NAME, UPDATE_TIME FROM " + Constants.getStatSchema() + ".CC_STAT_TABLE_INFO WHERE TABLE_ID = ? ";
			tableInfo = QueryFactory.getReadQuery().queryForRow(sql, new String[] {tableName}, new MapRowMapperImpl());
			// 设置默认的统计表
			if(tableInfo == null) {
				tableInfo= new HashMap<>();
				tableInfo.put("TARGET_TABLE_NAME", tableName + "1");
				tableInfo.put("UPDATE_TIME", EasyCalendar.newInstance().getDateTime("-"));
			}
		} catch(Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(SatisfactionEvaluationStatListSql.class) + "获取统计库中的最新统计表名和统计时间出错:" + e.getMessage(), e);
		}
		return tableInfo;
	}
	
	/**
	 * 获取Stat数据库的表名
	 */
	private static String getStatTableName(String tableName) {
		return Constants.getStatSchema() + "." + tableName;
	}
	
}
