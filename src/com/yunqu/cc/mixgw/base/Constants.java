package com.yunqu.cc.mixgw.base;

import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.ConfigUtil;
import com.yq.busi.common.util.SystemParamUtil;
import com.yq.busi.common.util.UserUtil;
import org.easitline.common.core.context.AppContext;

import javax.servlet.http.HttpServletRequest;

/**
 * 常量
 * <AUTHOR>
 *
 */
public class Constants {

	public final static String DS_WIRTE_NAME_ONE = "yc-wirte-ds-1";     //默认数据源名称(写)
	
	public final static String DS_WIRTE_NAME_TWO = "yc-wirte-ds-2";     //默认数据源名称(写)
	
	public final static String DS_READ_NAME= "yc-read-ds";     //默认数据源名称(读)
	
	public final static String DS_CX_GQBI_NAME= "cx-gqbi-ds";     //默认数据源名称(读)
	

	public final static String APP_NAME = "cx-mix";     //应用
	
	/** 企业呼叫中心订购ID */
	public final static String BUSI_ID_007 = "007";

	public final static String SUMMARY_APP_NAME = "cc-summary";

	public final static String EXPORT_HIDEN="01";//导出类型 全部
	
	//public final static String ENT_ID = AppContext.getContext(APP_NAME).getProperty("ENT_ID", "");
	
	public final static String SEND_SMS_URL= AppContext.getContext(APP_NAME).getProperty("SEND_SMS_URL", "http://172.16.10.40:7003/NewSms/ProxyService/SendSmsMgrNewImplProxyService?wsdl");
	
	public final static String ENT_ID = AppContext.getContext(APP_NAME).getProperty("ENT_ID", "");
	/** 经销商信息同步企业ID */
	public final static String DEALERS_ENT_ID = AppContext.getContext(APP_NAME).getProperty("DEALERS_ENT_ID", "");
	/** 零售客服信息同步企业ID */
	public final static String RETAIL_ENT_ID = AppContext.getContext(APP_NAME).getProperty("RETAIL_ENT_ID", "");
	/** 系统医生企业ID */
	public final static String SYSTEM_DOCTOR_ENT_ID = AppContext.getContext(APP_NAME).getProperty("SYSTEM_DOCTOR_ENT_ID", "");
	/** 催收信息同步企业ID */
	public final static String COLLECTION_ENT_ID = AppContext.getContext(APP_NAME).getProperty("COLLECTION_ENT_ID", "");
	/** IAM接口地址*/
	public final static String IAM_INTERFACE_URL = AppContext.getContext(Constants.APP_NAME).getProperty("IAM_INTERFACE_URL", "https://apigateway-test.gsafc.com:8443/rest_ad");
	/** IAM的RSA公钥*/
	public final static String IAM_RSA_PUBLIC_KEY = AppContext.getContext(Constants.APP_NAME).getProperty("IAM_RSA_PUBLIC_KEY", "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDPEHo+NjMU9suBRFwRsxIpVuKhgVhP4+dCmK2OYCUd4rNFCOwTjvz14SovHvX7vSexw8Q1pLdREd+br7f1lZIap66FajSJ0eYQzGrY3lXqAMX93wIMmK23D8YaH4BDNjT2Wc3Eyjlz+74OJgO9j0cJdUb01p76tEw00nSdgrHfRQIDAQAB");
	/** 全媒体在线地址*/
	public final static String MEDIA_URL = AppContext.getContext(Constants.APP_NAME).getProperty("MEDIA_URL", "");
	/** FES同步开始时间*/
	public final static String FES_START_TIME = AppContext.getContext(Constants.APP_NAME).getProperty("FES_START_TIME", "000000");
	/** FES同步结束时间*/
	public final static String FES_END_TIME = AppContext.getContext(Constants.APP_NAME).getProperty("FES_END_TIME", "235959");
	/** UNIONID解密密钥*/
	public final static String UNION_ID_PRIVATE_KEY = AppContext.getContext(Constants.APP_NAME).getProperty("UNION_ID_PRIVATE_KEY", "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAKBBhyArPzdsh3cb8fbfMi8AVEpWf+MZfoXsDdOlyeNntDBRIPUceKertXIzE588VIB8yixjEdIBg0UDSlikRhxYBpkSnpXBJ9/250onwiE3wRlnNIiS7eioCkBR11ksEcPbdNZs1S9MyNG3L5l4NQ+apgOwCLb4fG7EIj/YQrevAgMBAAECgYBDXWhM6IEJblC/i/q5mPdCdLP6D8RwlX3vE9x4FJ/6PNSs3t2oy5AYg4ChgcEE1dp7rHlcEUUjb0lcEFcHTf6+LVmUrtkv9r8XZfNvux7568v7/HjyoXz6mSS2Gq2cH/83gdBBj8ZBc+Rqb70/Gy7zPHl5RcUoe+n5yiGPpHEeGQJBAOop06uLpjJt0j/s0oV2NI2clJ3EMPM6sI7lit5PwKf0d1foooskBfFBqunhQzMSSdhHvzCC/9NZ9uNJdS4KkI0CQQCvM0/pw+6xpckDwTzQ/CgvdqhMdtPXpQ53bo3jrLPi4GQUVjpnmWE9z8dppYL9tB9V1+shK9XuOIWvnvwjajArAkBSQQEOmE54FmWteNX2dbA+MktuI9WCCxKsD2u5bdBh7xjg82HteGjx9zw/TZaaYQk5hPJe6xVMO6Ti8BHOa32dAkBDdqrLBVugpIonG/3jK3X98N8VspwYacKMUtHoXdRXBLkE679JrVI4jw3mrIkoUHfQgnhUKaETPkicO8gziuxJAkBDTOD07zRgzkJtQcSvDgHCAwM6vJHgjh2wW55gAVes5RttLqBNga8VZizgdBrM8WsCu8v+XKd9dwP+Gs8xeFLg");
	/** 提示输入手机号话数*/
	public final static String PROMPT_INPUT_WORDS = AppContext.getContext(Constants.APP_NAME).getProperty("PROMPT_INPUT_WORDS", "");
	/** 手机号校验失败话数*/
	public final static String PHONE_VERIFY_FAIL = AppContext.getContext(Constants.APP_NAME).getProperty("PHONE_VERIFY_FAIL", "");
	/** 微信校验失败话数*/
	public final static String WECHAT_VERIFY_FAIL = AppContext.getContext(Constants.APP_NAME).getProperty("WECHAT_VERIFY_FAIL", "");
	/** 微信小程序校验失败话数*/
	public final static String WECHAT_VERIFY_FAIL2 = AppContext.getContext(Constants.APP_NAME).getProperty("WECHAT_VERIFY_FAIL2", "");
	/** 清理文件路径*/
	public final static String CLEAR_FILE_PATH = AppContext.getContext(Constants.APP_NAME).getProperty("CLEAR_FILE_PATH", "");
	/** 清理文件后缀*/
	public final static String CLEAR_FILE_SUFFIX = AppContext.getContext(Constants.APP_NAME).getProperty("CLEAR_FILE_SUFFIX", "");
	/** 招行虚拟账户接口URL */
	public final static String WECHAT_MINIPROGRAM_URL = AppContext.getContext(Constants.APP_NAME).getProperty("WECHAT_MINIPROGRAM_URL", "");
	/** 招行虚拟账户接口appId */
	public final static String WECHAT_MINIPROGRAM_APPID = AppContext.getContext(Constants.APP_NAME).getProperty("WECHAT_MINIPROGRAM_APPID", "");
	/** 申请虚拟账户下发短信接口 */
	public final static String WECHAT_VIRTUAL_ACCOUNT = "/api/wechat/support/va/send-sms/cc";
	/** 查询虚拟账户信息接口 */
	public final static String WECHAT_QUERY_VIRTUAL_ACCOUNT = "/api/wechat/support/va/detail/cc";
	/** 查询虚拟账户流水明细接口 */
	public final static String WECHAT_QUERY_VIRTUAL_ACCOUNT_FLOW_LIST = "/api/wechat/support/va/flow-info/cc";
	/** 申请虚拟账户下发短信接口 */
	// public final static String WECHAT_VIRTUAL_ACCOUNT = "/api/wechat/virtual-account/send-sms";
	/** 查询虚拟账户信息接口 */
	// public final static String WECHAT_QUERY_VIRTUAL_ACCOUNT = "/api/wechat/virtual-account/detail";
	/** 查询虚拟账户流水明细接口 */
	// public final static String WECHAT_QUERY_VIRTUAL_ACCOUNT_FLOW_LIST = "/api/wechat/virtual-account/flow-info";

	/**
	 * 系统医生接口url
	 * @return
	 */
	public static String getSystemDoctorUrl() {
		return AppContext.getContext(Constants.APP_NAME).getProperty("SYSTEM_DOCTOR_URL", "");
	}
	
	/**
	 * 系统医生接口url
	 * @return
	 */
	public static String getSystemDoctorAppId() {
		return AppContext.getContext(Constants.APP_NAME).getProperty("SYSTEM_DOCTOR_APPID", "");
	}
	
	private static AppContext context = AppContext.getContext("yc-api");
	
	private static AppContext baseContext = AppContext.getContext("cc-base");
	
	public final static String BI_SYSTEM_SERVICE = "BI_SYSTEM_SERVICE";
	
	/** 是否正在同步零售客服信息 */
	public final static String IS_SYN_RETAIL_INFO = "IS_SYN_RETAIL_INFO";
	
	/** 是否正在同步JIRA工单信息 */
	public final static String IS_SYN_ORDER_HISTORY = "IS_SYN_ORDER_HISTORY";
	
	/** 是否正在同步FES信息 */
	public final static String IS_SYN_FES_INFO = "IS_SYN_FES_INFO";
	/** 是否正在同步FES信息 */
	public final static String IS_SYN_RECEIPT_DTL = "IS_SYN_RECEIPT_DTL";

	/** 是否正在同步微信结算补偿信息 */
	public final static String IS_SYN_WECHAT_SETTLED_COMPENSATION = "IS_SYN_WECHAT_SETTLED_COMPENSATION";

	/** 全媒体校验转人工 */
	public final static String MEDIA_VERIFY_ZXKF = "zxkf";
	/** 全媒体校验结束会话 */
	public final static String MEDIA_VERIFY_CLOSE = "close";
	/** 全媒体校验文本内容 */
	public final static String MEDIA_VERIFY_MESSAGE = "message";
	/** 全媒体校验前缀 */
	public final static String MEDIA_VERIFY = "MEDIA_VERIFY";
	
	public static String getLoginUrl(){
		return AppContext.getContext(APP_NAME).getProperty("LOGIN_URL", "");
	}
	
	public static String getDefaulBusiSchema() {
		return AppContext.getContext("yc-api").getProperty("BUSI_DB", "ycbusi");
	}
	
	/**
	 * 统计库名称
	 */
	public static String getStatSchema(){
		return context.getProperty("STAT_DB","stat");
	}

	public static String getPassword(){
		return baseContext.getProperty("SERVICE_INTERFACE_PWD", "yq_85521717");
	}
	
	/**
	 * 获取对接云中继企业ID
	 * @return
	 */
	public static String getRelayCloudEntId() {
		return AppContext.getContext(Constants.APP_NAME).getProperty("RELAY_CLOUD_ENT_ID", "");
	}
	
	/** 同步接口类型 01-合同信息 **/
	public static final String SYNC_COMMAND_01 = "01";
	/** 同步接口类型 02-经销商信息 **/
	public static final String SYNC_COMMAND_02 = "02";
	/** 同步接口类型 03-历史信息 **/
	public static final String SYNC_COMMAND_03 = "03";
	/** 同步接口类型 04-Fes信息 **/
	public static final String SYNC_COMMAND_04 = "04";
	/** 同步接口类型 05-合同状态 **/
	public static final String SYNC_COMMAND_05 = "05";
	/** 同步接口类型 06-微信结算补偿信息 **/
	public static final String SYNC_COMMAND_06 = "06";
	
	/** 同步结果 01-未满足同步条件 **/
	public static final String SYNC_RESULT_01 = "01";
	/** 同步结果 02-同步成功 **/
	public static final String SYNC_RESULT_02 = "02";
	/** 同步结果 03-同步失败 **/
	public static final String SYNC_RESULT_03 = "03";

	public static String getBindContractUrl() {
		return AppContext.getContext(Constants.APP_NAME).getProperty("BIND_CONTRACT_URL", "");
	}

	public static String getBindContractMap() {
		return AppContext.getContext(Constants.APP_NAME).getProperty("BIND_CONTRACT_MAP", "");
	}

	public static String getMediaSignKey() {
		return AppContext.getContext(Constants.APP_NAME).getProperty("MEDIA_SIGN_KEY", "");
	}

	public static String getAgentSpecialPhone() {
		return AppContext.getContext(Constants.APP_NAME).getProperty("AGENT_SPECIAL_PHONE", "");
	}


	public static String getAgentSpecialPhoneJgts() {
		return AppContext.getContext(Constants.APP_NAME).getProperty("AGENT_SPECIAL_PHONE_JGTS", "");
	}

	public static String getMediaInMethod() {
		return AppContext.getContext(Constants.APP_NAME).getProperty("MEDIA_IN_METHOD", "");
	}

	// 缓存前缀：客户端验证码
	public static final String CACHE_CLIENTVERIFY_PREFIX = "CACHE_CLIENTVERIFY_";

	/** 是否开手动评分,方法获取 */
	public static String getRunManual() {
		return AppContext.getContext("cc-quality").getProperty("RUN_MANUAL", "N");
	}

	/** 一键质检时手动选择质检任务 */
	public static String getSelectQcTask(HttpServletRequest request) {
		UserModel user = UserUtil.getUser(request);
		return getSelectQcTask(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId());
	}

	public static String getSelectQcTask(String schema, String entId, String busiOrderId) {
		String cfg = SystemParamUtil.getEntParam(schema, entId, busiOrderId, "cc-quality", "SELECT_QC_TASK");
		return cfg;
	}

	public static int getQueryScope(){
		return ConfigUtil.getInt("cc-report","QUERY_TIME_SCOPE", 31);
	}

	/** 是否开手动评分,方法获取 */
	public static String getIpWhiteList() {
		return AppContext.getContext(APP_NAME).getProperty("IP_WHITE_LIST", "");
	}

}
