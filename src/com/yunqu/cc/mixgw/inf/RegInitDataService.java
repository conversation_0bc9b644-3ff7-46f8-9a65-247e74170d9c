package com.yunqu.cc.mixgw.inf;

import org.easitline.common.core.service.ServiceException;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.IBaseService;
import com.yq.busi.common.dict.DictConstants;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yunqu.cc.mixgw.base.Constants;
/**
 * 提供本模块需要初始化的数据，如数据字典、定时任务等
 * 提供的数据由相关管理模块通过服务类接口采集
 */
public class RegInitDataService extends IBaseService{
	
	/**
	 * 字典组类型：2-定制字典
	 */
	private static final String DICT_TYPE_CUSTOM = "2";

	@Override
	public JSONObject invokeMethod(JSONObject json) throws ServiceException {
		
		JSONObject result = new JSONObject();
		JSONObject data = new JSONObject();
		
		data.put("dict", getInitDict());
		data.put("jobs", getInitJobs());
		result.put("data", data);
		result.put("respCode", GWConstants.RET_CODE_SUCCESS);
		result.put("respDesc", "操作成功");
		
		return result;
	}


	/**
	 * 获取该模块里的定时任务
	 * @return
	 */
	private JSONArray getInitJobs() {
		
		JSONArray jobArray = new JSONArray();
		
		JSONObject job1 = new JSONObject();
		job1.put("reqMain", Constants.BI_SYSTEM_SERVICE);
		job1.put("reqParam", "synRetailInfo");
		job1.put("name", "每小时进行同步零售客服信息");
		job1.put("jobDesc", "每小时进行同步零售客服信息");
		job1.put("execType", DictConstants.EXEC_TYPE_INTEVAL_HOUR);
		job1.put("execRule", "1");
		job1.put("execDelay", 150);
		job1.put("type", DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job1.put("builtIn", DictConstants.DICT_SY_YN_Y);
		jobArray.add(job1);
		
		JSONObject job2 = new JSONObject();
		job2.put("reqMain", Constants.BI_SYSTEM_SERVICE);
		job2.put("reqParam", "synDealersInfo");
		job2.put("name", "每小时进行同步经销商信息");
		job2.put("jobDesc", "每小时进行同步经销商信息");
		job2.put("execType", DictConstants.EXEC_TYPE_INTEVAL_HOUR);
		job2.put("execRule", "1");
		job2.put("execDelay", 150);
		job2.put("type", DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job2.put("builtIn", DictConstants.DICT_SY_YN_Y);
		jobArray.add(job2);
		
		JSONObject job3 = new JSONObject();
		job3.put("reqMain", Constants.BI_SYSTEM_SERVICE);
		job3.put("reqParam", "synFes");
		job3.put("name", "同步FES数据-每三小时");
		job3.put("jobDesc", "每隔3小时同步FES数据");
		job3.put("execType", DictConstants.EXEC_TYPE_INTEVAL_HOUR);
		job3.put("execRule", "3");
		job3.put("execDelay", 150);
		job3.put("type", DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job3.put("builtIn", DictConstants.DICT_SY_YN_Y);
		jobArray.add(job3);
		
		JSONObject job4 = new JSONObject();
		job4.put("reqMain", Constants.BI_SYSTEM_SERVICE);
		job4.put("reqParam", "delOverdueFes");
		job4.put("name", "每天FES过期数据");
		job4.put("jobDesc", "每天删除FES过期数据");
		job4.put("execType", DictConstants.EXEC_TYPE_BY_DAY);
		job4.put("execRule", "01:00:00");
		job4.put("execDelay", 150);
		job4.put("type", DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job4.put("builtIn", DictConstants.DICT_SY_YN_Y);
		jobArray.add(job4);
		
		JSONObject job5 = new JSONObject();
		job5.put("reqMain", Constants.BI_SYSTEM_SERVICE);
		job5.put("reqParam", "synRequestStatus");
		job5.put("name", "同步合同状态信息-按小时");
		job5.put("jobDesc", "每隔1小时同步合同状态信息(成功后不执行当天不再执行)");
		job5.put("execType", DictConstants.EXEC_TYPE_INTEVAL_HOUR);
		job5.put("execRule", "1");
		job5.put("execDelay", 150);
		job5.put("type", DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job5.put("builtIn", DictConstants.DICT_SY_YN_Y);
		jobArray.add(job5);
		
		JSONObject job6 = new JSONObject();
		job6.put("reqMain", Constants.BI_SYSTEM_SERVICE);
		job6.put("reqParam", "synOrderHistory");
		job6.put("name", "每小时进行同步JIRA工单历史信息");
		job6.put("jobDesc", "每小时进行同步JIRA工单历史信息");
		job6.put("execType", DictConstants.EXEC_TYPE_INTEVAL_HOUR);
		job6.put("execRule", "1");
		job6.put("execDelay", 150);
		job6.put("type", DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job6.put("builtIn", DictConstants.DICT_SY_YN_Y);
		jobArray.add(job6);
		
		JSONObject job7 = new JSONObject();
		job7.put("reqMain", "VIRTUAL_ACC_JOB_SERVICE");
		job7.put("reqParam", "virtualAcc");
		job7.put("name", "每30秒检测申请虚拟账户失败数据，进行重新发送处理");
		job7.put("jobDesc", "每30秒检测申请虚拟账户失败数据，进行重新发送处理");
		job7.put("execType", DictConstants.EXEC_TYPE_INTEVAL_SECOND);
		job7.put("execRule", "30");
		job7.put("execDelay", 150);
		job7.put("type", DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job7.put("builtIn", DictConstants.DICT_SY_YN_Y);
		jobArray.add(job7);
		
		JSONObject job8 = new JSONObject();
		job8.put("reqMain", "SYSTEM_DOCTOR_SERVICE");
		job8.put("reqParam", "sendData");
		job8.put("name", "每10分钟检测推送会话记录---系统医生");
		job8.put("jobDesc", "每10分钟检测推送会话记录---系统医生");
		job8.put("execType", DictConstants.EXEC_TYPE_BY_MINUTE);
		job8.put("execRule", "10");
		job8.put("execDelay", 150);
		job8.put("type", DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job8.put("builtIn", DictConstants.DICT_SY_YN_Y);
		jobArray.add(job8);
		
		JSONObject job9 = new JSONObject();
		job9.put("reqMain", "CX_MIX_GQ_COLLECT_STAT_DATA");
		job9.put("reqParam", "UPDATE_TASK_OBJ");
		job9.put("name", "每10分钟汇总更新表的特定数据");
		job9.put("jobDesc", "每10分钟汇总更新表的特定数据");
		job9.put("execType", DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job9.put("execRule", "10");
		job9.put("execDelay", 150);
		job9.put("type", DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job9.put("builtIn", DictConstants.DICT_SY_YN_Y);
		jobArray.add(job9);


		JSONObject job10 = new JSONObject();
		job10.put("reqMain", Constants.BI_SYSTEM_SERVICE);
		job10.put("reqParam", "synReceipt");
		job10.put("name", "每30分钟同步手工扣账记录信息");
		job10.put("jobDesc", "每30分钟同步手工扣账记录信息");
		job10.put("execType", DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job10.put("execRule", "30");
		job10.put("execDelay", 150);
		job10.put("type", DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job10.put("builtIn", DictConstants.DICT_SY_YN_Y);
		jobArray.add(job10);

		JSONObject job11 = new JSONObject();
		job11.put("reqMain", Constants.BI_SYSTEM_SERVICE);
		job11.put("reqParam", "synWechatSettledCompensationInfo");
		job11.put("name", "每小时进行同步微信结算补偿信息");
		job11.put("jobDesc", "每小时进行同步微信结算补偿信息");
		job11.put("execType", DictConstants.EXEC_TYPE_INTEVAL_HOUR);
		job11.put("execRule", "1");
		job11.put("execDelay", 150);
		job11.put("type", DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job11.put("builtIn", DictConstants.DICT_SY_YN_Y);
		jobArray.add(job11);
		return jobArray;
	}
	

	/**
	 * 获取本模块所需的数据字段集合
	 * @return
	 */
	private JSONArray getInitDict() {
		
		JSONArray dictArray = new JSONArray();
		
		JSONObject dictGroup1 = createDictGroup("BI_SYNC_COMMAND","接口类型");
		JSONArray dictGroupArray1 = new JSONArray();
		dictGroupArray1.add(createDict(Constants.SYNC_COMMAND_01,"合同信息",1));
		dictGroupArray1.add(createDict(Constants.SYNC_COMMAND_02,"经销商信息",2));
		dictGroupArray1.add(createDict(Constants.SYNC_COMMAND_03,"历史信息",3));
		dictGroupArray1.add(createDict(Constants.SYNC_COMMAND_04,"Fes信息",4));
		dictGroupArray1.add(createDict(Constants.SYNC_COMMAND_05,"合同状态",5));
		dictGroupArray1.add(createDict(Constants.SYNC_COMMAND_06,"微信结算补偿信息",6));
		dictGroup1.put("DICTS", dictGroupArray1);
		dictArray.add(dictGroup1);

		JSONObject dictGroup2 = createDictGroup("BI_SYNC_RESULT","接口类型");
		JSONArray dictGroupArray2 = new JSONArray();
		dictGroupArray2.add(createDict(Constants.SYNC_RESULT_01,"未满足同步条件",1));
		dictGroupArray2.add(createDict(Constants.SYNC_RESULT_02,"同步成功",2));
		dictGroupArray2.add(createDict(Constants.SYNC_RESULT_03,"同步失败",3));
		dictGroup2.put("DICTS", dictGroupArray2);
		dictArray.add(dictGroup2);
		
		return dictArray;
	}

	/**
	 * 创建字典项
	 * @param code
	 * @param name
	 * @param index
	 * @return
	 */
	private JSONObject createDict(String code, String name, int index) {
		JSONObject dict = new JSONObject();
		dict.put("CODE", code);
		dict.put("NAME", name);
		dict.put("SORT_NUM", index);
		
		return dict;
	}

	/**
	 * 创建字典组
	 * @param code
	 * @param val
	 * @return type 组类型  0-系统字典(系统内置) 1-业务字典(用户配置) 2-定制字典(系统内置,字典项可修改)
	 */
	private JSONObject createDictGroup(String code, String val,String type) {
		JSONObject dictGroup1 = new JSONObject();
		dictGroup1.put("CODE", code);
		dictGroup1.put("NAME", val);
		dictGroup1.put("TYPE", type);
		dictGroup1.put("MODULE", Constants.APP_NAME);
		return dictGroup1;
	}
	
	/**
	 * 创建字典组
	 * @param code
	 * @param val
	 * @return
	 */
	private JSONObject createDictGroup(String code, String val) {
		JSONObject dictGroup1 = new JSONObject();
		dictGroup1.put("CODE", code);
		dictGroup1.put("NAME", val);
		dictGroup1.put("MODULE", Constants.APP_NAME);
		return dictGroup1;
	}

	@Override
	public String getServiceId() {
		return "CC-REG-INIT-DATA-"+Constants.APP_NAME;
	}

	@Override
	public String getName() {
		return "CX-MIX-GQ初始化数据服务接口";
	}

	
}
