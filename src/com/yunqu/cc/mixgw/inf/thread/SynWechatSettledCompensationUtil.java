package com.yunqu.cc.mixgw.inf.thread;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.SQLRecoverableException;
import java.sql.SQLTimeoutException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.base.QueryFactory;
import com.yunqu.cc.mixgw.util.BiSyncLogUtils;

/**
 * 微信结算补偿信息同步工具类
 * <AUTHOR>
 * @date 2025-07-03
 */
public class SynWechatSettledCompensationUtil {

	private static Logger logger = CommonLogger.getLogger("bi-system-syn");
	
	// 是否正在运行
	private volatile boolean isRun = false;
	
	private static class SynWechatSettledCompensationUtilHolder {
		private static SynWechatSettledCompensationUtil instance = new SynWechatSettledCompensationUtil();
	}

	
	public static SynWechatSettledCompensationUtil getInstance() {
		return SynWechatSettledCompensationUtilHolder.instance;
	}	
	
	/**
	 * 同步微信结算补偿信息
	 * @param schema 数据库schema
	 * @param dateId 指定同步日期
	 * @param isRepeat 是否重复同步
	 * @return
	 */
	public JSONObject synWechatSettledCompensationInfo(String schema, String dateId, boolean isRepeat) {
		JSONObject result = new JSONObject();
		if(isRun) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 正在同步数据，不可重复执行");
			result.put("respDesc", "正在同步数据，不可重复执行");
			result.put("respCode", GWConstants.RET_CODE_SUCCESS);
			return result;
		}
		long startTime = System.currentTimeMillis();
		try {
			isRun = true;
			
			String maxUpdateDate = dateId;
			String lastDate = "";
			if(!isRepeat) {
				// 查询出最近一次更新时间
				maxUpdateDate = QueryFactory.getReadQuery().queryForString("SELECT MAX(ETL_DATE) FROM " + schema + ".CX_GQ_WECHAT_SETTLED_COMPENSATION", new Object [] {});
				
				// 查询BI系统最新ETL时间
				EasySQL sql = new EasySQL("SELECT MAX(ETL_DATE) FROM WECHATAPI.WECHAT_SETTLED_COMPENSATION");
				lastDate = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_CX_GQBI_NAME).queryForString(sql.getSQL(), sql.getParams());
				
				if(StringUtils.isBlank(lastDate) || StringUtils.equals(CacheUtil.get("CURRENT_DAY_SYN_WECHAT_SETTLED_COMPENSATION"), "Y")) {
					logger.info(CommonUtil.getClassNameAndMethod(this) + " BI系统数据暂未更新或当日已更新");
					result.put("respDesc", "BI系统数据暂未更新或当日已更新");
					result.put("respCode", GWConstants.RET_CODE_SUCCESS);
					BiSyncLogUtils.saveSyncLog(Constants.RETAIL_ENT_ID, schema, Constants.SYNC_COMMAND_06, startTime, Constants.SYNC_RESULT_01, "不满足同步条件");
					return result;
				}
			}
			
			int totalCount = getNeedSynDataCount(maxUpdateDate);
			if(totalCount < 1) {
				logger.info(CommonUtil.getClassNameAndMethod(this) + " 暂无待同步记录");
				result.put("respDesc", "暂无待同步记录");
				result.put("respCode", GWConstants.RET_CODE_SUCCESS);
				return result;
			}
			// 清空历史表记录
			QueryFactory.getWriteQuery().execute("TRUNCATE TABLE " + schema + ".CX_GQ_WECHAT_SETTLED_COMPENSATION_TMP", new Object[] {});
			
			// 同步数据至临时表
			int synIndex = readWechatSettledCompensationList(maxUpdateDate, totalCount, schema);
			
			// 将现有数据同步到备份表（如果需要）
			// QueryFactory.getWriteQuery().execute("TRUNCATE TABLE " + schema + ".CX_GQ_WECHAT_SETTLED_COMPENSATION2");
			// QueryFactory.getWriteQuery().execute("insert into " + schema + ".CX_GQ_WECHAT_SETTLED_COMPENSATION2 select * from " + schema + ".CX_GQ_WECHAT_SETTLED_COMPENSATION");
			
			synWechatSettledCompensationTable(schema);
			result.put("respDesc", "同步微信结算补偿信息成功");
			result.put("respCode", GWConstants.RET_CODE_SUCCESS);
			long endTime = System.currentTimeMillis();
			CacheUtil.put("CURRENT_DAY_SYN_WECHAT_SETTLED_COMPENSATION", "Y", getDayRemainingTime(new Date()));
			logger.info(CommonUtil.getClassNameAndMethod(this) + " 同步微信结算补偿信息成功，lastDate<" + lastDate + "> synIndex<" + synIndex + ">，耗时:" + (endTime - startTime));
			BiSyncLogUtils.saveSyncLog(Constants.RETAIL_ENT_ID, schema, Constants.SYNC_COMMAND_06, startTime, Constants.SYNC_RESULT_02, "同步成功，最大业务日期:" + lastDate);
		} catch (Exception e) {
			result.put("respDesc", "同步微信结算补偿信息失败");
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 同步微信结算补偿信息失败 error:" + e.getMessage(), e);
			BiSyncLogUtils.saveSyncLog(Constants.RETAIL_ENT_ID, schema, Constants.SYNC_COMMAND_06, startTime, Constants.SYNC_RESULT_03, "同步失败, error:" + e.getMessage());
		} finally {
			isRun = false;
		}
		return result;
	}
	
	/**
	 * 同步微信结算补偿信息（使用当前日期）
	 * @param schema
	 * @return
	 */
	public JSONObject synWechatSettledCompensationInfo(String schema) {
		return synWechatSettledCompensationInfo(schema, DateUtil.getCurrentDateStr("yyyyMMdd"), false);
	}
	
	
	/**
	 * 同步临时表数据至数据表
	 * @param schema
	 * @throws Exception 
	 */
	private void synWechatSettledCompensationTable(String schema) throws SQLException {
		CacheUtil.put(Constants.IS_SYN_WECHAT_SETTLED_COMPENSATION, "Y");
		long startTime = System.currentTimeMillis();
		try {
			EasyQuery writeQuery = QueryFactory.getWriteQuery();
			while(true) {
				// 删除临时表中已存在的合同信息
				String deleteSql = "DELETE FROM " + schema + ".CX_GQ_WECHAT_SETTLED_COMPENSATION WHERE EXISTS (SELECT CONTRACT_NUMBER FROM " + schema + ".CX_GQ_WECHAT_SETTLED_COMPENSATION_TMP WHERE CX_GQ_WECHAT_SETTLED_COMPENSATION.CONTRACT_NUMBER = CX_GQ_WECHAT_SETTLED_COMPENSATION_TMP.CONTRACT_NUMBER) LIMIT " + 2000;
				int updateCount = writeQuery.executeUpdate(deleteSql);
				if(updateCount == 0) {
					break;
				}
			}
			
			String selectIntoSql = "INSERT INTO " + schema + ".CX_GQ_WECHAT_SETTLED_COMPENSATION SELECT * FROM " + schema + ".CX_GQ_WECHAT_SETTLED_COMPENSATION_TMP";
			writeQuery.executeUpdate(selectIntoSql);
		} catch (SQLException e) {
			CacheUtil.put(Constants.IS_SYN_WECHAT_SETTLED_COMPENSATION, "N");
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 同步临时表数据至实体表失败 error:" + e.getMessage(), e);
			throw new SQLException();
		}
		CacheUtil.put(Constants.IS_SYN_WECHAT_SETTLED_COMPENSATION, "N");
		long endTime = System.currentTimeMillis();
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 同步临时表数据至实体表成功，耗时:" + (endTime - startTime));
	}
	
	/**
	 * 读取微信结算补偿信息列表
	 * @param maxUpdateDate 最大更新日期
	 * @param totalCount 总数量
	 * @param schema 数据库schema
	 * @return 同步索引
	 * @throws SQLException
	 */
	private int readWechatSettledCompensationList(String maxUpdateDate, int totalCount, String schema) throws SQLException {
		long startTime = System.currentTimeMillis();
		Connection conn = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		
		int synIndex = 0;
		boolean isError = false;
		String lastContractNumber = "";
		List<JSONObject> dataList = new ArrayList<JSONObject>();
		
		// 循环次数，防止无限制循环
		int whileIndex = 0;
		// 读取数据超时，需要遍历读取
		while(whileIndex < 15) {
			StringBuilder sqlStr = new StringBuilder("SELECT * FROM WECHATAPI.WECHAT_SETTLED_COMPENSATION t where 1 = 1");
			if(StringUtils.isNotBlank(maxUpdateDate)) {
				sqlStr.append(" AND ETL_DATE >= '" + maxUpdateDate + "'");
			}
			if(StringUtils.isNotBlank(lastContractNumber)) {
				sqlStr.append(" AND CONTRACT_NUMBER > '" + lastContractNumber + "'");
			}
			sqlStr.append(" ORDER BY CONTRACT_NUMBER");
			try {
				conn = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_CX_GQBI_NAME).getConnection();
				stmt = conn.prepareStatement(sqlStr.toString(), ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
				stmt.setFetchSize(100);
				stmt.setMaxRows(totalCount);
				stmt.setQueryTimeout(1800);
				rs = stmt.executeQuery();
				int i = 0;
				String insertSql = "INSERT INTO " +schema + ".CX_GQ_WECHAT_SETTLED_COMPENSATION_TMP ";
				while(rs.next()){
					JSONObject dataJson = new JSONMapperImpl().mapRow(rs,2);
					
					if(StringUtils.equals(lastContractNumber, rs.getString("CONTRACT_NUMBER"))) {
						continue;
					}
					
					dataList.add(dataJson);
					lastContractNumber = rs.getString("CONTRACT_NUMBER");
					i++;
					synIndex++;
					if(i >= 1000) {
						insertWechatSettledCompensationTmp(insertSql, dataList);
						dataList.clear();
						i = 0;
					}
				}
				insertWechatSettledCompensationTmp(insertSql, dataList);
				break;
			} catch (SQLRecoverableException e) {
				// 此异常为超时异常，关闭连接，继续读取循环
				logger.info(CommonUtil.getClassNameAndMethod(this) + " BI数据读取超时，截至合同ID为lastContractNumber<" + lastContractNumber + ">，进行循环");
				continue;
			} catch(SQLTimeoutException e) {
				// 此异常为超时异常，关闭连接，继续读取循环
				logger.info(CommonUtil.getClassNameAndMethod(this) + " BI数据读取超时，截至合同ID为lastContractNumber<" + lastContractNumber + ">，进行循环");
				continue;
			} catch(Exception e) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + " 同步BI数据失败 error:" + e.getMessage(), e);
				isError = true;
				break;
			} finally {
				try {
					if (rs != null) {
						rs.close();
					}
				} catch (Exception ex) {
					logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + ex.getMessage(), ex);
				}
				try {
					if (stmt != null) {
						stmt.close();
					}
				} catch (Exception ex) {
					logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + ex.getMessage(), ex);
				}
				try {
					if (conn != null) {
						conn.close();
					}
				} catch (Exception ex) {
					logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + ex.getMessage(), ex);
				}
				whileIndex++;
			}
		}
		if(isError) {
			throw new SQLException();
		}
		long endTime = System.currentTimeMillis();
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 读取BI系统数据信息成功，耗时：" + (endTime - startTime));
		return synIndex;
	}

	/**
	 * 批量插入微信结算补偿信息到临时表
	 * @param insertSql
	 * @param dataList
	 * @throws SQLException
	 */
	private void insertWechatSettledCompensationTmp(String insertSql, List<JSONObject> dataList) throws SQLException {
		try {
			int dataSize = dataList.size();
			if(dataSize == 0) {
				return;
			}

			StringBuilder sqlStr = new StringBuilder(insertSql);
			sqlStr.append("(CONTRACT_NUMBER,RTL_TL_FLAG,REQUEST_STATUS_DSC,IS_HOLD_DD,UNALLOCATED_AMT,TTL_RENTAL_OVERDUE,INTEREST_RATE,DISTRIBUTOR_NAME_CHS,STRUCTURED_RENTAL_IND,ASSET_CONDITION_CODE,SETTLED_TRM,ASSET_TYPE,LEASE_TYPE,CONTRACT_ACTIVATION_DATE,EVNT_DTE_TIME,LS_PRINCIPAL_OUTSTANDING_AMT,INTEREST_AMT,PRINCIPAL_OUTSTANDING_AMT,SOURCE_CODE,ETL_DATE,FINANCIAL_PRODUCT_NAME,CONTRACT_TRM,DUE_DAY) VALUES ");

			List<Object> paramList = new ArrayList<Object>();
			for(int i = 0; i < dataSize; i++) {
				JSONObject dataJson = dataList.get(i);
				if(i > 0) {
					sqlStr.append(",");
				}
				sqlStr.append("(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

				paramList.add(escapeString(dataJson.getString("CONTRACT_NUMBER")));
				paramList.add(escapeString(dataJson.getString("RTL_TL_FLAG")));
				paramList.add(escapeString(dataJson.getString("REQUEST_STATUS_DSC")));
				paramList.add(escapeString(dataJson.getString("IS_HOLD_DD")));
				paramList.add(dataJson.getBigDecimal("UNALLOCATED_AMT"));
				paramList.add(dataJson.getBigDecimal("TTL_RENTAL_OVERDUE"));
				paramList.add(dataJson.getBigDecimal("INTEREST_RATE"));
				paramList.add(escapeString(dataJson.getString("DISTRIBUTOR_NAME_CHS")));
				paramList.add(escapeString(dataJson.getString("STRUCTURED_RENTAL_IND")));
				paramList.add(escapeString(dataJson.getString("ASSET_CONDITION_CODE")));
				paramList.add(dataJson.getBigDecimal("SETTLED_TRM"));
				paramList.add(escapeString(dataJson.getString("ASSET_TYPE")));
				paramList.add(escapeString(dataJson.getString("LEASE_TYPE")));
				paramList.add(escapeString(dataJson.getString("CONTRACT_ACTIVATION_DATE")));
				paramList.add(escapeString(dataJson.getString("EVNT_DTE_TIME")));
				paramList.add(dataJson.getBigDecimal("LS_PRINCIPAL_OUTSTANDING_AMT"));
				paramList.add(dataJson.getBigDecimal("INTEREST_AMT"));
				paramList.add(dataJson.getBigDecimal("PRINCIPAL_OUTSTANDING_AMT"));
				paramList.add(escapeString(dataJson.getString("SOURCE_CODE")));
				paramList.add(escapeString(dataJson.getString("ETL_DATE")));
				paramList.add(escapeString(dataJson.getString("FINANCIAL_PRODUCT_NAME")));
				paramList.add(dataJson.getBigDecimal("CONTRACT_TRM"));
				paramList.add(escapeString(dataJson.getString("DUE_DAY")));
			}

			QueryFactory.getWriteQuery().execute(sqlStr.toString(), paramList.toArray());
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 批量导入数据至临时表失败 error: " + e.getMessage(), e);
			throw new SQLException();
		}
	}

	/**
	 * 获取待同步数据条数
	 * @param maxUpdateDate 最近一次更新日期
	 * @return
	 * @throws SQLException
	 */
	private int getNeedSynDataCount(String maxUpdateDate) throws SQLException {
		EasySQL sql = new EasySQL("SELECT COUNT(CONTRACT_NUMBER) FROM WECHATAPI.WECHAT_SETTLED_COMPENSATION");
		sql.append(maxUpdateDate, "WHERE ETL_DATE >= ?");
		return EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_CX_GQBI_NAME).queryForInt(sql.getSQL(), sql.getParams());
	}

	/**
	 * 转义数据中包含'的文本
	 * @param content
	 * @return
	 */
	private static String escapeString(String content) {
		if(StringUtils.isNotBlank(content)) {
			return content.replaceAll("'", "\\\\'");
		}
		return content;
	}

	/**
	 * 获取当天剩余秒数
	 * @param currentDate
	 * @return
	 */
	public static Integer getDayRemainingTime(Date currentDate) {
	    LocalDateTime midnight = LocalDateTime.ofInstant(currentDate.toInstant(),
	        ZoneId.systemDefault()).plusDays(1).withHour(0).withMinute(0)
	        .withSecond(0).withNano(0);
	    LocalDateTime currentDateTime = LocalDateTime.ofInstant(currentDate.toInstant(),
	        ZoneId.systemDefault());
	    long seconds = ChronoUnit.SECONDS.between(currentDateTime, midnight);
	    return (int) seconds;
	}
}
