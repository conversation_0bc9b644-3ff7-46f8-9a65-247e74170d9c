package com.yunqu.cc.mixgw.inf.thread;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.SQLRecoverableException;
import java.sql.SQLTimeoutException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.base.QueryFactory;
import com.yunqu.cc.mixgw.util.BiSyncLogUtils;

public class SynRetailInfoUtil {

	private static Logger logger = CommonLogger.getLogger("bi-system-syn");
	
	// 是否正在运行
	private volatile boolean isRun = false;
	
	private static class SynRetailInfoUtilHoler {
		private static SynRetailInfoUtil instance = new SynRetailInfoUtil();
	}

	
	public static SynRetailInfoUtil getInstance() {
		return SynRetailInfoUtilHoler.instance;
	}	
	
	public JSONObject synRetailInfo(String schema, String dateId, boolean isRepeat) {
		JSONObject result = new JSONObject();
		if(isRun) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 正在同步数据，不可重复执行");
			result.put("respDesc", "正在同步数据，不可重复执行");
			result.put("respCode", GWConstants.RET_CODE_SUCCESS);
			return result;
		}
		long startTime = System.currentTimeMillis();
		try {
			isRun = true;
			
			String maxUpdateDate = dateId;
			String lastDate = "";
			if(!isRepeat) {
				EasySQL sql = new EasySQL("select min(LAST_ETL_DATE) from (");
				sql.append("SELECT T.PROGRAM_NAME, MAX(T.PERIOD_ID) AS LAST_ETL_DATE");
				sql.append("FROM BLADM.ETL_AUDIT_TRAIL T WHERE T.PROGRAM_NAME IN('SP_ODS_CC_RETAIL_CONTRACT_DTL',");
				sql.append("'SP_BL_CONTRACT_DIM',");
				sql.append("'SP_BL_CUSTOMER_DIM',");
				sql.append("'SP_ODS_RETAIL_CONTRACT_PERSON',");
				sql.append("'SP_BL_FINANCIAL_CONSULT_DIM',");
				sql.append("'SP_BL_APPLICATION_DIM',");
				sql.append("'SP_CMS_CONTRACT_CONFIGURATION',");
				sql.append("'SP_CMS_ID_CARD_TYPE',");
				sql.append("'SP_ODS_RTL_CON_RESTRUCTURE_DTL') AND T.LOG_TEXT = 'Program Completed!'");
				sql.append("GROUP BY T.PROGRAM_NAME");
				sql.append(") t");
				lastDate = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_CX_GQBI_NAME).queryForString(sql.getSQL(), sql.getParams());
				
				if(StringUtils.isBlank(lastDate) || !StringUtils.equals(lastDate, DateUtil.addDay("yyyyMMdd", DateUtil.getCurrentDateStr("yyyyMMdd"), -1)) || StringUtils.equals(CacheUtil.get("CURRENT_DAY_SYN_RETAIL_INFO"), "Y")) {
					logger.info(CommonUtil.getClassNameAndMethod(this) + " BI系统数据暂未更新或当日已更新");
					result.put("respDesc", "BI系统数据暂未更新或当日已更新");
					result.put("respCode", GWConstants.RET_CODE_SUCCESS);
					BiSyncLogUtils.saveSyncLog(Constants.RETAIL_ENT_ID, schema, Constants.SYNC_COMMAND_01, startTime, Constants.SYNC_RESULT_01, "不满足同步条件");
					return result;
				}

				// 查询出最近一次更新时间
				maxUpdateDate = QueryFactory.getReadQuery().queryForString("SELECT MAX(PERIOD_ID) FROM " + schema + ".CX_GQ_RETAIL_INFO", new Object [] {});
			}
			
			int totalCount = getNeedSynDataCount(maxUpdateDate);
			if(totalCount < 1) {
				logger.info(CommonUtil.getClassNameAndMethod(this) + " 暂无待同步记录");
				result.put("respDesc", "暂无待同步记录");
				result.put("respCode", GWConstants.RET_CODE_SUCCESS);
				return result;
			}
			// 清空历史表记录
			QueryFactory.getWriteQuery().execute("TRUNCATE TABLE " + schema + ".CX_GQ_RETAIL_INFO_TMP", new Object[] {});
			
			// 同步数据至临时表
			int synIndex = readRetailInfoList(maxUpdateDate, totalCount, schema);
			
			// 将现有数据同步到CX_GQ_RETAIL_INFO2表
			QueryFactory.getWriteQuery().execute("TRUNCATE TABLE " + schema + ".CX_GQ_RETAIL_INFO2");
			QueryFactory.getWriteQuery().execute("insert into " + schema + ".CX_GQ_RETAIL_INFO2 select * from " + schema + ".CX_GQ_RETAIL_INFO");
			
			synRetailTable(schema);
			result.put("respDesc", "同步零售客服信息成功");
			result.put("respCode", GWConstants.RET_CODE_SUCCESS);
			long endTime = System.currentTimeMillis();
			CacheUtil.put("CURRENT_DAY_SYN_RETAIL_INFO", "Y", getDayRemainingTime(new Date()));
			logger.info(CommonUtil.getClassNameAndMethod(this) + " 同步零售客服信息成功，lastDate<" + lastDate + "> synIndex<" + synIndex + ">，耗时:" + (endTime - startTime));
			BiSyncLogUtils.saveSyncLog(Constants.RETAIL_ENT_ID, schema, Constants.SYNC_COMMAND_01, startTime, Constants.SYNC_RESULT_02, "同步成功，最大业务日期:" + lastDate);
		} catch (Exception e) {
			result.put("respDesc", "同步零售客服信息失败");
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 同步零售客服信息失败 error:" + e.getMessage(), e);
			BiSyncLogUtils.saveSyncLog(Constants.RETAIL_ENT_ID, schema, Constants.SYNC_COMMAND_01, startTime, Constants.SYNC_RESULT_03, "同步失败, error:" + e.getMessage());
		} finally {
			isRun = false;
		}
		return result;
	}
	
	public JSONObject synRetailInfo(String schema) {
		return synRetailInfo(schema, DateUtil.getCurrentDateStr("yyyyMMdd"), false);
	}
	
	
	/**
	 * 同步临时表数据至数据表
	 * @param schema
	 * @throws Exception 
	 */
	private void synRetailTable(String schema) throws SQLException {
		CacheUtil.put(Constants.IS_SYN_RETAIL_INFO, "Y");
		long startTime = System.currentTimeMillis();
		try {
			EasyQuery wirteQuery = QueryFactory.getWriteQuery();
			while(true) {
				// 删除临时表中已存在的合同信息
				String deleteSql = "DELETE FROM " + schema + ".CX_GQ_RETAIL_INFO WHERE EXISTS (SELECT CONTRACT_NUMBER FROM " + schema + ".CX_GQ_RETAIL_INFO_TMP WHERE CX_GQ_RETAIL_INFO.CONTRACT_NUMBER = CX_GQ_RETAIL_INFO_TMP.CONTRACT_NUMBER) LIMIT " + 2000;
				int updateCount = wirteQuery.executeUpdate(deleteSql);
				if(updateCount == 0) {
					break;
				}
			}
			
			String selectIntoSql = "INSERT INTO " + schema + ".CX_GQ_RETAIL_INFO SELECT * FROM " + schema + ".CX_GQ_RETAIL_INFO_TMP";
			wirteQuery.executeUpdate(selectIntoSql);
		} catch (SQLException e) {
			CacheUtil.put(Constants.IS_SYN_RETAIL_INFO, "N");
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 同步临时表数据至实体表失败 error:" + e.getMessage(), e);
			throw new SQLException();
		}
		CacheUtil.put(Constants.IS_SYN_RETAIL_INFO, "N");
		long endTime = System.currentTimeMillis();
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 同步临时表数据至实体表成功，耗时:" + (endTime - startTime));
	}
	
	/**
	 * 获取BI视图数据
	 * @param maxUpdateDate 最后更新日期
	 * @param totalCount 总条数
	 * @param schema 数据库名称
	 * @return
	 * @throws SQLException
	 */
	private int readRetailInfoList(String maxUpdateDate, int totalCount, String schema) throws SQLException {
		int synIndex = 0;
		long startTime = System.currentTimeMillis();
		List<JSONObject> dataList = new ArrayList<JSONObject>();
		Connection conn = null;
	    PreparedStatement stmt = null;
	    ResultSet rs = null;
	    boolean isError = false;
    	String lastContractNumber = null;
    	
    	// 循环次数，防止无限制循环
    	int whileIndex = 0;
    	// 读取数据超时，需要遍历读取
    	while(whileIndex < 15) {
    		StringBuilder sqlStr = new StringBuilder("SELECT t.* FROM CC_RETAIL_CONTRACT_DETAIL_V t where 1 = 1");
    		if(StringUtils.isNotBlank(maxUpdateDate)) {
    			sqlStr.append(" AND PERIOD_ID >= '" + maxUpdateDate + "'");
    		}
    		if(StringUtils.isNotBlank(lastContractNumber)) {
    			sqlStr.append(" AND CONTRACT_NUMBER > '" + lastContractNumber + "'");
    		}
    		sqlStr.append(" ORDER BY CONTRACT_NUMBER");
    		try {
	    		conn = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_CX_GQBI_NAME).getConnection();
	    		stmt = conn.prepareStatement(sqlStr.toString(), ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
	    		stmt.setFetchSize(100);
	    		stmt.setMaxRows(totalCount);
	    		stmt.setQueryTimeout(1800);
	    		rs = stmt.executeQuery();
	    		int i = 0;
	    		String insertSql = "INSERT INTO " +schema + ".CX_GQ_RETAIL_INFO_TMP ";
	    		while(rs.next()){
	    			JSONObject dataJson = new JSONMapperImpl().mapRow(rs,2);
	    			
	    			if(StringUtils.equals(lastContractNumber, rs.getString("CONTRACT_NUMBER"))) {
	    				continue;
	    			}
	    			lastContractNumber = rs.getString("CONTRACT_NUMBER");
	    			dataList.add(dataJson);
	    			if((i!=0 && i%500 == 0)) {
	    				// 插入临时表
	    				insertRetailTmp(insertSql, dataList);
	    				// 清空data对象
	    				dataList.clear();
	    			}
	    			i++;
	    			synIndex++;
	    		}
	    		insertRetailTmp(insertSql, dataList);
	    		break;
    		} catch (SQLRecoverableException e) {
    			// 此异常为超时异常，关闭连接，继续读取循环
    			logger.info(CommonUtil.getClassNameAndMethod(this) + " BI数据读取超时，截至合同ID为lastContractNumber<" + lastContractNumber + ">，进行循环");
    			continue;
    		} catch(SQLTimeoutException e) {
    			// 此异常为超时异常，关闭连接，继续读取循环
    			logger.info(CommonUtil.getClassNameAndMethod(this) + " BI数据读取超时，截至合同ID为lastContractNumber<" + lastContractNumber + ">，进行循环");
    			continue;
    		} catch(Exception e) {
    			logger.error(CommonUtil.getClassNameAndMethod(this) + " 同步BI数据失败 error:" + e.getMessage(), e);
    	    	isError = true;
    	    	break;
    		} finally {
    			try {
    				if (rs != null) {
    					rs.close();
    				}
    			} catch (Exception ex) {
    				logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + ex.getMessage(), ex);
    			}
    			try {
	    			if (stmt != null) {
	    				stmt.close();
	    			}
    			} catch (Exception ex) {
    				logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + ex.getMessage(), ex);
    			}
    			try {
    				if (conn != null) {
    					conn.close();
    				}
    			} catch (Exception ex) {
    				logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + ex.getMessage(), ex);
    			}
    			whileIndex++;
    		}
    	}
	    if(isError) {
	    	throw new SQLException();
	    }
	    long endTime = System.currentTimeMillis();
	    logger.info(CommonUtil.getClassNameAndMethod(this) + " 读取BI系统数据信息成功，耗时：" + (endTime - startTime));
	    return synIndex;
	}
	
	private void insertRetailTmp(String insertSql, List<JSONObject> dataList) throws SQLException {
		try {
			int dataSize = dataList.size();
			if(dataSize == 0) {
				return;
			}

			StringBuilder sqlStr = new StringBuilder(insertSql);
			
			List<Object> paramList = new ArrayList<Object>();
			
			for(int j = 0; j < dataSize; j++ ) {
				JSONObject dataJson = dataList.get(j);
				
				paramList.add(dataJson.getString("NID_NBR"));
				paramList.add(dataJson.getString("GENDER_FLAG"));
				paramList.add(dataJson.getString("ID_TYPE_ID"));
				paramList.add(dataJson.getString("CUSTOMER_NAME_CHS"));
				paramList.add(escapeString(dataJson.getString("EMPLOYER_NAME")));
				paramList.add(escapeString(dataJson.getString("MAIL_ADDRESS_NAME_CHS")));
				paramList.add(escapeString(dataJson.getString("BANK_NAME_CHS")));
				paramList.add(dataJson.getString("ACCOUNT_NUMBER"));
				paramList.add(dataJson.getString("MOBILE_PHONE"));
				paramList.add(dataJson.getString("REQUEST_STATUS_DESC"));
				paramList.add(dataJson.getString("APPLICATION_NUMBER"));
				paramList.add(dataJson.getString("CONTRACT_NUMBER"));
				paramList.add(escapeString(dataJson.getString("DEALER_NAME_CHS")));
				paramList.add(dataJson.getString("FINANCIAL_ADVISOR_NAME"));
				paramList.add(dataJson.getString("FINANCIAL_ADVISOR_PHONE"));
				paramList.add(escapeString(dataJson.getString("FINANCIAL_PRODUCT_NAME")));
				paramList.add(dataJson.getString("EFFECTIVE_SETTLE_DATE"));
				paramList.add(dataJson.getString("ER_AMOUNT"));
				paramList.add(dataJson.getString("LAST_SETTLE_DATE"));
				paramList.add(dataJson.getString("LAST_SETTLE_AMT"));
				paramList.add(dataJson.getString("CONTRACT_ACTIVATION_DATE"));
				paramList.add(dataJson.getString("CONTRACT_END_DATE"));
				paramList.add(dataJson.getString("CONTRACT_CLOSING_DATE"));
				paramList.add(dataJson.getString("CONTRACT_TRM"));
				paramList.add(dataJson.getString("SETTLED_TRM"));
				paramList.add(dataJson.getString("UN_SETTLED_TRM"));
				paramList.add(dataJson.getString("BANK_CARD_TAIL_NO"));
				paramList.add(dataJson.getString("DUE_DATE"));
				paramList.add(dataJson.getString("RENTAL_AMT"));
				paramList.add(dataJson.getString("FINANCED_AMT"));
				paramList.add(dataJson.getString("INTEREST_TOTAL_AMT"));
				paramList.add(dataJson.getString("CUSTOMER_RATE"));
				paramList.add(dataJson.getString("PRINCIPAL_OUTSTAND_AMT"));
				paramList.add(dataJson.getString("INTEREST_OUTSTAND_AMT"));
				paramList.add(dataJson.getString("WRITE_OFF_FLAG"));
				paramList.add(dataJson.getString("MORTGAGE_FREE_IND"));
				paramList.add(dataJson.getString("INTEREST_CURRENT_AMT"));
				paramList.add(dataJson.getString("ER_BREAK_AMT"));
				paramList.add(escapeString(dataJson.getString("ASSET_MODEL_DESC")));
				paramList.add(dataJson.getString("REQUEST_STATUS"));
				paramList.add(dataJson.getString("WECHAT_APPLICATION_NUMBER"));
				paramList.add(dataJson.getString("VRC_NO"));
				paramList.add(dataJson.getString("PRODUCING_AREA"));
				paramList.add(dataJson.getString("NUMBER_PLATE"));
				paramList.add(dataJson.getString("ENGINENO"));
				paramList.add(dataJson.getString("VINNO"));
				paramList.add(dataJson.getString("EXP_COMPANY"));
				paramList.add(dataJson.getString("EXP_LIAISON"));
				paramList.add(dataJson.getString("EXP_ADDRESS"));
				paramList.add(dataJson.getString("EXP_PHONE_NUM"));
				paramList.add(dataJson.getString("EXP_NUM"));
				paramList.add(dataJson.getString("EXP_DATE_ID"));
				paramList.add(dataJson.getString("STORAGE_FLAG"));
				paramList.add(dataJson.getString("GAP_COST_IND"));
				paramList.add(dataJson.getString("PILOT_CODE"));
				paramList.add(dataJson.getString("PR_INT_OUTSTAND_AMT"));
				paramList.add(dataJson.getString("ER_SAVE_AMT"));
				paramList.add(dataJson.getString("LICENSEPLATE"));
				paramList.add(dataJson.getString("OVERDUE_DD"));
				paramList.add(dataJson.getString("PERIOD_ID"));
				paramList.add(dataJson.getString("EXTEND_FLAG"));
				paramList.add(dataJson.getString("UNIONID"));
				paramList.add(dataJson.getString("SHARE_INFORMATION_IND"));
				paramList.add(dataJson.getString("RECEIPT_DATE"));
				paramList.add(dataJson.getString("RECEIPT_AMT"));
				paramList.add(dataJson.getString("FPG"));
				
				sqlStr.append(" SELECT ");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?,");
				sqlStr.append("?");
				
				if((j + 1) != dataSize) {
					sqlStr.append(" UNION ALL ");
				}
			}
			QueryFactory.getWriteQuery().execute(sqlStr.toString(), paramList.toArray());
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 批量导入数据至临时表失败 error: " + e.getMessage(), e);
			throw new SQLException();
		}
	}

	/**
	 * 获取待同步数据条数
	 * @param maxUpdateDate 最近一次更新日期
	 * @return
	 * @throws SQLException
	 */
	private int getNeedSynDataCount(String maxUpdateDate) throws SQLException {
		EasySQL sql = new EasySQL("SELECT COUNT(CONTRACT_NUMBER) FROM CC_RETAIL_CONTRACT_DETAIL_V");
		sql.append(maxUpdateDate, "WHERE PERIOD_ID >= ?");
		return EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_CX_GQBI_NAME).queryForInt(sql.getSQL(), sql.getParams());
	}
	
	/**
	 * 转义数据中包含'的文本
	 * @param content
	 * @return
	 */
	private static String escapeString(String content) {
		if(StringUtils.isNotBlank(content)) {
			return content.replaceAll("'", "\\\\'");
		}
		return content;
	}
	
	/**
	 * 获取当天剩余秒数
	 * @param currentDate
	 * @return
	 */
	public static Integer getDayRemainingTime(Date currentDate) {
	    LocalDateTime midnight = LocalDateTime.ofInstant(currentDate.toInstant(),
	        ZoneId.systemDefault()).plusDays(1).withHour(0).withMinute(0)
	        .withSecond(0).withNano(0);
	    LocalDateTime currentDateTime = LocalDateTime.ofInstant(currentDate.toInstant(),
	        ZoneId.systemDefault());
	    long seconds = ChronoUnit.SECONDS.between(currentDateTime, midnight);
	    return (int) seconds;
	}
	
	public static void main(String[] args) {
		JSONObject dataJson = new JSONObject();
		
		Object o [] = new Object[] {
				dataJson.getString("NID_NBR"),
				dataJson.getString("GENDER_FLAG"),
				dataJson.getString("ID_TYPE_ID"),
				dataJson.getString("CUSTOMER_NAME_CHS"),
				escapeString(dataJson.getString("EMPLOYER_NAME")),
				escapeString(dataJson.getString("MAIL_ADDRESS_NAME_CHS")),
				escapeString(dataJson.getString("BANK_NAME_CHS")),
				dataJson.getString("ACCOUNT_NUMBER"),
				dataJson.getString("MOBILE_PHONE"),
				dataJson.getString("REQUEST_STATUS_DESC"),
				dataJson.getString("APPLICATION_NUMBER"),
				dataJson.getString("CONTRACT_NUMBER"),
				escapeString(dataJson.getString("DEALER_NAME_CHS")),
				dataJson.getString("FINANCIAL_ADVISOR_NAME"),
				dataJson.getString("FINANCIAL_ADVISOR_PHONE"),
				escapeString(dataJson.getString("FINANCIAL_PRODUCT_NAME")),
				dataJson.getString("EFFECTIVE_SETTLE_DATE"),
				dataJson.getString("ER_AMOUNT"),
				dataJson.getString("LAST_SETTLE_DATE"),
				dataJson.getString("LAST_SETTLE_AMT"),
				dataJson.getString("CONTRACT_ACTIVATION_DATE"),
				dataJson.getString("CONTRACT_END_DATE"),
				dataJson.getString("CONTRACT_CLOSING_DATE"),
				dataJson.getString("CONTRACT_TRM"),
				dataJson.getString("SETTLED_TRM"),
				dataJson.getString("UN_SETTLED_TRM"),
				dataJson.getString("BANK_CARD_TAIL_NO"),
				dataJson.getString("DUE_DATE"),
				dataJson.getString("RENTAL_AMT"),
				dataJson.getString("FINANCED_AMT"),
				dataJson.getString("INTEREST_TOTAL_AMT"),
				dataJson.getString("CUSTOMER_RATE"),
				dataJson.getString("PRINCIPAL_OUTSTAND_AMT"),
				dataJson.getString("INTEREST_OUTSTAND_AMT"),
				dataJson.getString("WRITE_OFF_FLAG"),
				dataJson.getString("MORTGAGE_FREE_IND"),
				dataJson.getString("INTEREST_CURRENT_AMT"),
				dataJson.getString("ER_BREAK_AMT"),
				escapeString(dataJson.getString("ASSET_MODEL_DESC")),
				dataJson.getString("REQUEST_STATUS"),
				dataJson.getString("WECHAT_APPLICATION_NUMBER"),
				dataJson.getString("VRC_NO"),
				dataJson.getString("PRODUCING_AREA"),
				dataJson.getString("NUMBER_PLATE"),
				dataJson.getString("ENGINENO"),
				dataJson.getString("VINNO"),
				dataJson.getString("EXP_COMPANY"),
				dataJson.getString("EXP_LIAISON"),
				dataJson.getString("EXP_ADDRESS"),
				dataJson.getString("EXP_PHONE_NUM"),
				dataJson.getString("EXP_NUM"),
				dataJson.getString("EXP_DATE_ID"),
				dataJson.getString("STORAGE_FLAG"),
				dataJson.getString("GAP_COST_IND"),
				dataJson.getString("PILOT_CODE"),
				dataJson.getString("PR_INT_OUTSTAND_AMT"),
				dataJson.getString("ER_SAVE_AMT"),
				dataJson.getString("LICENSEPLATE"),
				dataJson.getString("OVERDUE_DD"),
				dataJson.getString("PERIOD_ID"),
				dataJson.getString("EXTEND_FLAG"),
				dataJson.getString("UNIONID"),
				dataJson.getString("SHARE_INFORMATION_IND"),
				dataJson.getString("RECEIPT_DATE"),
				dataJson.getString("RECEIPT_AMT"),
				dataJson.getString("FPG")
			};
		
		StringBuilder sqlStr = new StringBuilder("");
		
		sqlStr.append(" SELECT ");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?,");
		sqlStr.append("?");
		
		System.out.println(o.length);
		
		System.out.println(sqlStr.toString());
		
		int aSum = 0;
		int bSum = 0;
		for(int i = 0; i < sqlStr.length(); i++ ) {
			char c = sqlStr.charAt(i);
			if("?".equals(new String(new char[]{c}))) {
				aSum++;
			}
			if(",".equals(new String(new char[]{c}))) {
				bSum++;
			}
		}
		System.out.println(aSum);
		System.out.println(bSum);
	}
	
}
