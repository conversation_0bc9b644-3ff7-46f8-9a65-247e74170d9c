package com.yunqu.cc.smsgw.dao;

import java.sql.SQLException;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.db.impl.JSONMapperImpl;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yunqu.cc.smsgw.base.AppDaoContext;
import com.yunqu.cc.smsgw.base.CommonLogger;
import com.yunqu.cc.smsgw.base.Constants;
import com.yunqu.cc.smsgw.base.QueryFactory;
import com.yunqu.cc.smsgw.model.SmsReceipt;

public class SmsInfoDao extends AppDaoContext {

	private Logger logger = CommonLogger.logger;

	/*public JSONObject channelMessage() {
		EasySQL sql = this.getEasySQL("select row_number() over(ORDER BY t.ID) numindex , t.* ");
		sql.append("from " + Constants.SCHEMA + ".C_SMS_CHANNEL t ");
		logger.debug("获取短信渠道内容,sql=" + sql.getSQL() + "{" + JSON.toJSONString(sql.getParams()) + "}");
		JSONObject obj = this.queryForPageList(sql.getSQL(), sql.getParams(), null);
		return obj;
	}*/

	/**
	 * 插入短信回执
	 * 
	 * @param sr
	 * @return
	 * @throws Exception
	 */
	public String insertSmsReceipt(SmsReceipt sr,String schema) throws Exception {
		if (StringUtils.isBlank(sr.getBakup())) {
			sr.setBakup("");
		}
		if (StringUtils.isBlank(sr.getSmsInfoId())) {
			sr.setSmsInfoId("");
		}
		String id = IDGenerator.getDefaultNUMID();
		String currTime = DateUtil.getCurrentDateStr();

		StringBuffer sql = new StringBuffer();
		sql.append(" INSERT INTO  " + schema + ".C_SMS_RECEIPT(ID,TYPE,CONTENT,BAKUP,CREATE_TIME,SMS_MSG_ID,SMS_INFO_ID,STATUS,RECEIVER,SENDER,CHANNEL_ID,BUSI_ORDER_ID,ENT_ID)  ");
		sql.append(" VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)");

		Object[] args = new Object[] { id, sr.getType(), sr.getContent(), sr.getBakup(), currTime, sr.getSmsMsgId(), sr.getSmsInfoId(), sr.getStatus(), sr.getReceiver(), sr.getSender(), sr.getChannelId(),sr.getBusiOrderId(),sr.getEntId() };
		QueryFactory.getWriteQuery().execute(sql.toString(), args);
		return id;
	}

	/**
	 * 根据短信id，查询短信记录
	 * 
	 * @param smsId
	 * @return
	 */
	public JSONObject findSmsBySmsId(String schema,String smsId) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append(" SELECT * FROM " + schema + ".C_SMS_SEND_RECORD WHERE ID = '" + smsId + "'");
			logger.debug(CommonUtil.getClassNameAndMethod(this) + " SQL:" + sql.toString());
			return QueryFactory.getReadQuery().queryForRow(sql.toString(), null,new JSONMapperImpl());
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 查询出错", e);
		}
		return null;
	}
	
	
	public List<JSONObject> findSmsBySmsSendId(String schema,String smsSendId){
		try {
			StringBuffer sql = new StringBuffer();
			sql.append(" select * from "+ schema + ".C_SMS_SEND_RECORD where SMS_SEND_ID= '" + smsSendId + "'");
			logger.debug(CommonUtil.getClassNameAndMethod(this) + " SQL:" + sql.toString());
			return QueryFactory.getReadQuery().queryForList(sql.toString(), null,new JSONMapperImpl());
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 查询出错", e);
		}
		return null;
	} 
	

	/**
	 * 根据短信id，结合短信发送渠道关联记录表，找到可以自动轮发的下一个通道(短信发送失败的情况下，查询下一个通道时，可以使用该接口)
	 * 
	 * @param msgId
	 * @return
	 */
	public JSONObject findAlarmChannelBySmsBatch(String msgId,String schema) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append(" SELECT T1.* FROM " + schema
					+ ".C_SMS_CHANNEL T1 WHERE T1.TYPE='1' AND IS_AUTO='1' AND STATUS='1'  ");
			sql.append(" AND NOT EXISTS(  SELECT 1 FROM " + schema
					+ ".C_SMS_CHANNEL_RECORD T2 WHERE T2.CHANNEL_ID = T1.ID AND T2.MSG_ID='" + msgId + "' ) ");
			return QueryFactory.getWriteQuery().queryForRow(sql.toString(), null,new JSONMapperImpl());
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 查询出错", e);
		}
		return null;

	}

	/**
	 * 查询短信发送渠道关联记录,必须指定渠道id、短信id、发送批次
	 * 
	 * @param channelId
	 *            渠道id
	 * @param msgId
	 *            短信id
	 * @param smsSendBatch
	 *            发送批次
	 * @return
	 */
	public JSONObject findSmsChannelRecord(String channelId, String msgId, int smsSendBatch,String schema) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append(" SELECT * FROM " + schema + ".C_SMS_CHANNEL_RECORD WHERE  CHANNEL_ID = '" + channelId
					+ "' AND MSG_ID='" + msgId + "' AND SEND_BATCH = " + smsSendBatch);
			logger.debug(CommonUtil.getClassNameAndMethod(this) + " SQL:" + sql.toString());
			return QueryFactory.getWriteQuery().queryForRow(sql.toString(), null,new JSONMapperImpl());
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 查询出错", e);
		}
		return null;
	}

	/**
	 * 添加短信发送渠道关联记录
	 * 
	 * @param channelId
	 *            渠道id
	 * @param msgId
	 *            短信id
	 * @param smsSendBatch
	 *            发送批次
	 * @param sendTims
	 *            发送次数
	 * @return
	 * @throws Exception
	 */
	public String insertSmsChannelRecord(String channelId, String msgId, int smsSendBatch, int sendTims,String schema)
			throws Exception {

		try {
			StringBuffer sql = new StringBuffer();
			sql.append(" INSERT INTO  " + schema
					+ ".C_SMS_CHANNEL_RECORD(SMS_CHANNEL_RECORDID,CHANNEL_ID,MSG_ID,SEND_TIMES,SEND_BATCH,CREATE_TIME)  ");
			sql.append(" VALUES (?,?,?,?,?,?)");
			String id = IDGenerator.getDefaultNUMID();
			String currTime = DateUtil.getCurrentDateStr();
			Object[] args = new Object[] { id, channelId, msgId, smsSendBatch, sendTims, currTime };
			QueryFactory.getWriteQuery().execute(sql.toString(), args);
			return id;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 操作出错", e);
		}
		return null;
	}

	/**
	 * 更新短信发送渠道关联记录的发送次数
	 * 
	 * @param smsChannelRecordId
	 *            短信发送渠道关联记录id
	 * @param sendTimes
	 *            要增加的短信发送次数
	 * @throws Exception
	 */
	public void updateSmsChannelRecord(String smsChannelRecordId, int sendTimes,String schema) throws Exception {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append(" UPDATE " + schema + ".C_SMS_CHANNEL_RECORD SET SEND_TIMES = SEND_TIMES + "
					+ sendTimes + "  WHERE SMS_CHANNEL_RECORDID=? ");
			Object[] args = new Object[] { smsChannelRecordId };
			QueryFactory.getWriteQuery().execute(sql.toString(), args);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 操作出错", e);
		}
	}

	/**
	 * 找到默认的发送渠道
	 * 
	 * @return
	 */
	public JSONObject findAlarmChannel(String entId,String busiOrderId,String schema) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append(" SELECT * FROM " + schema
					+ ".C_SMS_CHANNEL WHERE TYPE=? AND DEFAULT_CHANEL = ? AND STATUS=? AND EP_CODE = ? AND BUSI_ORDER_ID = ? ");
			logger.debug(CommonUtil.getClassNameAndMethod(this) + " SQL:" + sql.toString());
			return QueryFactory.getWriteQuery().queryForRow(sql.toString(), new Object[]{"01","1","1",entId,busiOrderId}, new JSONMapperImpl());
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 查询出错", e);
		}
		return null;
	}
	
	/**
	 * 根据渠道CODE查找渠道信息
	 * 
	 * @return
	 */
	public JSONObject getChannelByCode(String code,String entId,String busiOrderId,String schema) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append(" SELECT * FROM " + schema
					+ ".C_SMS_CHANNEL WHERE CODE = ? AND EP_CODE = ? AND BUSI_ORDER_ID = ? ");
			logger.debug(CommonUtil.getClassNameAndMethod(this) + " SQL:" + sql.toString());
			return QueryFactory.getWriteQuery().queryForRow(sql.toString(), new Object[]{code,entId,busiOrderId}, new JSONMapperImpl());
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 查询出错", e);
		}
		return null;
	}

	/**
	 * 找到id找到对应的告警渠道
	 * 
	 * @param smsId
	 * @return
	 */
	public JSONObject findAlarmChannelById(String channelId,String schema) {

		try {
			StringBuffer sql = new StringBuffer();
			sql.append(" SELECT * FROM " + schema + ".C_SMS_CHANNEL T1 WHERE  ");
			sql.append(" T1.ID = '" + channelId + "' ");
			return QueryFactory.getWriteQuery().queryForRow(sql.toString(), new Object[]{}, new JSONMapperImpl());
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 查询出错", e);
		}
		return null;
	}

	/**
	 * 查询所有渠道
	 * 
	 * @param smsId
	 * @return
	 */
	public List<JSONObject> findChannelAll(String schema) {

		try {
			StringBuffer sql = new StringBuffer();
			sql.append(" SELECT ID AS id,CODE AS code,NAME AS name,TYPE AS type,URL AS url,DEFAULT_CHANNEL AS defaultChannel,ACCOUNT AS account,PASSWD AS password,PARAMS AS params FROM " + schema + ".C_SMS_CHANNEL T1  ");
			return QueryFactory.getWriteQuery().queryForList(sql.toString(), new Object[]{},new JSONMapperImpl());
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 查询出错", e);
		}
		return null;
	}

	/**
	 * 根据模板编号查询模板内容
	 * 
	 * @param code
	 * @return
	 */
	public JSONObject findTemplateByCode(String code,String schema) {
		try {
			String sql = "SELECT CONTENT FROM " + schema + ".C_SMS_TEMPLATE WHERE CODE = '" + code + "'";
			return QueryFactory.getWriteQuery().queryForRow(sql, new Object[]{},new JSONMapperImpl());
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 查询出错", e);
		}
		return null;
	}

	/**
	 * 查询出要发送的下行短信
	 * 
	 * @return
	 * @throws SQLException
	 */
	public List<JSONObject> findNeedSendSms(String schema,String epCode,String busiOrderId) throws SQLException {
		try {
			StringBuffer sql = new StringBuffer();
			String date = DateUtil.getCurrentDateStr();
			sql.append(" SELECT * FROM " + schema + ".C_SMS_SEND_RECORD WHERE EP_CODE=? AND BUSI_ORDER_ID=? AND SEND_TIME<=?  AND SEND_STATUS = ?  AND SEND_TIMES < ? " );
			return QueryFactory.getWriteQuery().queryForList(sql.toString(), new Object[]{epCode,busiOrderId,date,Constants.SMS_SEND_STATUS_WAIT_SEND,Constants.MSG_SEND_MAX_TIMES}, new JSONMapperImpl());
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 查询出错", e);
		}
		return null;
	}
	
	/**
	 * 查询出群发批次
	 * 
	 */
	public List<JSONObject> findCrowdNeedSendSms(String schema,String epCode,String busiOrderId){
		try {
			StringBuffer sql = new StringBuffer();
			sql.append(" select C1.ID AS CID from "+ schema + ".C_SMS_INFO C1  where EP_CODE=? AND BUSI_ORDER_ID=? AND  (C1.STATUS='1' or C1.STATUS='5')  AND exists( ");
			sql.append(" SELECT SMS_SEND_ID from "+ schema +".C_SMS_SEND_RECORD C2 ");
			sql.append(" WHERE C1.ID=C2.SMS_INFO_ID  AND C2.SEND_STATUS = '1' ) ");
			return QueryFactory.getWriteQuery().queryForList(sql.toString(), new Object[]{epCode,busiOrderId}, new JSONMapperImpl());
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 查询出错", e);
		}
		return null;
	}
	
	public List<JSONObject> getSmsSendRecord(String schema,String SendId,String busiOrderId,String epCode){
		try {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from "+ schema +".C_SMS_SEND_RECORD");
		sql.append(" where SMS_INFO_ID = ? AND SEND_TIME<=?   AND SEND_STATUS = ?  AND SEND_TIMES < ? AND BUSI_ORDER_ID = ? AND EP_CODE = ?  ");
		return QueryFactory.getWriteQuery().queryForList(sql.toString(), new Object[] {SendId,DateUtil.getCurrentDateStr(),Constants.SMS_SEND_STATUS_WAIT_SEND,Constants.MSG_SEND_MAX_TIMES,busiOrderId,epCode}, new JSONMapperImpl());
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 查询出错", e);
		}
		return null;
	}
	

	/**
	 * 修改下行短信的状态
	 * 
	 * @param smsId
	 *            下行短信ID
	 * @param status
	 *            短信播发状态：1-待发送 2-发送中 3-发送成功 4-发送失败
	 * @param smsSendId
	 *            短信下发id，由运营商返回，可以为空
	 * @param channelId
	 *            短信下发渠道id，为空时不做修改
	 * @param sendTimes
	 *            增加的短信发送此处
	 * @param sendResult
	 *            发送结果
	 * @throws Exception
	 */
	public void updateSmsStatus(String smsId, String status, String smsSendId, String channelId, int sendTimes,
			String sendResult,String schema) throws Exception {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append(" UPDATE " + schema
					+ ".C_SMS_SEND_RECORD SET SEND_STATUS=? ,SEND_RESULT_DESC=?, SEND_TIMES = SEND_TIMES+ "
					+ sendTimes);
			if (StringUtils.isNotBlank(channelId)) {
				sql.append(" , CHANNEL_ID = '" + channelId + "' ");
			}
			if (StringUtils.isNotBlank(smsSendId)) {
				sql.append(" , SMS_SEND_ID = '" + smsSendId + "' ");
			}
			sql.append(" WHERE ID=?");
			Object[] args = new Object[] { status, sendResult, smsId };

			QueryFactory.getWriteQuery().execute(sql.toString(), args);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 操作出错", e);
		}
	}
	
	public void updateSmsInfoStatus(String id,String status,String schema) {
		try {
			StringBuffer sql= new StringBuffer();
			sql.append(" update "+ schema +".C_SMS_INFO set STATUS = ? where id=(select SMS_INFO_ID from "+ schema +".C_SMS_SEND_RECORD where id= ? ) ");
			Object[] args = new Object[] {status,id};
			QueryFactory.getWriteQuery().execute(sql.toString(), args);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 操作出错", e);
		}
	}
	
	
	
	
	
	
	
	/***
	 * 根据企业id查询订购id
	 * @throws SQLException 
	 */
	public String getBusiOrderId(String entId) throws SQLException {
		StringBuffer sql = new StringBuffer();
		sql.append("select BUSI_ORDER_ID FROM CC_BUSI_ORDER where ENT_ID = '"+ entId + "' ");
		JSONObject queryForRow = QueryFactory.getWriteQuery().queryForRow(sql.toString(), null,new JSONMapperImpl());
		return queryForRow.getString("BUSI_ORDER_ID");
	}
}