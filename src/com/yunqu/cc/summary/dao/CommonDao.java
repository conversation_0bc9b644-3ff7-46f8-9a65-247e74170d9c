package com.yunqu.cc.summary.dao;
import java.util.Calendar;
import java.util.Date;

import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.DBTypes;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.summary.base.AppDaoContext;
import com.yunqu.cc.summary.base.CommonLogger;
import com.yunqu.cc.summary.base.Constants;

@WebObject(name="common")
public class CommonDao extends AppDaoContext {
	private Logger logger = CommonLogger.logger;
	
	//角色字典
	@WebControl(name="roleDict", type=Types.DICT)
	public JSONObject roleDict(){
		EasySQL sql = this.getEasySQL("SELECT ROLE_ID,ROLE_NAME FROM "+getTableName("CC_ROLE")+" WHERE 1 = 1 ");
		sql.append(this.getBusiOrderId(),"AND BUSI_ORDER_ID = ? ");
		sql.append(this.getEntId(), "AND ENT_ID = ? ");
		sql.append("ORDER BY ROLE_TYPE");
		return getDictByQuery(sql.getSQL(), sql.getParams());
	}
	
	/**
	 * 有效任务字典 
	 * @return
	 */
	@WebControl(name="taskDict",type=Types.DICT)
	public JSONObject myTaskDict(){
		EasySQL sql=this.getEasySQL("SELECT TASK_ID,TASK_NAME FROM ").append(getTableName("CC_TASK")).append(" WHERE 1 = 1 ");
		sql.append(getEntId(),"AND ENT_ID = ? ");
		sql.append(getBusiOrderId(),"AND BUSI_ORDER_ID = ? ");
		sql.append("AND TASK_STATE IN (5,6,8)");
		sql.append("ORDER BY CREATE_TIME DESC");
		EasyQuery query=this.getQuery();
		query.setMaxRow(120);
		this.setQuery(query);
		return getDictByQuery(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="weekAgoDate",type=Types.TEXT)
	public JSONObject weekBegin(){
	  	Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.DATE, - 7);
        String day = EasyDate.dateToString(c.getTime(), "yyyy-MM-dd");
		return getJsonResult(day);
	}
	
	@WebControl(name="todayDate",type=Types.TEXT)
	public JSONObject todayBegin(){
		return getJsonResult(EasyDate.getCurrentDateString("yyyy-MM-dd"));
	}
	@WebControl(name="userName",type=Types.TEXT)
	public JSONObject userName(){
		return getJsonResult(getUserPrincipal().getNickName());
	}
	@WebControl(name="todayFirstTime",type=Types.TEXT)
	public JSONObject todayFirstTime(){
		return getJsonResult(EasyDate.getCurrentDateString("yyyy-MM-dd")+ " 00:00:00");
	}
	@WebControl(name="todayEndTime",type=Types.TEXT)
	public JSONObject todayEndTime(){
		return getJsonResult(EasyDate.getCurrentDateString("yyyy-MM-dd")+ " 23:59:59");
	}
	@WebControl(name="todayTime",type=Types.TEXT)
	public JSONObject todayTime(){
		return getJsonResult(EasyDate.getCurrentDateString());
	}
	/**
	 * 获取技能组字典
	 */
	@WebControl(name="skillDict",type=Types.DICT)
	public JSONObject skillDict(){
		EasySQL sql = this.getEasySQL("SELECT SKILL_GROUP_ID,SKILL_GROUP_NAME FROM "+getTableName("CC_SKILL_GROUP")+" WHERE 1 = 1 ");
		sql.append(this.getBusiOrderId(),"AND BUSI_ORDER_ID = ? ");
		sql.append(this.getEntId(), "AND ENT_ID = ? ");
		sql.append("voice", "AND SKILL_GROUP_TYPE = ? ");
		sql.append("ORDER BY IDX_ORDER");
		return this.getDictByQuery(sql.getSQL(), sql.getParams());
	}
	/**
	 * 获取接入号码字典
	 */
	@WebControl(name="cannelDict",type=Types.DICT)
	public JSONObject cannelDict(){
		EasySQL sql = this.getEasySQL("SELECT CHANNEL_KEY,CHANNEL_KEY FROM CC_CHANNEL WHERE 1=1 AND CHANNEL_TYPE = 4");
		sql.append(this.getEntId(), "AND ENT_ID = ? ");
		return this.getDictByQuery(sql.getSQL(), sql.getParams());
	}
	
	/**
	 * 坐席字典
	 */
	@WebControl(name="userDict",type=Types.DICT)
	public JSONObject userDict(){
		EasySQL sql = this.getEasySQL("SELECT T1.USER_ID,CONCAT(CONCAT(T1.AGENT_PHONE,'-'),T1.AGENT_NAME) FROM");
		sql.append(this.getTableName("CC_BUSI_USER t1"));
		if(StringUtils.isNotBlank(this.param.getString("groupId"))){
			sql.append(",").append(getTableName("CC_SKILL_GROUP_USER t2"));
			sql.append("WHERE T1.USER_ID = T2.USER_ID AND T1.BUSI_ORDER_ID = T2.BUSI_ORDER_ID AND T1.USER_STATE = 0 AND T1.AGENT_PHONE IS NOT NULL");
			sql.append(this.param.getString("groupId"), "AND T2.SKILL_GROUP_ID = ?");
		}else{
			sql.append("WHERE T1.USER_STATE = 0 AND T1.AGENT_PHONE IS NOT NULL");
		}
		sql.append(this.getEntId(), "AND T1.ENT_ID = ? ");
		sql.append(this.getBusiOrderId(), "AND T1.BUSI_ORDER_ID = ?");
		sql.append("ORDER BY T1.AGENT_PHONE ASC");
		return this.getDictByQuery(sql.getSQL(), sql.getParams());
	}
	
	/**
	 * 坐席帐号字典
	 */
	@WebControl(name="acctDict",type=Types.DICT)
	public JSONObject acctDict(){
		String connects = "CONCAT(CONCAT(T3.AGENT_NAME,'-'),T1.USER_ACCT)";
		if(DBTypes.ORACLE.equals(getQuery().getTypes())){
			connects = "T3.AGENT_NAME||'-'||T1.USER_ACCT ";
		}
		EasySQL sql = this.getEasySQL("SELECT T1.USER_ACCT,CONNECTS FROM CC_USER T1,").append(getTableName("CC_BUSI_USER t3"));
		if(StringUtils.isNotBlank(this.param.getString("groupId"))){
			sql.append(",").append(getTableName("CC_SKILL_GROUP_USER T2"));
			sql.append("WHERE T3.USER_ID = T2.USER_ID AND T3.ENT_ID = T2.ENT_ID AND T3.BUSI_ORDER_ID = T2.BUSI_ORDER_ID");
			sql.append(this.param.getString("groupId"), "AND T2.SKILL_GROUP_ID = ?");
		}else{
			sql.append("WHERE 1 = 1");
		}
		sql.append("AND T1.ENT_ID = T3.ENT_ID AND T1.USER_ID = T3.USER_ID AND T1.USER_STATE = 0 AND T1.LOCK_STATE = 0 AND T1.AGENT_PHONE IS NOT NULL");
		sql.append(this.getBusiOrderId(), "AND T3.BUSI_ORDER_ID = ?");
		sql.append(this.getEntId(), "AND T1.ENT_ID = ? ");
		sql.append("ORDER BY T1.AGENT_PHONE ASC");
		return this.getDictByQuery(sql.getSQL().replace("connects", connects), sql.getParams());
	}
	
	/**
	 * 坐席字典（工作坐席效能专用）
	 */
	@WebControl(name="userDictByWork",type=Types.DICT)
	public JSONObject userDictByWork(){
		EasySQL sql = this.getEasySQL("SELECT T1.AGENT_PHONE,T1.USERNAME FROM CC_USER T1 ");
		sql.append(" left join "+getTableName("CC_SKILL_GROUP_USER")+" t2 on t2.USER_ID = t1.USER_ID and t1.ENT_ID  = t2.ENT_ID ");
		sql.append(" left join "+getTableName("CC_SKILL_GROUP")+" t3 on t3.SKILL_GROUP_ID = t2.SKILL_GROUP_ID and t3.SKILL_GROUP_TYPE !='struct' ");
		sql.append(" where 1=1 ");
		sql.append(this.getEntId(), "AND T1.ENT_ID = ? ");
		sql.append(" AND T1.AGENT_PHONE IS NOT NULL ");
		if(StringUtils.isNotBlank(this.param.getString("skillGroupId"))){
			sql.append(this.param.getString("skillGroupId"), " AND T3.SKILL_GROUP_ID = ?");
		}
		sql.append("ORDER BY T1.AGENT_PHONE ASC");
		JSONObject dictByQuery = this.getDictByQuery(sql.getSQL(), sql.getParams());
		return dictByQuery;
	}
	
	/**
	 * 获得当天的时间段
	 * @return
	 */
	@WebControl(name="limitTime",type=Types.TEXT)
	public JSONObject limitTime(){
		return getJsonResult(EasyDate.getCurrentDateString("yyyy-MM-dd")+ " 00:00:00"+' '
				+'~'+' '+EasyDate.getCurrentDateString("yyyy-MM-dd")+ " 23:59:59");
	}
	
	/**
	 * 获得明天的时间
	 * @return yyyy-MM-dd
	 */
	@WebControl(name="tomorrowDate",type=Types.TEXT)
	public JSONObject tomorrowDate(){
	  	Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.DATE, 1);
        String day = EasyDate.dateToString(c.getTime(), "yyyy-MM-dd");
		return getJsonResult(day);
	}
	
	/**
	 * 获得月初到明天的时间段
	 * @return yyyy-MM-dd
	 */
	@WebControl(name="limitDate",type=Types.TEXT)
	public JSONObject limitDate(){
	  	Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        String today = EasyDate.dateToString(c.getTime(), "yyyy-MM")+"-01";
        c.add(Calendar.DATE, 1);
        String day = EasyDate.dateToString(c.getTime(), "yyyy-MM-dd");
		return getJsonResult(today+" ~ "+day);
	}
	
	/**
	 * 获取字典
	 * @return
	 */
	@WebControl(name = "getDict", type = Types.DICT)
	public JSONObject getDict() {
		String dictGroupCode = (String)this.getMethodParam(0);
		if(StringUtils.isBlank(dictGroupCode)){
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 无法查询数据字典,请求的数据字典code为空");
			return null;
		}
		String depCode = UserUtil.getUser(request).getEpCode();
		return DictCache.getJsonEnableDictListByGroupCode(depCode, dictGroupCode);
	}
	/**
	 * 加载全媒体技能组
	 * @return
	 */
	@WebControl(name = "queryMediaSkill", type = Types.DICT)
	public JSONObject queryMediaSkill() {
		return this.getDictByQuery(
				"SELECT T.CODE,T.NAME FROM " + getTableName("V_CF_SKILLS") + " T WHERE T.SESSION_TYPE=02 AND ENT_ID = ? AND BUSI_ORDER_ID = ?",
				new Object[] {getEntId(),getBusiOrderId()});
	}
	/**
	 * 加载全媒体渠道
	 * @return
	 */
	@WebControl(name = "queryMediaChannel", type = Types.DICT)
	public JSONObject queryMediaChannel() {
		JSONObject json= this.getDictByQuery(
				"SELECT CHANNEL_ID CODE,CHANNEL_NAME NAME FROM CC_CHANNEL WHERE ENT_ID = ?",
				new Object[] {getEntId()});
		return json;
	}
	
	/**
	 * 根据类型获取小结的版本信息
	 * @return
	 */
	@WebControl(name="getSummaryVersion",type=Types.DICT)
	public JSONObject getSummaryVersion() {
		String type= StringUtils.defaultString(this.param.getString("type"),Constants.SUMMARY_O);
		EasyQuery query=this.getQuery();
	
		EasySQL sql = new EasySQL();
		if(query.getTypes()==DBTypes.MYSQL){
			sql = new EasySQL("select SR_VERSION as CODE,  CONCAT('第',SR_VERSION,'版') as NAME from "+getTableName("c_call_summary")+" where 1=1");
		}else{
			sql = new EasySQL("select SR_VERSION as CODE, '第'||SR_VERSION||'版' as NAME from "+getTableName("c_call_summary")+" where 1=1");
		}
		sql.append(type,"AND TYPE = ?");
		sql.append(getEntId()," and ENT_ID = ? ");
		sql.append(getBusiOrderId()," and BUSI_ORDER_ID = ? ");
		//sql.append(" and (SR_VERSION is not null or SR_VERSION <> '')");
		sql.append(" GROUP BY SR_VERSION ORDER BY SR_VERSION DESC");
		JSONObject json =this.getDictByQuery(sql.getSQL(), sql.getParams());
		return json;
	}
	
	/**
	 * 根据类型获取一级小结信息
	 * @return
	 */
	@WebControl(name="getParentSummary",type=Types.DICT)
	public JSONObject getParentSummary() {
		JSONObject json=new JSONObject();
		String version = this.param.getString("version");
		String type= StringUtils.defaultString(this.param.getString("type"),Constants.SUMMARY_O);
		if(StringUtils.isNotBlank(version)){
			EasySQL sql = new EasySQL("select CODE as CODE, NAME as NAME from "+getTableName("c_call_summary")+" where P_CODE='0'");
			sql.append(type,"AND TYPE = ?");
			//sql.append(version,"AND SR_VERSION = ?");
			sql.append(getEntId()," and ENT_ID = ? ");
			sql.append(getBusiOrderId()," and BUSI_ORDER_ID = ? ");
			sql.append("ORDER BY SR_VERSION DESC,CODE");
			json=this.getDictByQuery(sql.getSQL(), sql.getParams());
			return json;
		}
		EasySQL sql = new EasySQL("select CODE as CODE, NAME as NAME from "+getTableName("c_call_summary")+" where P_CODE='0'");
		sql.append(type,"AND TYPE = ?");
		//sql.append("and  (SR_VERSION IS NULL or (SR_VERSION ='' and SR_VERSION !='0'))");
		sql.append(getEntId()," and ENT_ID = ? ");
		sql.append(getBusiOrderId()," and BUSI_ORDER_ID = ? ");
		sql.append("ORDER BY SR_VERSION DESC,CODE");
		json=this.getDictByQuery(sql.getSQL(), sql.getParams());
		return json;
	}
	
	@WebControl(name="getChildSummary",type=Types.DICT)
	public JSONObject getChildSummary() {
		JSONObject json=new JSONObject();
		String version = this.param.getString("version");
		String type= StringUtils.defaultString(this.param.getString("type"),Constants.SUMMARY_O);
		String code= StringUtils.defaultString(this.param.getString("code"),"0");
		if(StringUtils.isNotBlank(version)){
			EasySQL sql = new EasySQL("select CODE as CODE, NAME as NAME from "+getTableName("c_call_summary")+" where 1=1");
			sql.append(type,"AND TYPE = ?");
			sql.append(code,"AND P_CODE = ?");
			//sql.append(version,"AND SR_VERSION = ?");
			sql.append(getEntId()," and ENT_ID = ? ");
			sql.append(getBusiOrderId()," and BUSI_ORDER_ID = ? ");
			sql.append("ORDER BY SR_VERSION DESC,CODE");
			json=this.getDictByQuery(sql.getSQL(), sql.getParams());
			return json;
		}
		EasySQL sql = new EasySQL("select CODE as CODE, NAME as NAME from "+getTableName("c_call_summary")+" where 1=1");
		sql.append(type,"AND TYPE = ?");
		sql.append(code,"AND P_CODE = ?");
		//sql.append("and  (SR_VERSION IS NULL or (SR_VERSION ='' and SR_VERSION !='0'))");
		sql.append(getEntId()," and ENT_ID = ? ");
		sql.append(getBusiOrderId()," and BUSI_ORDER_ID = ? ");
		sql.append("ORDER BY SR_VERSION DESC,CODE");
		json=this.getDictByQuery(sql.getSQL(), sql.getParams());
		return json;
	}
	
}
