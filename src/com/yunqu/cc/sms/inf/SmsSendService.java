package com.yunqu.cc.sms.inf;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.base.ServiceID;
import com.yq.busi.common.service.SchemaService;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.ConfigUtil;
import com.yunqu.cc.sms.base.CommonLogger;
import com.yunqu.cc.sms.base.Constants;
import com.yunqu.cc.sms.base.QueryFactory;

/**
 *生日短信发送服务
 */
public class SmsSendService extends IService {
	private Logger logger = CommonLogger.logger;

	private EasyQuery query = QueryFactory.getReadQuery();

	@Override
	public JSONObject invoke(JSONObject json) throws ServiceException {
		String command = json.getString("command");
		if (ServiceCommand.SMS_BIRTHDAY.equals(command)) {
			// 生日短信发送
			List<String> dbNames = SchemaService.findAllEntSchema();
			for (String schema : dbNames) {
				List<JSONObject> li = getSendSmsTell(schema);
				String content = getSendSmsContent(schema);
				if (!"".equals(content) && null != content && li.size() > 0) {
					List<JSONObject> list = new ArrayList<>();
					for (int i = 0; i < li.size(); i++) {
						JSONObject jso = new JSONObject();
						jso.put("receiver", li.get(i).getString("PHONENUM"));
						jso.put("content", content.replace("name", li.get(i).getString("NAME")));
						list.add(jso);
					}
					return sms(list, schema);
				}
			}
		}
		return null;
	}

	public List<JSONObject> getSendSmsTell(String schema) {
		List<JSONObject> lis = new ArrayList<>();
		String time = EasyCalendar.newInstance().getDateTime("-").substring(5, 10);
		String sql = "SELECT PHONENUM,NAME FROM " + schema + ".C_SMS_MEMBER WHERE SUBSTR(BIRTH_DATE, 6) = ? ";
		try {
			List<EasyRow> list = query.queryForList(sql, new Object[] { time });
			if (list != null && list.size() > 0) {
				for (EasyRow easyRow : list) {
					JSONObject jon = new JSONObject();
					jon.put("name", easyRow.getColumnValue("NAME"));
					jon.put("phone", easyRow.getColumnValue("PHONENUM"));
					lis.add(jon);
				}
			}
			return lis;
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "生日短信发送获取发送电话:" + e.getMessage(), e);
		}
		return lis;
	}

	public String getSendSmsContent(String schema) {
		String sql = "SELECT CONTENT FROM " + schema + ".C_SMS_TEMPLATE WHERE MSG_CODE = ?";
		String content = "";
		try {
			List<EasyRow> list = query.queryForList(sql, new Object[] { "BIRTHDAY_SMS" });
			if (list != null && list.size() > 0) {
				content = list.get(0).getColumnValue("CONTENT");
			}
			return content;
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			logger.error(CommonUtil.getClassNameAndMethod(this) + "生日短信发送获取发送内容:" + e.getMessage(), e);
			e.printStackTrace();
		}
		return content;
	}

	public JSONObject sms(List<JSONObject> list, String schema) {
		JSONObject son = new JSONObject();
		JSONObject js = new JSONObject();
		String id = RandomKit.smsAuthCode(16);
		String time = EasyCalendar.newInstance().getDateTime("-");
		js.put("sender", Constants.APP_NAME);
		js.put("sendTime", time);
		js.put("serialId", "");
		js.put("userAcc", "system");
		js.put("busiId", id);
		js.put("source", Constants.SEND_TYEP);
		js.put("model", ConfigUtil.getString("MODEL"));
		js.put("category", ConfigUtil.getString("CATEGORY"));
		js.put("command", ServiceCommand.SENDMESSAGE);
		js.put("schema", schema);
		js.put("receivers", list);
		IService service;
		try {
			service = ServiceContext.getService(ServiceID.SMSGW_INTEFACE);
			return son = service.invoke(js);
		} catch (ServiceException e) {
			// TODO Auto-generated catch block
			logger.error(CommonUtil.getClassNameAndMethod(this) + "生日短信发送:" + e.getMessage(), e);
			e.printStackTrace();
		}
		return son;
	}
}
