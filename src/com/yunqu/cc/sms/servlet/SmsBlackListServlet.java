package com.yunqu.cc.sms.servlet;

import java.sql.SQLException;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.BaseI18nUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.sms.base.AppBaseServlet;
import com.yunqu.cc.sms.base.CommonLogger;
import com.yunqu.cc.sms.base.Constants;
/**
 * Title:短信黑名单类
 * Description:实现短信黑名单业务处理
 * Company:云趣科技
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/smsBlackList/*")
public class SmsBlackListServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;

	private Logger logger = CommonLogger.getLogger();
	/**
	 * 添加短信黑名单
	 * @return
	 */
	@WebControl(name="actionForAdd",type=Types.LIST)
	public EasyResult actionForAdd() {
		try {
			JSONObject jsonObject = this.getJSONObject();
			int i=getBlackList(jsonObject);
			if(0!=i){
				return EasyResult.error(501, BaseI18nUtil.getI18nValue(this.getRequest(), Constants.APP_NAME, "该手机已经进入黑名单"));	
			}
			String createTime = EasyCalendar.newInstance().getDateTime("-");
			String id = RandomKit.smsAuthCode(16);
			UserModel userModel = UserUtil.getUser(this.getRequest());
			String deptCode=userModel.getDept().getDeptCode();//所属部门编号
			String userName=userModel.getUserName();//创建人姓名
			String userAccount=userModel.getUserAcc();//登陆账号
			String epCode=userModel.getEpCode();//企业编号
			String status= "1";
			jsonObject.put("ID", id);
			jsonObject.put("CREATE_USER_NAME", userName);
			jsonObject.put("CREATE_DEPT_CODE", deptCode);
			jsonObject.put("CREATE_TIME", createTime);
			jsonObject.put("CREATE_USER_ACC", userAccount);
			jsonObject.put("CREATE_EP_CODE", epCode);
			jsonObject.put("STATUS", status);
			jsonObject.put("ENT_ID", getEntId());
			jsonObject.put("BUSI_ORDER_ID", getBusiOrderId());
			EasyRecord record = new EasyRecord(this.getTableName("C_SMS_BLACK"), "ID").setColumns(jsonObject);
			this.getQuery().save(record);
			return EasyResult.ok(record, BaseI18nUtil.getI18nValue(this.getRequest(), Constants.APP_NAME,"操作成功!"));
		} catch (SQLException ex) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"添加失败，原因：" + ex.getMessage(), ex);
			return EasyResult.error(501, BaseI18nUtil.getI18nValue(this.getRequest(), Constants.APP_NAME, "操作失败，原因：") + ex.getMessage());
		}
	}
	
	/**
	 * 修改短信黑名单
	 * @return
	 */
	@WebControl(name="actionForUpdate",type=Types.LIST)
	public EasyResult actionForUpdate() {
		try {
			JSONObject json = this.getJSONObject();
			String id = this.getJsonPara("id");
			json.put("ID", id);
			int j=getBlackList(json);
			if(0!=j){
				return EasyResult.error(501, BaseI18nUtil.getI18nValue(this.getRequest(), Constants.APP_NAME, "该手机已经进入黑名单"));
			}
			EasyRecord record = new EasyRecord(this.getTableName("C_SMS_BLACK"), "ID").setColumns(json);
			this.getQuery().update(record);
			return EasyResult.ok(record, BaseI18nUtil.getI18nValue(this.getRequest(), Constants.APP_NAME,"操作成功!"));
		} catch (SQLException ex) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"更新失败，原因：" + ex.getMessage(), ex);
			return EasyResult.error(501, BaseI18nUtil.getI18nValue(this.getRequest(), Constants.APP_NAME, "操作失败，原因：") + ex.getMessage());
		}
	}
	
	/**
	 * 根据id删除短信黑名单
	 * 
	 * @return
	 */
	@WebControl(name="actionForDelete",type=Types.LIST)
	public EasyResult actionForDelete() {
		try {
			String id = this.getJsonPara("id");
			EasyRecord record = new EasyRecord(this.getTableName("C_SMS_BLACK"), "ID").setPrimaryValues(id);
			this.getQuery().deleteById(record);
			return EasyResult.ok(record);
		} catch (SQLException ex) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"删除失败，原因：" + ex.getMessage(), ex);
			return EasyResult.error(501, BaseI18nUtil.getI18nValue(this.getRequest(), Constants.APP_NAME, "操作失败，原因：") + ex.getMessage());
		}
	}
	
	/**
	 * 查询改手机号是否是黑名单
	 * @return
	 */
	public int getBlackList(JSONObject obj){
		String sql = "select * from " + this.getTableName("C_SMS_BLACK") + " WHERE PHONENUM=? AND ENT_ID = ? AND BUSI_ORDER_ID = ?";
		logger.debug(CommonUtil.getClassNameAndMethod(this)+"[查询渠道是否存在]，sql="+sql.toString()+"");
		try {
			List<EasyRow> list = this.getQuery().queryForList(sql,new Object[]{obj.getString("PHONENUM"),getEntId(),getBusiOrderId()});
			return list.size();
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"获取失败，原因：" + e.getMessage(), e);
		}
		return 0;
	}
	
}
