package com.yunqu.cc.base.inf;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.service.SchemaService;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.base.base.CommonLogger;
import com.yunqu.cc.base.base.QueryFactory;
import com.yunqu.cc.base.dao.agentconfig.InterfaceDao;
/**
 * 坐席配置相关服务，如查询关键字
 * 场景：全媒体客户与客服交流时，会通过该服务标记出客户一句话中的关键字; 全媒体会将会话的整段接送传入进来，匹配之后，替换其中部分内容即可。
 *
 */
public class MediaTagService extends IService{
	
	public Logger logger = CommonLogger.logger;
	
	public static final String CHANNEL_KEYWORD_CACHE_KEY="keywords-";
	
	private InterfaceDao dao = new InterfaceDao();
	
	@Override
	public JSONObject invoke(JSONObject json) throws ServiceException {
		return agentConfigSrhKeyWord(json);
	}
	/**
	 * 查询关键字
	 * @param json
	 * @return
	 */
	private JSONObject agentConfigSrhKeyWord(JSONObject json) {
		
		try {
			logger.info(CommonUtil.getClassNameAndMethod(this) + " 标记关键字,请求: << " + json.toJSONString());
			String entId = json.getString("entId");
			
			if(StringUtils.isBlank(entId)){
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 企业编号为空:"+json.toJSONString());
				return json;
			}
			
			String schema = SchemaService.findSchemaByEntId(entId);
			
			if(StringUtils.isBlank(schema)){
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 未找到对应的数据库名:"+json.toJSONString());
				return json;
			}
			
			JSONObject data = json.getJSONObject("data");
			
			String channelKey = data.getString("channelKey"); // 渠道标识
			String msgType = data.getString("msgType"); // 消息类型
			String msgContent = data.getString("msgContent"); // 消息内容
			
			JSONObject visitorInfo = json.getJSONObject("visitorInfo");
			String chatSessionId = visitorInfo.getString("chatSessionId"); //会话id
			String skillGroupId = visitorInfo.getString("skillGroupId"); //技能组ID，目前还拿不到 TODO
			String userAcc = visitorInfo.getString("agentId"); //坐席账号，agentId里存的是账号
			
			//只处理文本消息
			if(!"text".equals(msgType)){
				return json;
			}
			
			//获取渠道标识
			if(StringUtils.isBlank(channelKey)){
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 无法获取到channelKey:"+channelKey);
				return json;
			}
			
			if(StringUtils.isBlank(msgContent)){
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 内容为空,不打标签:"+msgContent);
				return json;
			}
			
			JSONArray keywordArray = new JSONArray();
			//找到缓存中的标签集合
			String cacheKeyWords = CacheUtil.get(CHANNEL_KEYWORD_CACHE_KEY+channelKey);
			List<JSONObject> list = null;
			//缓存里存的是标签json串，要转成数组
			if(StringUtils.isBlank(cacheKeyWords)){
				list = initKeyWords(channelKey,entId,schema);
				
				if(list==null || list.size()<1){
					CacheUtil.put(CHANNEL_KEYWORD_CACHE_KEY+channelKey, "{}",3600); //某个渠道无关键字时，避免重复查询数据库
					logger.warn(CommonUtil.getClassNameAndMethod(this)+" 往缓存中存入关键字,key="+"keywords-"+channelKey+",size="+0);
					return json;
				}
				CacheUtil.put(CHANNEL_KEYWORD_CACHE_KEY + channelKey, JSONObject.toJSONString(list),3600);
				logger.info(CommonUtil.getClassNameAndMethod(this)+" 往缓存中存入关键字,key="+"keywords-"+channelKey+",size="+list.size());
				
			}else if("{}".equals(cacheKeyWords)){
				logger.warn(CommonUtil.getClassNameAndMethod(this)+" 渠道缓存里无关键字,直接返回,"+channelKey);
				return json;
				
			}else{
				list = JSONObject.parseArray(CacheUtil.get(CHANNEL_KEYWORD_CACHE_KEY+channelKey)).toJavaList(JSONObject.class);
			}
			
			
			if(list!=null && list.size()>0){
				if(ServerContext.isDebug()){
					logger.info(CommonUtil.getClassNameAndMethod(this)+" 开始检测关键字:"+msgContent);
				}
				//逐个匹配
				for(int i=0;i<list.size();i++){
					JSONObject keywordJson = list.get(i);
					String id = keywordJson.getString("ID");
					String name = keywordJson.getString("CONTENT");
					if(StringUtils.isBlank(name)){
						continue;
					}
					try {
						Pattern pattern = Pattern.compile(name);
						Matcher matcher = pattern.matcher(msgContent);
						//匹配到内容
						if(matcher.find()){
							String word = matcher.group();
							JSONObject j = new JSONObject();
							j.put("keyWordId", id);
							j.put("keyWord", word);
							j.put("keyDir", keywordJson.getString("NAME"));
							keywordArray.add(j);
							msgContent = msgContent.replace(word, "<span class='chat-keyword' title='" + keywordJson.getString("NAME") + "'>" + word + "</span>");
							
							//将关键字操作记录入库
							dao.keywordRecord(channelKey, userAcc, skillGroupId, name, data.getString("msgContent"), entId, null, schema, "03", chatSessionId);
						}
					} catch (Exception e) {
						logger.error(CommonUtil.getClassNameAndMethod(this)+" 匹配关键字出错:"+name+","+msgContent,e);
					}
					
				}
			}
			
			//将关键字操作记录，使用队列，延迟入库
			/*if(keywordArray.size()>0){
				JSONObject req = new JSONObject();
				req.put("createAcc", userAcc);
				req.put("channelKey", channelKey);
				req.put("skillGroupId", skillGroupId);
				req.put("trickContent", data.getString("msgContent"));
				req.put("execType", "saveKeyWords");
				req.put("entId", entId);
				req.put("busiOrderId", ""); //目前暂无
				req.put("busiType", "03"); //对于敏感词 01-全媒体坐席 02-短信 03-全媒体客户
				req.put("type", "3"); //话术类型 1-常用语 2-敏感词 3-关键字
				req.put("busiId", chatSessionId);
				req.put("keywords", keywordArray);
				req.put("schema", schema);
				JobMessageQueue.getInstance().put(req.toJSONString());
			}*/
			
			data.put("msgContent", msgContent);
			data.put("keywords", keywordArray);
			
			
			
			
			logger.info(CommonUtil.getClassNameAndMethod(this) + "标记关键字,响应: >> " + json.toJSONString());
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 标记会话记录关键字出错:"+e.getMessage(),e);
		}
		return json;
	}
	
	/**
	 * 根据渠道获取关键字
	 * @param channelKey
	 * @return
	 */
	private List<JSONObject> initKeyWords(String channelKey,String entId,String schema) {
		try {
			EasyQuery query = QueryFactory.getWriteQuery();
			EasySQL sql = new EasySQL();
			
			//查询出针对客户的全媒体关键字
			sql.append("SELECT t1.ID,t1.CONTENT,t2.BUSI_ID,t3.NAME FROM " + schema + ".C_CF_KEYWORD t1");
			sql.append("INNER JOIN " + schema + ".C_CF_KEYWORD_BUSI T2 ON T1.DIR_ID = T2.DIR_ID");
			sql.append("INNER JOIN " + schema + ".C_CF_KEYWORD_DIR T3 ON T1.DIR_ID = T3.ID");
			sql.append(channelKey," WHERE T2.BUSI_ID = ?");
			sql.append("01"," AND T3.ENABLE_STATUS =? ");
			sql.append(entId," AND T3.EP_CODE = ?");
			sql.append("01"," AND T3.BUSI_TYPE = ?");
			
			if(ServerContext.isDebug()){
				logger.info(CommonUtil.getClassNameAndMethod(this) + "关键字查询sql:" + sql.getSQL());
			}
			
			List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			if(CommonUtil.listIsNotNull(list)){
				return list;
			}
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 往缓存里添加敏感词出现异常:"+e.getMessage(),e);
		}
		return null;
	}
	
}
