package com.yunqu.cc.base.servlet.area;

import java.io.File;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.Part;

import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.excel.ExcelUtils;
import org.easitline.common.utils.excel.handler.ExcelHeaderStyle;
import org.easitline.common.utils.excel.utils.Utils;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.base.base.AppBaseServlet;
import com.yunqu.cc.base.base.CommonLogger;

@MultipartConfig
@WebServlet("/servlet/county/*")
public class CountyServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	private Logger logger = CommonLogger.logger;

	/**
	 * 地区管理数据添加或者修改
	 * @return
	 */
	public EasyResult actionForCountyEdit() {
		JSONObject json = new JSONObject();
		JSONObject countyJson = getJSONObject("COUNTY");
		EasyQuery query = this.getQuery();
		try {
			query.begin();
			EasyRecord record = new EasyRecord("CC_COUNTY", "COUNTY_CODE");
			record.putAll(countyJson);
			if(StringUtils.isBlank(this.getJSONObject().getString("code"))) {
				query.save(record);
			} else {
				query.update(record);
			}
			
			logger.info(CommonUtil.getClassNameAndMethod(this)+" 保存区县信息"+json.toJSONString());
			
			query.commit();
			return EasyResult.ok("", getI18nValue("操作成功！"));
		} catch (Exception e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
			}
			logger.error(CommonUtil.getClassNameAndMethod(this)+"操作失败，原因：" + e.getMessage());
			System.out.println(CommonUtil.getClassNameAndMethod(this)+"操作失败，原因：" + e.getMessage());
			return EasyResult.error(500, "操作失败，原因：" + e.getMessage());
		}
	}
	
	/**
	 * 地区管理（批量）删除
	 * @return
	 */
	public EasyResult actionForCountyDelete() {
		JSONObject json = new JSONObject();
		JSONObject obj = this.getJSONObject();
		JSONArray ids = obj.getJSONArray("ids");
		EasyQuery query = this.getQuery();
		try {
			EasyRecord record = null;
			query.begin();
			for (int i = 0; i < ids.size(); i++) {
				json.put("COUNTY_CODE", ids.getString(i));
				record =  new EasyRecord("CC_COUNTY","COUNTY_CODE").setColumns(json);
				query.deleteById(record);
				
				logger.info(CommonUtil.getClassNameAndMethod(this));
			}
			query.commit();
			
			return EasyResult.ok("",getI18nValue("操作成功"));
		} catch (Exception e) {
			try {
				query.roolback();
			} catch (Exception e2) {
			}
			logger.error(CommonUtil.getClassNameAndMethod(this)+"删除失败，原因：" + e.getMessage());
			return EasyResult.error(500, "删除失败，原因：" + e.getMessage());
		}
	}
	
	/**
	 * 区县管理界面导出
	 * @throws SQLException
	 */
	public void actionForCountyExport() throws SQLException {
		HttpServletRequest request = this.getRequest();
		
		EasySQL sql = new EasySQL("select  * ");
		sql.append(" from CC_COUNTY");
		sql.append(" where 1=1  ");
		
		sql.append(this.getPara("ADMINISTRATIVE_CODE"), "AND ADMINISTRATIVE_CODE = ?");
		String keyWord=request.getParameter("KEY_WORD");
		if(StringUtils.notBlank(keyWord)) {
		sql.append(" AND (");
		sql.appendLike(keyWord, " COUNTY_CODE LIKE ?");
		sql.appendLike(keyWord, "or COUNTY_NAME LIKE ?");
		sql.append(" ) ");
		}
		
		sql.append(" order by COUNTY_CODE");
		
		List<Map<String, String>> data = getQuery().queryForList(sql.getSQL(), sql.getParams(), new MapRowMapperImpl());
		// 组装表头
		File file = FileKit.createTempFile(RandomKit.randomStr() + ".xlsx");
		List<String> headers = new ArrayList<String>();
		headers.add(" 序号 ");
		headers.add(" 区县编码 ");
		headers.add(" 区限名称 ");
		headers.add(" 城市行政编码 ");
		headers.add(" 城市名称 ");
		List<ExcelHeaderStyle> styles = new ArrayList<ExcelHeaderStyle>();
		for (String header : headers) {
			ExcelHeaderStyle style = new ExcelHeaderStyle();
			style.setData(header);
			style.setWidth(3600);
			styles.add(style);
		}
		List<List<String>> excelData = new ArrayList<List<String>>();
		int i = 0;
		List<String> list = null;
		if (data != null && data.size() > 0) {
			for (Map<String, String> map : data) {
				list = new ArrayList<String>();
				list.add(String.valueOf(++i));
				list.add(map.get("COUNTY_CODE"));
				list.add(map.get("COUNTY_NAME"));
				list.add(map.get("ADMINISTRATIVE_CODE"));
				list.add(map.get("AREA_NAME"));
				excelData.add(list);
			}
		}
		try {
			ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
			renderFile(file, "区县管理列表.xlsx");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	
	/**
	 * 地区信息导入
	 * @return
	 */
	public JSONObject actionForCountyUpload() {
		EasyQuery query = this.getQuery();
		try {
			query.begin();
			Part part = getFile("file");
			Workbook workbook = WorkbookFactory.create(part.getInputStream());
			List<List<String>> list = new ArrayList<>();
			Sheet sheet = workbook.getSheetAt(0);
			int maxLine = sheet.getLastRowNum();
			int lastCellNum = 0;
			for (int ii = 0; ii <= maxLine; ii++) {
				List<String> rows = new ArrayList<>();
				Row row = sheet.getRow(ii);
				if (ii == 0) {
					lastCellNum = row.getLastCellNum();
				}
				if (row != null) {
					for (int j = 0; j < lastCellNum; j++) {
						Cell cell = row.getCell(j);
						if(cell!=null){
							String val = Utils.getCellValue(cell); //
							if (StringUtils.isBlank(val)) {//isBlank用于判断单元格是否为空，如果为空，则返回TRUE；否则返回FALSE
								rows.add("");
							} else {
	            				rows.add(val);
							}
						}else{
							rows.add("");
						}
					}
					list.add(rows);
				}
			}

			for (int i = 1; i < list.size(); i++) {
				Map<Object, Object> m = new HashMap<Object, Object>();
				for (int j = 0; j < list.get(0).size(); j++) {
					switch (j) {
					case 1:
						m.put("COUNTY_CODE", list.get(i).get(j));
						break;
					case 2:
						m.put("COUNTY_NAME", list.get(i).get(j));
						break;
					case 3:
						m.put("ADMINISTRATIVE_CODE", list.get(i).get(j));
						break;
					case 4:
						m.put("AREA_NAME", list.get(i).get(j));
						break;
					default:
						break;
					}
				}
				JSONObject add=new JSONObject();
				String str = JSONObject.toJSON(m).toString();
				JSONObject jsonObject = JSONObject.parseObject(str);
				String countyCode=jsonObject.getString("COUNTY_CODE");
				String countyName=jsonObject.getString("COUNTY_NAME");
				if(!"".equals(countyCode)&&!"".equals(countyCode)){
					List<EasyRow> rowtb =this.getQuery().queryForList("select COUNTY_CODE from CC_COUNTY where AREA_CODE=?",new Object[]{countyCode});
					if(rowtb!=null&&!rowtb.isEmpty()){
						return EasyResult.error(100,"您好，数据中"+countyCode+"这个地区编码已存在"+countyName+"这个地区，所以无法导入");
					}
				}
				
				add.put("COUNTY_CODE", countyCode);
				add.put("COUNTY_NAME", countyName);
				add.put("ADMINISTRATIVE_CODE", jsonObject.getString("ADMINISTRATIVE_CODE"));
				add.put("AREA_NAME", jsonObject.getString("AREA_NAME"));
				EasyRecord record = new EasyRecord("CC_COUNTY", "COUNTY_CODE").setColumns(add);
				query.save(record);
			}
			query.commit();
			return EasyResult.ok("", getI18nValue("操作成功"));
		} catch (Exception e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				e1.printStackTrace();
			}
			logger.error(CommonUtil.getClassNameAndMethod(this) + "区县信息导入异常:"+e.getMessage(), e);
			return EasyResult.ok("", getI18nValue("操作失败"));
		}
	}
	
}
