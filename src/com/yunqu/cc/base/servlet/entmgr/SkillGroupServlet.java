
/**
 * 
 */
package com.yunqu.cc.base.servlet.entmgr;

import java.io.File;
import java.io.IOException;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.Part;

import org.apache.log4j.Logger;
import org.apache.poi.EncryptedDocumentException;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasyRowMapper;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.excel.ExcelUtils;
import org.easitline.common.utils.excel.handler.ExcelHeaderStyle;
import org.easitline.common.utils.excel.utils.Utils;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.annontation.InfAuthCheck;
import com.yq.busi.common.base.CEConstants;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.model.Yqlogger;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.LogUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.base.base.AppBaseServlet;
import com.yunqu.cc.base.base.CommonLogger;
import com.yunqu.cc.base.base.Constants;
import com.yunqu.cc.base.model.SkillGroup;



/**
 * <AUTHOR>
 *
 */
@MultipartConfig
@WebServlet("/servlet/skillGroup")
public class SkillGroupServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	private Logger logger = CommonLogger.logger;
	/*public EasyResult actionForAdd(){
		EasyResult result = new EasyResult();
		try {
			SkillGroup skillGroup=getModel(SkillGroup.class, "skillGroup");
			String sql="select count(1) from " + getTableName("CC_SKILL_GROUP") + " where ENT_ID = ? and BUSI_ORDER_ID = ? and SKILL_GROUP_NAME = ?";
			if(this.getQuery().queryForExist(sql,new Object[]{getEntId(), getBusiOrderId(), StringUtils.trimToEmpty(skillGroup.getString("SKILL_GROUP_NAME"))})) {
				result.addFail("该技能组名称已经存在！");
				return result;
			}
			skillGroup.setPrimaryValues(this.nextSeq("CC_SKILL_GROUP"));
			skillGroup.set("ENT_ID", getEntId());
			skillGroup.set("BUSI_ORDER_ID", this.getBusiOrderId());
			skillGroup.set("AGENT_COUNT", 0);
			skillGroup.addCreateTime();
			skillGroup.setCreator(getUserPrincipal().getUserId());
			this.getQuery().save(skillGroup);
			this.updateEcNode(skillGroup,true);
			result.setMsg("添加成功");
			String syncGroupInfo = this.syncGroupInfo(this.getEntId(),skillGroup.getString("SKILL_GROUP_ID"),"add");
			this.syncSkillCache(skillGroup.getString("SKILL_GROUP_ID"));
			if(syncGroupInfo != null){
				return EasyResult.fail(syncGroupInfo);
			}
		} catch (SQLException e) {
			this.error("添加失败！失败原因：" + e.getMessage(), e);
			result.addFail("添加失败！失败原因：" + e.getMessage());
		}
		return result;
	}*/
	
	@InfAuthCheck(resId = "cc-base-org-jnzgl")
	public EasyResult actionForAdd(){
		EasyResult result = new EasyResult();
		try {
			SkillGroup skillGroup=getModel(SkillGroup.class, "skillGroup");
			String SKILL_GROUP_TYPE=skillGroup.getString("SKILL_GROUP_TYPE");
			
			String groupType = skillGroup.getString("GROUP_TYPE");
			JSONObject typeJson = this.getQuery().queryForRow("select * from "+getTableName("CC_GROUP_TYPE")+" where GROUP_TYPE = ?", new Object[]{groupType}, new JSONMapperImpl());
			if(typeJson==null){
				return EasyResult.fail(getI18nValue("技能组类型不存在,请到组织类型管理里添加!"));
			}
			
			int count = this.getQuery().queryForInt("select count(1) from "+getTableName("CC_SKILL_GROUP") + " where ENT_ID = ? and BUSI_ORDER_ID = ? and GROUP_TYPE = ?", new Object[]{this.getEntId(),this.getBusiOrderId(),groupType});
			/*
			 * 判断技能组类型的最大节点数是否超标
			 */
			if(typeJson.getIntValue("MAX_NODE_COUNT") != 0 && count >= typeJson.getIntValue("MAX_NODE_COUNT")){
				return EasyResult.fail("添加失败，部门类型\""+typeJson.getString("GROUP_TYPE_NAME")+"\"最大节点数"+typeJson.getIntValue("MAX_NODE_COUNT")+"已经用完，不能再添加该类型。");
			}

			skillGroup.set("SKILL_GROUP_CODE", this.getGroupCode(skillGroup.getString("P_GROUP_ID")));
			skillGroup.set("SKILL_GROUP_TYPE", SKILL_GROUP_TYPE);
			
			JSONObject json=new JSONObject();
			json.put("SPILL_GROUP", skillGroup.getString("SPILL_GROUP"));
			json.put("MGR_TYPE", skillGroup.getString("MGR_TYPE"));
			skillGroup.set("EXT_CONF", json.toJSONString());
			skillGroup.remove("SPILL_GROUP");
			skillGroup.remove("MGR_TYPE");
			skillGroup.set("WORK_TIME", skillGroup.getString("WORK_TIME"));
			skillGroup.set("WORK_NOT_READY", skillGroup.getString("WORK_NOT_READY"));
			skillGroup.set("QUEUE_STRATEGY", skillGroup.getString("QUEUE_STRATEGY"));
			skillGroup.setPrimaryValues(this.nextSeq("CC_SKILL_GROUP"));
			skillGroup.set("ENT_ID", getEntId());
			skillGroup.set("BUSI_ORDER_ID", this.getBusiOrderId());
			skillGroup.set("AGENT_COUNT", 0);
			skillGroup.addCreateTime();
			skillGroup.setCreator(getUserPrincipal().getUserId());
			this.getQuery().save(skillGroup);

			result.setMsg(getI18nValue("添加成功"));
			/*
			 * 判断该技能组是否后有话务权限，
			 * 如果为1，表示有，并同步数据
			 */
			if(typeJson.getIntValue("CALL_FLAG") == 1){
				String syncGroupInfo = this.syncGroupInfo(this.getEntId(),skillGroup.getString("SKILL_GROUP_ID"),"add");
				this.syncSkillCache(skillGroup.getString("SKILL_GROUP_ID"));
				if(syncGroupInfo != null){
					return EasyResult.fail(syncGroupInfo);
				}
			}
			String name="技能组";
			if("struct".equals(SKILL_GROUP_TYPE)){
				name="团队";
			}
			getLogUtil("新增","新增了"+name);
		} catch (SQLException e) {
			this.error("添加失败！失败原因：" + e.getMessage(), e);
			result.addFail(getI18nValue("添加失败！失败原因：") + e.getMessage());
		}
		return result;
	}
	
	
	/**
	 * 获取部门编码
	 * @param busiType
	 * @param pId
	 * @return
	 * @throws SQLException
	 */
	private String getGroupCode(String pId) throws SQLException{
		String lastCode = this.getQuery().queryForString("select max(SKILL_GROUP_CODE) GROUP_CODE from "+getTableName("CC_SKILL_GROUP")+" where ENT_ID = ? and BUSI_ORDER_ID = ? and P_GROUP_ID = ?", new Object[]{this.getEntId(),this.getBusiOrderId(),pId});
		if(StringUtils.isBlank(lastCode)){
			String pCode = this.getQuery().queryForString("select SKILL_GROUP_CODE from "+getTableName("CC_SKILL_GROUP")+" where SKILL_GROUP_ID = ?", new Object[]{pId});
			if(StringUtils.isBlank(pCode)){
				//不是初始节点，但是父节点没有部门编码
				if(!"0".equals(pId)){
					return "";
				}
				return "500";
			}
			return pCode + "500";
		}
		String code = lastCode.substring(lastCode.length() - 3);
		String preCode = lastCode.substring(0,lastCode.length() - 3);
		code = (Integer.parseInt(code) + 1) + "";
		return preCode + code;
	}
	
	/**
	 * 更新技能组
	 * @return
	 */
	public EasyResult actionForUpdate(){
		EasyResult result = new EasyResult();
		JSONObject jsonObject = getJSONObject("skillGroup");
		try {
			/*String sql="select count(1) from " + getTableName("CC_SKILL_GROUP") + " where ENT_ID = ? and BUSI_ORDER_ID = ? and SKILL_GROUP_ID <> ? and SKILL_GROUP_NAME = ?";
			if(this.getQuery().queryForExist(sql,new Object[]{getEntId(), getBusiOrderId(), jsonObject.getString("SKILL_GROUP_ID"), StringUtils.trim(jsonObject.getString("SKILL_GROUP_NAME"))})) {
				result.addFail("该技能组名称已经存在！");
				return result;
			}*/
			SkillGroup skillGroup=getModel(SkillGroup.class, "skillGroup");
			JSONObject obj=new JSONObject();
			obj.put("SPILL_GROUP", skillGroup.getString("SPILL_GROUP"));
			obj.put("MGR_TYPE", skillGroup.getString("MGR_TYPE"));
			skillGroup.put("EXT_CONF", obj.toJSONString());
			
			String typeName=skillGroup.getString("GROUP_TYPE_NAME");
			skillGroup.remove("SPILL_GROUP");
			skillGroup.remove("GROUP_TYPE_NAME"); 
			skillGroup.remove("MGR_TYPE"); 
			this.getQuery().update(skillGroup);
			if(!StringUtils.equals("struct", skillGroup.getString("SKILL_GROUP_TYPE"))) {
				updateUserGroupList(jsonObject.getString("SKILL_GROUP_NAME"), jsonObject.getString("SKILL_GROUP_ID"));
			}
			result.setMsg(getI18nValue("修改成功"));
			getLogUtil("修改","修改"+typeName);
			if(this.getSkillCallFalg(skillGroup.getString("SKILL_GROUP_ID"))){
				this.syncSkillCache(skillGroup.getString("SKILL_GROUP_ID"));
				String syncGroupInfo = this.syncGroupInfo(this.getEntId(),skillGroup.getString("SKILL_GROUP_ID"),"update");
				if(syncGroupInfo != null){
					result.addFail(syncGroupInfo);
				}
			}
		} catch (SQLException e) {
			this.error("修改失败！失败原因：" + e.getMessage(), e);
			result.addFail(getI18nValue("修改失败！失败原因：") + e.getMessage());
		}
		return result;
	}
	
	/**
	 * 技能组移动
	 * @return
	 */
	public JSONObject actionForMobile(){
		JSONObject obj=this.getJSONObject("");
		EasyQuery query = this.getQuery();
		String pgroupId=obj.getString("motionId");
		String skillGroupId=obj.getString("skillGroupId");
		try {
			String sql="update " + getTableName("CC_SKILL_GROUP") + " set P_GROUP_ID='"+pgroupId+"' where SKILL_GROUP_ID ='"+skillGroupId+"'";
			query.execute(sql,new Object[]{});
			getLogUtil("修改","移动"+obj.getString("catalogs"));
			return EasyResult.ok(sql, getI18nValue("移动成功"));
		} catch (SQLException e1) {
			this.error(CommonUtil.getClassNameAndMethod(this)+ "技能组移动异常"+ e1.getMessage(), e1);
			return EasyResult.error(500,"移动失败="+e1.getMessage());
		}
	}
	
	
	
	
	/**
	 * 添加部门用户
	 * @return
	 */
	public EasyResult actionForAddUser(){
		JSONObject param = this.getJSONObject();
		JSONArray userIds = param.getJSONArray("userIds");
		String userAcc = param.getString("userAcc");
		String groupId = param.getString("groupId");
		String groupName = param.getString("groupName");
		String entId = this.getEntId();
		String busiOrderId = this.getBusiOrderId();
		
		if(userIds!=null && userIds.size()>0){
			ArrayList<Object[]> list = new ArrayList<>();
			
			for(int i = 0; i < userIds.size(); i++){
				String userId = userIds.getString(i);
				if(StringUtils.isNotBlank(userId)){
					list.add(new Object[]{groupId,userId,busiOrderId,entId,i});
					//删除缓存
					CacheUtil.getCcUserCache().delCache(getEntId(), getBusiOrderId(), userId);
				}
			}

			EasyQuery query = this.getQuery();
			try {
				query.executeBatch("insert into "+getTableName("CC_SKILL_GROUP_USER")+" (SKILL_GROUP_ID,USER_ID,BUSI_ORDER_ID,ENT_ID,IDX_ORDER) values(?,?,?,?,?)", list);
				this.updateAllUserGroupList();
				this.updateSkillUserCount();//更新技能组坐席数
				//话务能力
				if(this.getSkillCallFalg(groupId)){
					this.syncSkillCache(groupId);
					String syncGroupInfo = this.syncGroupInfo(this.getEntId(),groupId,"update");
					if(syncGroupInfo != null){
						return EasyResult.error(501, syncGroupInfo);
					}
				}
				getLogUtil("新增","为用户 "+userAcc+" 分配到技能组-"+groupName);
				
				
				
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
				return EasyResult.fail(getI18nValue("添加失败，失败原因：")+e.getMessage());
			}
		}
		
		return EasyResult.ok("",getI18nValue("操作成功"));
	}
	
	/**
	 * 添加排队组组
	 * @return
	 */
	public EasyResult actionForAddWorkGroup(){
		JSONObject param = this.getJSONObject();
		String workGroupIdStr = param.getString("workGroupId");
		String groupId = param.getString("groupId");
		String groupName = param.getString("groupName");
		String entId = this.getEntId();
		String busiOrderId = this.getBusiOrderId();
		String date = DateUtil.getCurrentDateStr();
		
		UserModel user = UserUtil.getUser(getRequest());
		
		ArrayList<Object[]> list = new ArrayList<>();
		if(StringUtils.isNotBlank(workGroupIdStr)){
			String [] workGroupArr = workGroupIdStr.split(",");
			EasyQuery query = this.getQuery();
			try {
				
				List<String> userList = this.getQuery().queryForList("SELECT USER_ID FROM " + this.getTableName("CC_SKILL_GROUP_USER") + " WHERE SKILL_GROUP_ID = ?", new Object[]{groupId}, new EasyRowMapper<String>() {

					@Override
					@SuppressWarnings("unchecked")
					public String mapRow(ResultSet rs, int index) {
						try {
							return rs.getString("USER_ID");
						} catch (SQLException e) {
							logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
						}
						return "";
					}
				});
				
				for(String workGroupId : workGroupArr){
					if(StringUtils.isNotBlank(workGroupId)){
						EasyRecord record = new EasyRecord(this.getTableName("C_CF_WORKGROUP_SKILL"), "ID");
						record.put("ID", RandomKit.randomStr());
						record.put("WORKGROUP_ID", workGroupId);
						record.put("SKILLGROUP_ID", groupId);
						record.put("CREATE_TIME", date);
						record.put("CREATE_USER_ACC", user.getUserAcc());
						record.put("CREATE_USER_NAME", user.getUserName());
						record.put("CREATE_USER_DEPT", user.getDeptCode());
						record.put("ENT_ID", entId);
						record.put("BUSI_ORDER_ID", busiOrderId);
						query.save(record);
						
						List<JSONObject> workUserList = query.queryForList("SELECT USER_ID,USER_ACC FROM " + this.getTableName("C_CF_WORKGROUP_USER") + " WHERE WORKGROUP_ID = ?", new Object[]{workGroupId}, new JSONMapperImpl());
						for (JSONObject workUser : workUserList) {
							String userId = workUser.getString("USER_ID");
							if(!userList.contains(userId)) {
								userList.add(userId);
								list.add(new Object[]{groupId, userId, busiOrderId, entId, workGroupId, userList.size()});
							}
						}
					}
				}
				
				query.executeBatch("INSERT INTO "+getTableName("CC_SKILL_GROUP_USER")+" (SKILL_GROUP_ID,USER_ID,BUSI_ORDER_ID,ENT_ID,WORK_GROUP_ID,IDX_ORDER) values(?,?,?,?,?,?)", list);
				
				this.updateAllUserGroupList();
				this.updateSkillUserCount();//更新技能组坐席数
				//话务能力
				if(this.getSkillCallFalg(groupId)){
					this.syncSkillCache(groupId);
					String syncGroupInfo = this.syncGroupInfo(this.getEntId(),groupId,"update");
					if(syncGroupInfo != null){
						return EasyResult.error(501, syncGroupInfo);
					}
				}
				getLogUtil("新增","为工作组 "+workGroupIdStr+" 分配到技能组-"+groupName);
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
				return EasyResult.fail(getI18nValue("添加失败，失败原因：")+e.getMessage());
			}
		}
		
		return EasyResult.ok("",getI18nValue("操作成功"));
	}
	
	public EasyResult actionForSetGroupLeader(){
		String user_id = getJsonPara("user_id");
		String skill_group_id = getJsonPara("skill_group_id");
		String is_leader = getJsonPara("status");
		String sql ="update "+getTableName("CC_SKILL_GROUP_USER")+" set IS_LEADER =? where USER_ID =? and SKILL_GROUP_ID = ?";
		try {
			this.getQuery().execute(sql, new Object[]{is_leader,user_id,skill_group_id});
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(getI18nValue("修改失败"));
		}
		return EasyResult.ok("",getI18nValue("修改成功"));
	}
	
	/**
	 * 删除技能组
	 * @return
	 */
	public EasyResult actionForDelete(){
		try {
			String skillGroupId = getJsonPara("pk");
			if(getQuery().queryForExist("select count(1) from " + getTableName("CC_SKILL_GROUP") + " where P_GROUP_ID = ?", skillGroupId)){
				return EasyResult.fail(getI18nValue("存在下级分组，不能删除,请先删除下级分组！"));
			}
			
			//删除已删除坐席但仍在技能组里的数据
			String deleteSql = "delete from " + getTableName("CC_SKILL_GROUP_USER") + " where SKILL_GROUP_ID=? AND USER_ID in (";
			deleteSql += " select * from (";
			deleteSql += " select T1.USER_ID AS ID from " + getTableName("CC_SKILL_GROUP_USER") + " T1 LEFT JOIN CC_USER T2 on T1.USER_ID=T2.USER_ID";
			deleteSql += " where T1.SKILL_GROUP_ID=? AND T2.USER_ID is null) tem";
			deleteSql += " )";
			getQuery().execute(deleteSql, new Object[] {skillGroupId, skillGroupId});
			
			if(getQuery().queryForExist("select count(1) from " + getTableName("cc_skill_group_user") + " where SKILL_GROUP_ID = ?", new Object[]{skillGroupId})) {
				return EasyResult.fail(getI18nValue("存在所属成员，不能删除,请先删除组内人员！"));
			}
			if(getQuery().queryForExist("select count(1) from " + getTableName("CC_SKILL_GROUP_CHANNEL") + " where SKILL_GROUP_ID = ?", new Object[]{skillGroupId})) {
				return EasyResult.fail(getI18nValue("存在关联的全媒体渠道，不能删除,请先删除关联的渠道！"));
			}
			SkillGroup skillGroup=new SkillGroup(getDbName());
			skillGroup.setPrimaryValues(skillGroupId);
			this.getQuery().deleteById(skillGroup);
			
			//话务能力
			if(this.getSkillCallFalg(skillGroupId)){
				String syncGroupInfo = this.syncGroupInfo(this.getEntId(),skillGroup.getString("SKILL_GROUP_ID"),"delete");
				String reloadKey = "RELOAD_SKILLGROUP_" + skillGroupId;
				cache.delete(reloadKey);
				if(syncGroupInfo != null){
					return EasyResult.fail(syncGroupInfo);
				}
			}
			getLogUtil("删除","删除技能组或者部门");
			return EasyResult.ok("",getI18nValue("删除成功"));
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	/**
	 * 删除排队组
	 * @return
	 */
	public EasyResult actionForDelWorkGroup(){
		EasyQuery query = getQuery();
		JSONObject jsonObject = getJSONObject();
		String skillGroupId = jsonObject.getString("skillGroupId");
		try {
			List<JSONObject> userList = new ArrayList<JSONObject>();
			String workGroupIdArr [] = jsonObject.getString("workGroupId").split(",");
			for(String workGroupId : workGroupIdArr) {
				userList.addAll(query.queryForList("SELECT USER_ID FROM " + this.getTableName("CC_SKILL_GROUP_USER") + " WHERE SKILL_GROUP_ID = ? AND WORK_GROUP_ID = ?", new Object[]{skillGroupId, workGroupId}, new JSONMapperImpl()));
				
				query.execute("DELETE FROM " + this.getTableName("C_CF_WORKGROUP_SKILL") + " WHERE SKILLGROUP_ID = ? AND WORKGROUP_ID = ?", skillGroupId, workGroupId);
			}
			for (JSONObject user : userList) {
				String userId = user.getString("USER_ID");
				int updateCount=query.executeUpdate("delete from " + getTableName("CC_SKILL_GROUP_USER") + " where SKILL_GROUP_ID = ? and USER_ID = ? ", new Object[]{skillGroupId,userId});
				if(updateCount>0){
					this.updateUserGroupList(query,this.getDbName(),this.getEntId(),this.getBusiOrderId(), userId);
					this.updateSkillUserCount();
				}
			}
			return EasyResult.ok("",getI18nValue("操作成功"));
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
			return EasyResult.fail(getI18nValue("移除失败！失败原因：") + e.getMessage());
		}
	}
	
	/**
	 * 删除用户
	 * @return
	 */
	public EasyResult actionForDelGroupUser(){
		EasyQuery query = getQuery();
		EasyResult result = new EasyResult();
		JSONObject jsonObject = getJSONObject();
		String type=jsonObject.getString("type");
		String skillGroupId = jsonObject.getString("skillGroupId");
		String groupName = jsonObject.getString("groupName");
		try {
			if("1".equals(type)){
				String userIdSplit[] = jsonObject.getString("userId").split(",");
				String userAccSplit[] = jsonObject.getString("userAcc").split(",");
					for(int i=0;i<userIdSplit.length;i++) {
						String userId = userIdSplit[i].toString();
						String userAcc = userAccSplit[i].toString();
						int updateCount=query.executeUpdate("delete from " + getTableName("CC_SKILL_GROUP_USER") + " where SKILL_GROUP_ID = ? and USER_ID = ? ", new Object[]{skillGroupId,userId});
						if(updateCount>0){
							this.updateUserGroupList(query,this.getDbName(),this.getEntId(),this.getBusiOrderId(), userId);
						}
						result.setMsg(getI18nValue("移除成功！"));
						this.updateSkillUserCount();//更新技能组坐席数
						getLogUtil("删除","将用户 "+userAcc+" 从技能组-"+groupName+" 移除");
						//话务能力
						if(this.getSkillCallFalg(skillGroupId)){
							String syncGroupInfo = this.syncGroupInfo(this.getEntId(),skillGroupId,"update");
							this.syncSkillCache(skillGroupId);
							if(syncGroupInfo != null){
								return EasyResult.fail(syncGroupInfo);
							}
						}
					}
			}else{
				String userId = jsonObject.getString("userId");
				String userAcc = jsonObject.getString("userAcc");
				int updateCount=query.executeUpdate("delete from " + getTableName("CC_SKILL_GROUP_USER") + " where SKILL_GROUP_ID = ? and USER_ID = ? ", new Object[]{skillGroupId,userId});
				if(updateCount>0){
					this.updateUserGroupList(query,this.getDbName(),this.getEntId(),this.getBusiOrderId(), userId);
				}
				result.setMsg(getI18nValue("移除成功！"));
				this.updateSkillUserCount();//更新技能组坐席数
				getLogUtil("删除","将用户 "+userAcc+" 从技能组-"+groupName+" 移除");
				
				//删除缓存
				CacheUtil.getCcUserCache().delCache(getEntId(), getBusiOrderId(), userId);
				
				//话务能力
				if(this.getSkillCallFalg(skillGroupId)){
					String syncGroupInfo = this.syncGroupInfo(this.getEntId(),skillGroupId,"update");
					this.syncSkillCache(skillGroupId);
					if(syncGroupInfo != null){
						return EasyResult.fail(syncGroupInfo);
					}
				}
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			result.addFail(getI18nValue("移除失败！失败原因：") + e.getMessage());
		}
		return result;
	}
	
	/**
	 * 批量删除
	 * @return
	 */
	public EasyResult actionForBatchDelUser(){
		EasyResult result = new EasyResult();
		JSONObject jsonObject=getJSONObject();
		EasyQuery query = getQuery();
		JSONArray userIds=jsonObject.getJSONArray("userIds");
		String skillGroupId = jsonObject.getString("skillGroupId");
		try {
			if(userIds!=null&&userIds.size()>0){
				EasyRecord easyRecord = new EasyRecord(getTableName("CC_SKILL_GROUP_USER"), new String[]{"SKILL_GROUP_ID","USER_ID"});
				for(int i = 0; i < userIds.size(); i ++){
					String id = userIds.getString(i);
						easyRecord.setPrimaryValues(new Object[]{skillGroupId,id});
						query.deleteById(easyRecord);
						this.updateUserGroupList(query,this.getDbName(), this.getEntId(),this.getBusiOrderId(),id);
				}
				result.setMsg(userIds.size()+getI18nValue("个坐席移除成功！"));
			}else{
				result.addFail(getI18nValue("请先选择坐席！"));
			}
			getLogUtil("修改","修改技能组或部门");
			this.updateSkillUserCount();//更新技能组坐席数
			
			//话务能力
			if(this.getSkillCallFalg(skillGroupId)){
				String syncGroupInfo = this.syncGroupInfo(this.getEntId(),skillGroupId,"update");
				this.syncSkillCache(skillGroupId);
				if(syncGroupInfo != null){
					return EasyResult.fail(syncGroupInfo);
				}
			}
		} catch (SQLException e) {
			this.error("移除坐席失败，原因："+e.getMessage(),e);
			result.addFail(getI18nValue("移除坐席失败，原因：")+e.getMessage());
		}
		return result;
	}

	
	/**
	 * 修改技能组下的所有坐席信息
	 * @param groupName
	 * @param groupId
	 * @throws SQLException 
	 */
	private void updateUserGroupList(String groupName, String groupId) throws SQLException{
		String sql = "update " + getTableName("CC_BUSI_USER") + " set GROUP_LIST = ? where BUSI_ORDER_ID = ? and ENT_ID = ? and USER_ID in (select USER_ID from "
				+getTableName("CC_SKILL_GROUP_USER")+" where SKILL_GROUP_ID = ?)";
		this.getQuery().execute(sql, new Object[]{groupName,getBusiOrderId(),getEntId(),groupId});
	}
	
	//--------------------
	
	public EasyResult actionForSynAllGroup(){
		String sql="select * from "+getTableName("CC_SKILL_GROUP")+" where ent_id = ?";
		try {
			List<JSONObject> list=this.getQuery().queryForList(sql, new Object[]{getEntId()},new JSONMapperImpl());
			if(list!=null){
				for(JSONObject jsonObject:list){
					String skillGroupId=jsonObject.getString("SKILL_GROUP_ID");
					String skillGroupName=jsonObject.getString("SKILL_GROUP_NAME");
					EasyRecord record=new EasyRecord("CC_EC_NODE","NODE_ID");
					record.set("NODE_ID",skillGroupId);
					record.set("NODE_NAME",skillGroupName);
					record.set("NODE_CODE",skillGroupId);
					record.set("NODE_TYPE", "3");
					record.set("P_DEPT_CODE", "0");
					record.set("ENT_ID", getEntId());
					record.set("CREATE_TIME", EasyDate.getCurrentDateString());
					record.set("CREATOR", UserUtil.getUser(getRequest()).getUserName());
					try {
						this.getQuery().executeUpdate("update cc_ec_user t1 set t1.NODE_ID = ? where t1.USER_ID in (select t2.USER_ID from "+getTableName("cc_skill_group_user")+" t2 where t2.SKILL_GROUP_ID = ?)",skillGroupId, skillGroupId);
						this.getQuery().save(record);
					} catch (Exception e) {
						this.error(e.getMessage(), e);
					}
				}
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok(null,getI18nValue("同步完成!"));
		
	}
	private void updateEcNode(EasyRecord skillGroup,boolean flag){
		EasyRecord record=new EasyRecord("CC_EC_NODE","NODE_ID");
		String skillGroupId=skillGroup.getString("SKILL_GROUP_ID");
		record.set("NODE_ID",skillGroup.getString("SKILL_GROUP_ID"));
		record.set("NODE_NAME", skillGroup.getString("SKILL_GROUP_NAME"));
		try {
			if(flag){
				record.set("NODE_CODE", skillGroup.getString("SKILL_GROUP_ID"));
				record.set("NODE_TYPE", "3");
				record.set("P_DEPT_CODE", "0");
				record.set("ENT_ID", getEntId());
				record.set("CREATE_TIME", EasyDate.getCurrentDateString());
				record.set("CREATOR", UserUtil.getUser(getRequest()).getUserName());
				this.getQuery().save(record);
			}else{
				try {
					boolean result=this.getQuery().update(record);
					if(!result)updateEcNode(skillGroup,true);
					this.getQuery().executeUpdate("update cc_ec_user t1 set t1.NODE_ID = ? where t1.USER_ID in (select t2.USER_ID from "+getTableName("cc_skill_group_user")+" t2 where t2.SKILL_GROUP_ID = ?)",skillGroupId, skillGroupId);
				} catch (Exception e) {
					updateEcNode(skillGroup,true);
				}
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		
	}
	public EasyResult actionForInitAdd(){
		EasyResult result = new EasyResult();
		try {
			SkillGroup skillGroup=getModel(SkillGroup.class, "skillGroup");
			skillGroup.setPrimaryValues("ivr"+this.nextSeq("CC_SKILL_GROUP"));
			skillGroup.set("ENT_ID", getEntId());
			skillGroup.set("BUSI_ORDER_ID", this.getBusiOrderId());
			skillGroup.set("AGENT_COUNT", 0);
			skillGroup.set("SKILL_GROUP_NAME", "ivr技能组");
			skillGroup.set("QUEUE_STRATEGY",1);
			skillGroup.addCreateTime();
			skillGroup.setCreator(getUserPrincipal().getUserId());
			this.getQuery().save(skillGroup);
			
			this.syncSkillCache(skillGroup.getString("SKILL_GROUP_ID"));
			String syncGroupInfo = this.syncGroupInfo(this.getEntId(),skillGroup.getString("SKILL_GROUP_ID"),"add");
			if(syncGroupInfo != null){
				result.addFail("添加失败！失败原因：" + syncGroupInfo);
			}
		} catch (SQLException e) {
			this.error("添加失败！失败原因：" + e.getMessage(), e);
			result.addFail("添加失败！失败原因：" + e.getMessage());
		}
		return result;
	}
	
	/*public EasyResult actionForUpdate(){
		EasyResult result = new EasyResult();
		JSONObject jsonObject = getJSONObject("skillGroup");
		try {
			String sql="select count(1) from " + getTableName("CC_SKILL_GROUP") + " where ENT_ID = ? and BUSI_ORDER_ID = ? and SKILL_GROUP_ID <> ? and SKILL_GROUP_NAME = ?";
			if(this.getQuery().queryForExist(sql,new Object[]{getEntId(), getBusiOrderId(), jsonObject.getString("SKILL_GROUP_ID"), StringUtils.trim(jsonObject.getString("SKILL_GROUP_NAME"))})) {
				result.addFail("该技能组名称已经存在！");
				return result;
			}
			SkillGroup skillGroup=getModel(SkillGroup.class, "skillGroup");
			this.getQuery().update(skillGroup);
			this.updateEcNode(skillGroup,false);
			updateUserGroupList(jsonObject.getString("SKILL_GROUP_NAME"), jsonObject.getString("SKILL_GROUP_ID"));
			result.setMsg("修改成功！");
			this.syncSkillCache(skillGroup.getString("SKILL_GROUP_ID"));
			String syncGroupInfo = this.syncGroupInfo(this.getEntId(),skillGroup.getString("SKILL_GROUP_ID"),"update");
			if(syncGroupInfo != null){
				result.addFail(syncGroupInfo);
			}
		} catch (SQLException e) {
			this.error("修改失败！失败原因：" + e.getMessage(), e);
			result.addFail("修改失败！失败原因：" + e.getMessage());
		}
		return result;
	}*/
	
	
	public EasyResult actionForUpdateGroupUser() {
		EasyQuery  easyQuery = this.getQuery();
		try {
			String groupId=getJSONObject().getString("groupId");
			JSONArray array=getJSONObject().getJSONArray("userIds");
			if(!isSkillById(groupId)){
				return EasyResult.fail(getI18nValue("该技能组已经被删除，无效操作！"));
			}
			easyQuery.execute("delete from "+getTableName("CC_SKILL_GROUP_USER")+" where SKILL_GROUP_ID = ?", new Object[]{groupId});
			if(array!=null && !array.isEmpty()){
				for(Object object:array){
					EasyRecord userGroup=new EasyRecord(getTableName("cc_skill_group_user"),new String[]{"SKILL_GROUP_ID","USER_ID"});
					userGroup.set("IDX_ORDER", 99);
					userGroup.set("BUSI_ORDER_ID", getBusiOrderId());
					userGroup.set("ENT_ID", getEntId());
					userGroup.setPrimaryValues(groupId,object);
					easyQuery.save(userGroup);
				}
			}
			this.updateAllUserGroupList();//
			this.updateSkillUserCount();//更新技能组坐席数
			this.syncSkillCache(groupId);
			String syncGroupInfo = this.syncGroupInfo(this.getEntId(),groupId,"update");
			if(syncGroupInfo != null){
				return EasyResult.error(501, syncGroupInfo);
			}
			return EasyResult.ok(null,getI18nValue("操作成功!"));
		} catch (SQLException ex) {
			this.error("操作失败，原因："+ex.getMessage(),ex);
			return EasyResult.error(501, "操作失败，原因："+ex.getMessage());
		}
	}	
	
	/**
	 * 下载技能组坐席模板
	 * @return
	 */
	public void actionForDownLoadTemp(){
		try {
			List<ExcelHeaderStyle> styles=new ArrayList<ExcelHeaderStyle>();
			ExcelHeaderStyle style1=new ExcelHeaderStyle();
			style1.setData("技能组名称");
			style1.setWidth(6000);
			style1.setBackgroundColor(IndexedColors.GREY_25_PERCENT.index);
			styles.add(style1);

			ExcelHeaderStyle style2=new ExcelHeaderStyle();
			style2.setData("坐席账号(不能修改)");
			style2.setWidth(6000);
			style2.setBackgroundColor(IndexedColors.GREY_25_PERCENT.index);
			styles.add(style2);

			ExcelHeaderStyle style3=new ExcelHeaderStyle();
			style3.setData("坐席姓名");
			style3.setWidth(4200);
			style3.setBackgroundColor(IndexedColors.GREY_25_PERCENT.index);
			styles.add(style3);

			ExcelHeaderStyle style6=new ExcelHeaderStyle();
			style6.setData("调整方式（加入/移除）");
			style6.setWidth(6200);
			style6.setBackgroundColor(IndexedColors.GREY_25_PERCENT.index);
			styles.add(style6);

			File tempFile=FileKit.createTempFile("技能组坐席分配模板"+RandomKit.next()+".xlsx");
			List<List<String>> list = new ArrayList<>();
			ExcelUtils.getInstance().exportObjects2Excel(list,styles, tempFile.getAbsolutePath());
			renderFile(tempFile);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 导入
	 */
	public void actionForUpload() throws IOException, ServletException, EncryptedDocumentException, InvalidFormatException{
		Part part=getFile("file");
		Workbook workbook = WorkbookFactory.create(part.getInputStream());
		List<List<String>> list = new ArrayList<>();
        Sheet sheet = workbook.getSheetAt(0);
        int maxLine =sheet.getLastRowNum();
        for (int ii = 0; ii <= maxLine; ii++) {
            List<String> rows = new ArrayList<>();
            Row row = sheet.getRow(ii);
            if(row!=null){
            	for(int j=0;j<row.getLastCellNum();j++){
            		Cell cell=row.getCell(j);
            		if(cell!=null){
            			String val = Utils.getCellValue(cell);
            			if(StringUtils.isBlank(val)){
            				rows.add("");
            			}else{
            				rows.add(val);
            			}
            		}else{
            			rows.add("");
            		}
            	}
            	for(int i = rows.size() - 1; i < 4; i++ ){
            		rows.add("");
            	}
            	list.add(rows);
            }
	    }

		if(list==null||list.size()<=1){
			renderJson(EasyResult.fail(getI18nValue("读取数据为空!")));
			return;
		}
		
        JSONObject data = new JSONObject();
        data.put("count", list.size()-1);
        JSONArray array=new JSONArray();
		for(int j=1;j<list.size();j++){
			List<String> strs=list.get(j);
			if(StringUtils.isBlank(strs.get(0))){
				continue;
			}
			JSONObject jsonObject=new JSONObject(true);
			jsonObject.put("GROUP_NAME", strs.get(0));
			jsonObject.put("AGENT_PHONE", strs.get(1));
			jsonObject.put("AGENT_NAME", strs.get(2));
			jsonObject.put("ALLOT_TYPE", strs.get(3));
			array.add(jsonObject);
		}
		data.put("listJson", array.size());
		cache.put("KILL_BATCH_UPDATE", array);
		renderJson(EasyResult.ok(data));
	}
	
	/**
	 * 保存批量分配数据
	 * @throws SQLException
	 */
	public EasyResult actionForSaveBatch() throws SQLException{
		EasyResult result = new EasyResult();
		EasyQuery query = this.getQuery();
		String entId = getEntId();
		String busiOrderId = getBusiOrderId();
		JSONArray list = (JSONArray)cache.get("KILL_BATCH_UPDATE");
		if(list==null||list.size()==0){
			renderJson(EasyResult.fail(getI18nValue("读取数据为空!")));
			result.addFail(getI18nValue("读取数据为空!"));
			return result;
		}
		
		Map<String, String> groups = groups();
		Map<String, String> users = users();
		Map<String, String> usersGroups = usersGroups();
		getLogger().info("actionForSaveBatch list:"+list);
		getLogger().info("actionForSaveBatch groups:"+groups);
		getLogger().info("actionForSaveBatch users:"+users);
		getLogger().info("actionForSaveBatch usersGroups:"+usersGroups);
		//更新用户技能组集合
		String updateUserGroupSQL = "update " + getTableName("CC_BUSI_USER") + " set GROUP_LIST=? where ENT_ID=? AND BUSI_ORDER_ID=? AND USER_ID=?";
		List<Object[]> updateUserGroupParams = new ArrayList<>();
		
		//移除技能组用户
		String delGroupUserSQL = "delete from " + getTableName("cc_skill_group_user") + " where ENT_ID=? AND BUSI_ORDER_ID=? AND USER_ID=? AND SKILL_GROUP_ID=?";
		List<Object[]> delGroupUserParams = new ArrayList<>();
		//找到的技能组id
		HashSet<String> groupIdSet = new HashSet<String>();
		for (int i=0;i<list.size();i++) {
			JSONObject row=list.getJSONObject(i);
			getLogger().info("JSONObject:"+row.toJSONString());
			String allotType = row.getString("ALLOT_TYPE");//调整方式
			String groupName = row.getString("GROUP_NAME");
			String agentPhone = row.getString("AGENT_PHONE");
			if(StringUtils.isBlank(groupName)||StringUtils.isBlank(agentPhone)){
				continue;
			}
			agentPhone = agentPhone.split("@")[0];
			String groupId = groups.get(groupName);
			String userId = users.get(agentPhone);
			String groupList = usersGroups.get(agentPhone);
			getLogger().info("groupId:"+groupId+",userId:"+userId+",groupList:"+groupList);
			if(StringUtils.isBlank(groupId)||StringUtils.isBlank(userId)){
				continue;
			}
			groupIdSet.add(groupId);
			groupList = StringUtils.isNotBlank(groupList)?groupList:"";
			
			if("加入".equals(allotType)){
				try {
					//添加技能组用户
					EasyRecord userGroup=new EasyRecord(getTableName("cc_skill_group_user"),new String[]{"SKILL_GROUP_ID","USER_ID"});
					userGroup.set("IDX_ORDER", 99);
					userGroup.set("BUSI_ORDER_ID", busiOrderId);
					userGroup.set("ENT_ID", entId);
					userGroup.setPrimaryValues(groupId,userId);
					query.save(userGroup);
				} catch (Exception e) {
					continue;
				}
				//添加用户技能组集合
				if(StringUtils.isNotBlank(groupList)){
					groupList += ","+groupName;
				}else{
					groupList = groupName;
				}
				updateUserGroupParams.add(new Object[]{groupList,entId,busiOrderId,userId});
			}else if("移除".equals(allotType)){
				//移除技能组用户
				delGroupUserParams.add(new Object[]{entId,busiOrderId,userId,groupId});
				
				//删除用户技能组集合
				if(groupList.indexOf(groupName+",")>=0){
					groupList = groupList.replace(groupName+",", "");
				}else{
					groupList = groupList.replace(groupName, "");
				}
				updateUserGroupParams.add(new Object[]{groupList,entId,busiOrderId,userId});
			}
		}
		query.executeBatch(updateUserGroupSQL, updateUserGroupParams);
		query.executeBatch(delGroupUserSQL, delGroupUserParams);
		this.updateSkillUserCount();//更新技能组坐席数
		
		ArrayList<String> syscList = new ArrayList<>();
		for (String groupId : groupIdSet) {
			this.syncSkillCache(groupId);
			String syncGroupInfo = this.syncGroupInfo(this.getEntId(),groupId,"update");
			if(syncGroupInfo != null){
				syscList.add(syncGroupInfo);
			}
		}
		if(syscList.size() > 0){
			result.addFail(syscList.get(0));
		}
		return result;
	}
	
	private Map<String, String> groups(){
		try {
			List<JSONObject> queryForList = getQuery().queryForList("select SKILL_GROUP_ID,SKILL_GROUP_NAME from "+getTableName("cc_skill_group")+" where ENT_ID=? and BUSI_ORDER_ID=?", new Object[]{getEntId(),getBusiOrderId()}, new JSONMapperImpl());
			if(queryForList==null||queryForList.size()==0){
				return null;
			}
			Map<String, String> map = new HashMap<String, String>();
			for (JSONObject jsonObject : queryForList) {
				map.put(jsonObject.getString("SKILL_GROUP_NAME"), jsonObject.getString("SKILL_GROUP_ID"));
			}
			return map;
		} catch (SQLException e) {
			e.printStackTrace();
			return null;
		}
	}
	
	private Map<String, String> users(){
		try {
			List<JSONObject> queryForList = getQuery().queryForList("select USER_ID,AGENT_PHONE from "+getTableName("CC_BUSI_USER")+" where ENT_ID=? and BUSI_ORDER_ID=?", new Object[]{getEntId(),getBusiOrderId()}, new JSONMapperImpl());
			if(queryForList==null||queryForList.size()==0){
				return null;
			}
			Map<String, String> map = new HashMap<String, String>();
			for (JSONObject jsonObject : queryForList) {
				map.put(jsonObject.getString("AGENT_PHONE"), jsonObject.getString("USER_ID"));
			}
			return map;
		} catch (SQLException e) {
			e.printStackTrace();
			return null;
		}
	}
	
	private Map<String, String> usersGroups(){
		try {
			List<JSONObject> queryForList = getQuery().queryForList("select AGENT_PHONE,GROUP_LIST from "+getTableName("CC_BUSI_USER")+" where ENT_ID=? and BUSI_ORDER_ID=?", new Object[]{getEntId(),getBusiOrderId()}, new JSONMapperImpl());
			if(queryForList==null||queryForList.size()==0){
				return null;
			}
			Map<String, String> map = new HashMap<String, String>();
			for (JSONObject jsonObject : queryForList) {
				map.put(jsonObject.getString("AGENT_PHONE"), jsonObject.getString("GROUP_LIST"));
			}
			return map;
		} catch (SQLException e) {
			e.printStackTrace();
			return null;
		}
	}
	
	/*public EasyResult actionForDelete(){
		try {
			String skillGroupId = getJsonPara("pk");
			if(getQuery().queryForExist("select count(1) from " + getTableName("cc_skill_group_user") + " where SKILL_GROUP_ID = ?", new Object[]{skillGroupId})) {
				return EasyResult.fail("该技能组存在坐席，不能删除！");
			}
			SkillGroup skillGroup=new SkillGroup(getDbName());
			skillGroup.setPrimaryValues(skillGroupId);
			this.getQuery().deleteById(skillGroup);
			List<Map<String, String>> channleIds = this.getQuery().queryForList("select CHANNEL_ID from CC_CHANNEL where ENT_ID = ?", new Object[]{this.getEntId()}, new MapRowMapperImpl());
			for (Map<String, String> map : channleIds) {
				this.sysnChanneCache(map.get("CHANNEL_ID"));
			}
			this.getQuery().execute("delete from "+getTableName("CC_SKILL_GROUP_CHANNEL")+" where ENT_ID = ? and SKILL_GROUP_ID = ?", new Object[]{this.getEntId(),skillGroupId});
			String syncGroupInfo = this.syncGroupInfo(this.getEntId(),skillGroup.getString("SKILL_GROUP_ID"),"delete");
			String reloadKey = "RELOAD_SKILLGROUP_" + skillGroupId;
			cache.delete(reloadKey);
			if(syncGroupInfo != null){
				return EasyResult.fail(syncGroupInfo);
			}
			return EasyResult.ok(null,"删除成功!");
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	}*/
	/*public EasyResult actionForDelGroupUser(){
		EasyQuery query = getQuery();
		EasyResult result = new EasyResult();
		JSONObject jsonObject = getJSONObject();
		String userId = jsonObject.getString("userId");
		String skillGroupId = jsonObject.getString("skillGroupId");
		try {
			boolean isYDX = "002".equals(this.getBusiId());
			if(jsonObject.getIntValue("flag")!=1 && isYDX){
				String sql = "select sum(OBJ_COUNT)-sum(OBJ_USE_COUNT) from "+getTableName("CC_TASK_AGENT")+" where AGENT_ID = ? and ENT_ID = ? and BUSI_ORDER_ID = ?  and TASK_STATE in(0,1)";
				int count=query.queryForInt(sql, new Object[]{userId,getEntId(),getBusiOrderId()});
				if(count>0){
					result.addFail("该用户存在"+count+"条未执行的数据,如更改技能组,将回收该用户所有未执行的数据,是否继续？");
					result.setState(2);
					return result;
				}
			}else if(jsonObject.getIntValue("flag")==1 && isYDX){			//判断是否回收任务
				String sql = "select distinct TASK_ID from "+getTableName("CC_TASK_AGENT")+" where OBJ_COUNT > OBJ_USE_COUNT and AGENT_ID = ? and BUSI_ORDER_ID = ? and TASK_STATE in(0,1)";
				String sql2 = "update "+getTableName("CC_TASK_AGENT")+" set OBJ_COUNT = OBJ_USE_COUNT where AGENT_ID = ? and TASK_ID = ?";
				List<JSONObject> list = query.queryForList(sql, new Object[]{userId,getBusiOrderId()},new JSONMapperImpl());
				if(list != null && list.size() > 0){						//遍历所有任务
					sql="update "+getTableName("CC_TASK_OBJ")+" set AGENT_ID='0',ALLOC_AGENT_TIME=null,RUN_DATE=0 where TASK_STATE = ? and AGENT_ID = ? and ENT_ID = ? and TASK_ID = ?";
					for (JSONObject row : list) {
						query.executeUpdate(sql, new Object[]{0,userId,getEntId(),row.getString("TASK_ID")});	//回收任务
						query.execute(sql2, new Object[]{userId,row.getString("TASK_ID")});						//任务总数置为使用数
					}
				}
			}
			int updateCount=query.executeUpdate("delete from " + getTableName("CC_SKILL_GROUP_USER") + " where SKILL_GROUP_ID = ? and USER_ID = ? ", new Object[]{skillGroupId,userId});
			if(updateCount>0){
				this.updateUserGroupList(query, userId);
				this.updateSkillUserCount();
			}
			result.setMsg("移除成功！");
			this.updateSkillUserCount();//更新技能组坐席数
			String syncGroupInfo = this.syncGroupInfo(this.getEntId(),skillGroupId,"update");
			this.syncSkillCache(skillGroupId);
			if(syncGroupInfo != null){
				return EasyResult.fail(syncGroupInfo);
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			result.addFail("移除失败！失败原因：" + e.getMessage());
		}
		return result;
	}*/
	
	public EasyResult actionForUpdateGroupUserOrder(){
		EasyQuery query = this.getQuery();
		try {
			String mgrType = this.getJSONObject().getString("mgrType");
			if("1".equals(mgrType)) {
				JSONObject gkuser = this.getJSONObject("gkuser");
				String userAcc = this.getRequest().getParameter("userAcc");
				String skillGroupName = this.getRequest().getParameter("skillGroupName");
				String indexOrder = gkuser.getString("IDX_ORDER");
				EasyRecord userGroup=new EasyRecord(getTableName("cc_skill_group_user"),new String[]{"SKILL_GROUP_ID","USER_ID"});
				userGroup.setColumns(gkuser);
				getQuery().update(userGroup);
				String syncGroupInfo = this.syncGroupInfo(this.getEntId(),userGroup.getString("SKILL_GROUP_ID"),"update");
				this.syncSkillCache(userGroup.getString("SKILL_GROUP_ID"));
				if(syncGroupInfo != null){
					return EasyResult.fail(syncGroupInfo);
				}
				getLogUtil("修改","更改技能组- "+skillGroupName+" 中 "+userAcc+" 的优先级为："+indexOrder);
			} else {
				JSONObject gkwork = this.getJSONObject("gkwork");
				EasyRecord record = new EasyRecord(this.getTableName("C_CF_WORKGROUP_SKILL"), "ID");
				record.setColumns(gkwork);
				query.update(record);
				String skillGroupId = gkwork.getString("SKILLGROUP_ID");
				String workGroupId = gkwork.getString("WORKGROUP_ID");
				int priority = gkwork.getIntValue("PRIORITY");
				List<JSONObject> userList = this.getQuery().queryForList("SELECT USER_ID FROM " + this.getTableName("CC_SKILL_GROUP_USER") + " WHERE SKILL_GROUP_ID = ? AND WORK_GROUP_ID = ? ORDER BY IDX_ORDER", new Object[] {skillGroupId, workGroupId}, new JSONMapperImpl());
				int i = 1;
				for(JSONObject user : userList) {
					EasyRecord groupUserRecord = new EasyRecord(this.getTableName("CC_SKILL_GROUP_USER"), "SKILL_GROUP_ID", "USER_ID");
					groupUserRecord.put("SKILL_GROUP_ID", skillGroupId);
					groupUserRecord.put("USER_ID", user.getString("USER_ID"));
					groupUserRecord.put("WORK_GROUP_ID", workGroupId);
					groupUserRecord.put("IDX_ORDER", priority * 100 + i);
					query.update(groupUserRecord);
					i++;
				}
			}
			return EasyResult.ok(null,getI18nValue("修改成功!"));
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	/**
	 * 批量更新技能组优先级
	 * @return
	 */
	public EasyResult actionForBatchUpdateGroupUserOrder(){
		EasyQuery query = getQuery();
		try {
			JSONObject gkuser = getJSONObject("gkuser");
			String userAcc = gkuser.getString("USER_ACC");
			String indexOrder = gkuser.getString("IDX_ORDER");
			ArrayList<Object[]> list = new ArrayList<>();
			String userIds = gkuser.getString("USER_ID");
			String skillGroupId = gkuser.getString("SKILL_GROUP_ID");
			String skillGroupName = gkuser.getString("SKILL_GROUP_NAME");
			String idxOrder = gkuser.getString("IDX_ORDER");
			if(StringUtils.isNotBlank(userIds)){
				String[] idArr = userIds.split(",");
				if(idArr!=null&&idArr.length>0){
					for (String id : idArr) {
						list.add(new Object[]{idxOrder,skillGroupId,id});
					}
				}
			}
			if(list.size()>0){
				query.executeBatch("UPDATE "+getTableName("cc_skill_group_user") +" SET IDX_ORDER = ? where SKILL_GROUP_ID = ? and USER_ID = ?", list);
			}
			String syncGroupInfo = this.syncGroupInfo(this.getEntId(),skillGroupId,"update");
			this.syncSkillCache(skillGroupId);
			if(syncGroupInfo != null){
				return EasyResult.fail(syncGroupInfo);
			}
			getLogUtil("修改","更改技能组- "+skillGroupName+" 中 "+userAcc+" 的优先级为："+indexOrder);
			return EasyResult.ok(null,getI18nValue("修改成功!"));
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	/**
	 * 批量更新技能组优先级
	 * @return
	 */
	public EasyResult actionForBatchUpdateWorkGroupOrder(){
		EasyQuery query = getQuery();
		try {
			JSONObject gkwork = getJSONObject("gkwork");
			String workGroupId = gkwork.getString("WORKGROUP_ID");
			int priority = gkwork.getIntValue("PRIORITY");
			ArrayList<Object[]> list = new ArrayList<>();
			String skillGroupId = gkwork.getString("SKILLGROUP_ID");
			List<JSONObject> userList = query.queryForList("SELECT USER_ID FROM " + this.getTableName("CC_SKILL_GROUP_USER") + " WHERE SKILL_GROUP_ID = ? AND WORK_GROUP_ID IN ('" + String.join("','",workGroupId.split(",")) + "')", new Object[] {skillGroupId}, new JSONMapperImpl());
			if(StringUtils.isNotBlank(workGroupId)){
				String[] idArr = workGroupId.split(",");
				if(idArr!=null&&idArr.length>0){
					for (String id : idArr) {
						list.add(new Object[]{priority,skillGroupId,id});
					}
				}
			}
			if(list.size()>0){
				query.executeBatch("UPDATE "+getTableName("C_CF_WORKGROUP_SKILL") +" SET PRIORITY = ? where SKILLGROUP_ID = ? and WORKGROUP_ID = ?", list);
			}
			
			int i = 1;
			for (JSONObject user : userList) {
				EasyRecord groupUserRecord = new EasyRecord(this.getTableName("CC_SKILL_GROUP_USER"), "SKILL_GROUP_ID", "USER_ID");
				groupUserRecord.put("SKILL_GROUP_ID", skillGroupId);
				groupUserRecord.put("USER_ID", user.getString("USER_ID"));
				groupUserRecord.put("IDX_ORDER", priority * 100 + i);
				query.update(groupUserRecord);
				i++;
			}
			
			String syncGroupInfo = this.syncGroupInfo(this.getEntId(),skillGroupId,"update");
			this.syncSkillCache(skillGroupId);
			if(syncGroupInfo != null){
				return EasyResult.fail(syncGroupInfo);
			}
			getLogUtil("修改","更改技能组- "+skillGroupId+" 中 "+workGroupId+" 的优先级为："+priority);
			return EasyResult.ok(null,getI18nValue("修改成功!"));
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	
	/**
	 * 批量删除
	 * @return
	 *//*
	public EasyResult actionForBatchDelUser(){
		EasyResult result = new EasyResult();
		JSONObject jsonObject=getJSONObject();
		EasyQuery query = getQuery();
		int errorCount = 0;
		JSONArray userIds=jsonObject.getJSONArray("userIds");
		String skillGroupId = jsonObject.getString("skillGroupId");
		try {
			query.begin();
			if(userIds!=null&&userIds.size()>0){
				String sql="select AGENT_ID from "+getTableName("CC_TASK_OBJ")+" where GROUP_ID = ? and TASK_STATE= 0 and ENT_ID = ?  and BUSI_ORDER_ID= ? group by AGENT_ID";
				List<Map<String, String>> list = this.getQuery().queryForList(sql, new Object[]{skillGroupId,getEntId(),getBusiOrderId()}, new MapRowMapperImpl());
				HashSet<String> userIdSet = null;
				if(list != null && list.size() > 0) {
					userIdSet = new HashSet<>(userIds.size() + list.size());
					for(int i = 0; i < list.size(); i++){
						userIdSet.add(list.get(i).get("AGENT_ID"));
					}
				}else{
					userIdSet = new HashSet<>(userIds.size());
				}
				EasyRecord easyRecord = new EasyRecord(getTableName("CC_SKILL_GROUP_USER"), new String[]{"SKILL_GROUP_ID","USER_ID"});
				for(int i = 0; i < userIds.size(); i ++){
					String id = userIds.getString(i);
					if(!userIdSet.add(id)){
						errorCount++;
					}else{
						easyRecord.setPrimaryValues(new Object[]{skillGroupId,id});
						query.deleteById(easyRecord);
						this.updateUserGroupList(query, id);
					}
				}
				if(errorCount >= userIds.size()){
					result.addFail("所有坐席都存在未执行的任务，不能移除！");
				}else if(errorCount > 0){
					result.setMsg((userIds.size()-errorCount)+"个坐席移除成功！"+errorCount+"个坐席存在未执行的任务,不能移除！");
				}else{
					result.setMsg(userIds.size()+"个坐席移除成功！");
				}
			}else{
				result.addFail("请先选择坐席！");
			}
			query.commit();
			this.updateSkillUserCount();//更新技能组坐席数
			this.updateSkillUserCount();
			String syncGroupInfo = this.syncGroupInfo(this.getEntId(),skillGroupId,"update");
			this.syncSkillCache(skillGroupId);
			if(syncGroupInfo != null){
				return EasyResult.fail(syncGroupInfo);
			}
		} catch (SQLException e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				this.error("移除坐席失败，原因："+e1.getMessage(),e1);
				result.addFail("移除坐席失败，原因："+e1.getMessage());
			}
			this.error("移除坐席失败，原因："+e.getMessage(),e);
			result.addFail("移除坐席失败，原因："+e.getMessage());
		}
		return result;
	}*/
	
	/**
	 * 添加服务渠道
	 */
	public EasyResult actionForAddGroupChannle(){
		try {
			JSONObject json = this.getJSONObject("skillGroup");
			if(!isSkillById(json.getString("SKILL_GROUP_ID"))){
				return EasyResult.fail(getI18nValue("该技能组已经被删除，无效操作！"));
			}
			EasyRecord record = new EasyRecord(getTableName("CC_SKILL_GROUP_CHANNEL"));
			record.setColumns(json);
			record.set("ENT_ID", this.getEntId());
			record.set("BUSI_ORDER_ID", this.getBusiOrderId());
			record.set("CREATOR", this.getUserPrincipal().getUserName());
			record.set("CREATE_TIME", EasyDate.getCurrentDateString());
			this.getQuery().save(record);

			String syncGroupInfo = this.syncGroupInfo(this.getEntId(),record.getString("SKILL_GROUP_ID"),"update");
			this.syncSkillCache(record.getString("SKILL_GROUP_ID"));
			this.sysnChanneCache(record.getString("CHANNEL_ID"));
			if(syncGroupInfo != null){
				return EasyResult.fail(syncGroupInfo);
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
		return EasyResult.ok("",getI18nValue("操作成功"));
	}
	
	/**
	 * 移除服务渠道
	 */
	public EasyResult actionForDeleteGroupChannle(){
		JSONObject json = this.getJSONObject();
		try {
			this.getQuery().execute("delete from "+getTableName("CC_SKILL_GROUP_CHANNEL")+" where SKILL_GROUP_ID = ? and CHANNEL_ID = ? and KEY_ID = ?",
					new Object[]{json.getString("skillGroupId"),json.getString("channelId"),json.getString("keyId")});

			String syncGroupInfo = this.syncGroupInfo(this.getEntId(),json.getString("skillGroupId"),"update");
			this.syncSkillCache(json.getString("skillGroupId"));
			this.sysnChanneCache(json.getString("channelId"));
			if(syncGroupInfo != null){
				return EasyResult.fail(syncGroupInfo);
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
		return EasyResult.ok("",getI18nValue("操作成功"));
	}

	@Override
	protected String getResId() {
		return null;
	}

	/**
	 * 修改技能组下的所有坐席信息
	 * @param groupName
	 * @param groupId
	 * @throws SQLException 
	 *//*
	private void updateUserGroupList(String groupName, String groupId) throws SQLException{
		String sql = "update " + getTableName("CC_BUSI_USER") + " set GROUP_LIST = ? where BUSI_ORDER_ID = ? and ENT_ID = ? and USER_ID in (select USER_ID from "
				+getTableName("CC_SKILL_GROUP_USER")+" where SKILL_GROUP_ID = ?)";
		this.getQuery().execute(sql, new Object[]{groupName,getBusiOrderId(),getEntId(),groupId});
	}*/
	
	private void sysnChanneCache(String channelId){
		String reloadKey = "RELOAD_CHANNEL_" + channelId;
		cache.put(reloadKey, System.currentTimeMillis() + "",3600);
	}
	
	/**
	 * 更新所有用户的技能组名称(冗余字段)
	 */
	private void updateAllUserGroupList(){
		try {
			EasyQuery query = this.getQuery();
			EasySQL sql = new EasySQL();
			sql.append(" SELECT DISTINCT T1.USER_ID FROM "+getTableName("CC_SKILL_GROUP_USER")+" T1 ,"+getTableName("CC_SKILL_GROUP")+" T2 WHERE T1.SKILL_GROUP_ID  = T2.SKILL_GROUP_ID AND T2.ENT_ID=? AND T2.BUSI_ORDER_ID=? AND T2.SKILL_GROUP_TYPE !=? ");
			sql.append(" UNION ");
			sql.append(" SELECT DISTINCT USER_ID FROM "+getTableName("CC_BUSI_USER")+" WHERE ENT_ID=? AND BUSI_ORDER_ID=? AND( GROUP_LIST !='' OR GROUP_LIST IS NOT  NULL) ");
			
			String entId = this.getEntId();
			String busiOrderId = this.getBusiOrderId();
			String dbName = this.getDbName();
			
//			sql.append("select USER_ID from "+getTableName("CC_BUSI_USER")+" where ENT_ID = ? and BUSI_ORDER_ID = ?");
			List<EasyRow> list = query.queryForList(sql.getSQL(),new Object[] { entId, busiOrderId, Constants.SKILL_GROUP_TYPE_STRUCT ,this.getEntId(),this.getBusiOrderId()});
			query.setMaxRow(20000);
			if (list != null && list.size() > 0) {
				new Thread() {
					public void run() {
						logger.info("本次需要更新技能组名称的用户量:"+list.size());
						for (EasyRow easyRow : list) {
							String userId = easyRow.getColumnValue("USER_ID");
							try {
								updateUserGroupList(query,dbName,entId,busiOrderId, userId);
							} catch (SQLException e) {
								logger.error("更新坐席["+userId+"]技能组名称失败:"+e.getMessage(),e);
							}
						}
					}
				}.start();
			}
		} catch (SQLException e) {
			logger.error("更新坐席技能组名称失败:"+e.getMessage(),e);
		}
	}
	
	
	/**
	 * 获取技能组Id
	 * @param tableName
	 * @return
	 */
	protected synchronized int nextSeq(String tableName){
		tableName = tableName.toUpperCase();
		String sql = "select SEQ_ID from CC_SEQ  where TABLE_ID = ?";
		int seq = 0;
		try {
			String seqIdStr = this.getQuery().queryForString(sql, new Object[]{tableName});
			if(StringUtils.isNotBlank(seqIdStr)){
				seq = Integer.parseInt(seqIdStr);
			}
		} catch (Exception ex) {
			 this.error("AppBaseServlet.nextSeq() error->cause:"+ex.getMessage(), ex);
		}
		if(seq <=0){
			try {
				sql = "insert into CC_SEQ(TABLE_ID,SEQ_ID) values (?,?)";
				this.getQuery().execute(sql, new Object[]{tableName,1});
			} catch (Exception ex) {
				this.error("AppBaseServlet.nextSeq() error->cause:"+ex.getMessage(), ex);
			}
		}
		seq = seq +1;
		sql = "update CC_SEQ set SEQ_ID = ? where TABLE_ID = ?  ";
		try {
			this.getQuery().executeUpdate(sql, new Object[]{seq,tableName});
		} catch (Exception ex) {
			this.error("AppBaseServlet.nextSeq() error->cause:"+ex.getMessage(), ex);
		}
		return seq;
	}
	
	/**
	 * 判断技能组是否属于外呼权限
	 * @param skillId
	 * @return
	 */
	private boolean getSkillCallFalg(String skillId){
		try {
			return this.getQuery().queryForExist("select count(1) from "+getTableName("CC_SKILL_GROUP t1,")+getTableName("CC_GROUP_TYPE t2")+" where t1.GROUP_TYPE = t2.GROUP_TYPE and t2.CALL_FLAG = 1 and t1.SKILL_GROUP_ID = ?", new Object[]{skillId});
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return false;
	}
	
	/**
	 * 更新坐席技能组名称
	 * 需要支持在线程中执行，不能获取请求对象
	 * @param userId
	 * @throws SQLException
	 */
	private void updateUserGroupList(EasyQuery query,String schema, String entId,String busiOrderId, String userId) throws SQLException{
		String grouName = "";
		List<EasyRow> list = query.queryForList("select t1.SKILL_GROUP_NAME from "+schema+".CC_SKILL_GROUP"+" t1,"+schema+".CC_SKILL_GROUP_USER"+" t2 where t1.SKILL_GROUP_ID = t2.SKILL_GROUP_ID and t2.ENT_ID = ? and t2.BUSI_ORDER_ID = ? and t2.USER_ID = ? and t1.SKILL_GROUP_TYPE != ?", new Object[]{entId,busiOrderId, userId,Constants.SKILL_GROUP_TYPE_STRUCT});
		if(list != null){
			for (EasyRow easyRow : list) {
				grouName += "," + easyRow.getColumnValue("SKILL_GROUP_NAME");
			}
			if(grouName.length() > 1){
				grouName = grouName.substring(1);
			}
		}
		query.execute("update "+schema+".CC_BUSI_USER"+ " set GROUP_LIST = ? where ENT_ID=? and BUSI_ORDER_ID = ? and USER_ID = ?", new Object[]{grouName,entId,busiOrderId, userId});
	}
	/**
	 * 判断技能组是否存在
	 * @throws SQLException 
	 */
	private boolean isSkillById(String groupId) throws SQLException{
		return this.getQuery().queryForExist("select count(1) from "+getTableName("CC_SKILL_GROUP")+" where SKILL_GROUP_ID = ?", new Object[]{groupId});
	}
	
	/**
	 * 将库里所有技能组全部同步给iccs
	 * @return
	 */
	public EasyResult actionForSyncAllSkillGroup(){
		try {
			logger.info(CommonUtil.getClassNameAndMethod(this)+" 开始同步所有的技能组到ICCS...");
			List<EasyRow> list = this.getQuery().queryForList("select SKILL_GROUP_ID from "+getTableName("CC_SKILL_GROUP") + " where ENT_ID = ? and BUSI_ORDER_ID = ? and SKILL_GROUP_TYPE != ?", new Object[]{this.getEntId(),this.getBusiOrderId(),"struct"});
			if(CommonUtil.listIsNotNull(list)){
				for(EasyRow row : list){
					String skillGroupId = row.getColumnValue("SKILL_GROUP_ID");
					try {
						logger.info(CommonUtil.getClassNameAndMethod(this)+" 同步技能组,skillGroupId:"+skillGroupId+",skillGroupName:"+row.getColumnValue("SKILL_GROUP_NAME"));

						String syncGroupInfo = this.syncGroupInfo(this.getEntId(),skillGroupId,"update");
						this.syncSkillCache(skillGroupId);
						if(syncGroupInfo != null){
							logger.error(CommonUtil.getClassNameAndMethod(this)+" 同步技能组失败,skillGroupId:"+skillGroupId+",skillGroupName:"+row.getColumnValue("SKILL_GROUP_NAME")+","+syncGroupInfo);
						}
					} catch (Exception e) {
						logger.error(CommonUtil.getClassNameAndMethod(this)+" 同步技能组异常,skillGroupId:"+skillGroupId+",skillGroupName:"+row.getColumnValue("SKILL_GROUP_NAME"),e);
					}
					
				}
			}
			
			logger.info(CommonUtil.getClassNameAndMethod(this)+" 结束同步所有的技能组到ICCS...");
		}catch(Exception e){
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 同步技能组异常",e);
			return EasyResult.error();
		}
		return EasyResult.ok("",getI18nValue("操作成功"));
	}
	
	/**
	 * 更新用户的技能组
	 */
	public EasyResult actionForSetSkill(){
		//用户新的技能组集合
		String ids=getJSONObject().getString("ids");
    	String userId = getPara("userId");
    	
		EasyQuery query = this.getQuery();
		//用户最新的技能组
		List<String> addList = new ArrayList<String>();
		//用户已有，无需变化的技能组
		List<String> existList = new ArrayList<String>();
		//发生变化的技能组
		List<String> changeList = new ArrayList<String>();
		
		if(StringUtils.isBlank(ids) || StringUtils.isBlank(userId)){
			logger.info("设置用户技能组为空,ids="+ids+",userId="+userId);
			try {
				query.executeUpdate("delete from "+getTableName("CC_SKILL_GROUP_USER")+" where USER_ID = ? and ENT_ID = ? and BUSI_ORDER_ID = ?",userId,getEntId(),getBusiOrderId());
			} catch (SQLException e) {
				logger.error("设置用户技能组失败:参数,ids="+ids+",userId="+userId);
				return EasyResult.fail(getI18nValue("设置用户技能组失败,参数为空!")); 
			}
			this.updateAllUserGroupList();
			this.updateSkillUserCount();//更新技能组坐席数
			return EasyResult.ok(getI18nValue("设置用户技能组为空!")); 
		}
		
    	try {	
    		String[] split = ids.split(",");
			for (String string : split) {
				addList.add(string);
			}
			
    		//查询用户关联的技能组列表
    		List<JSONObject> list = query.queryForList("select t1.SKILL_GROUP_ID from "+getTableName("CC_SKILL_GROUP_USER")+" t1 left join " + this.getTableName("CC_SKILL_GROUP") + " T2 ON T1.SKILL_GROUP_ID = T2.SKILL_GROUP_ID where t1.USER_ID = ? and t1.ENT_ID = ? and t1.BUSI_ORDER_ID = ? AND T2.SKILL_GROUP_TYPE != ? ", new Object[]{userId,getEntId(),getBusiOrderId(), "struct"},new JSONMapperImpl());
    		if(list!=null&&list.size()>0){
    			for (int i = 0; i < list.size(); i++) {
					String skillGroupId = list.get(i).getString("SKILL_GROUP_ID");
					
	    			if(!addList.contains(skillGroupId)){//用户已没有该技能组，可以删除数据
	    				query.executeUpdate("delete from "+getTableName("CC_SKILL_GROUP_USER")+" where USER_ID = ? and ENT_ID = ? and BUSI_ORDER_ID = ? and SKILL_GROUP_ID = ?",userId,getEntId(),getBusiOrderId(),skillGroupId);
	    				changeList.add(skillGroupId);
	    			}else{
	    				//不增加列表
	    				existList.add(skillGroupId);
	    			}
				}
    		}
			EasyRecord record = new EasyRecord(getTableName("CC_SKILL_GROUP_USER"),"SKILL_GROUP_ID","USER_ID");
			record.set("BUSI_ORDER_ID", this.getBusiOrderId());
			record.set("ENT_ID", this.getEntId());
			record.set("USER_ID", userId);
			record.set("IDX_ORDER", 0);
    		for(int i = 0 ;i<addList.size();i++){
    			String skillGroupId = addList.get(i);
    			//已存在的技能组中不包含新的技能组，则需要添加
    			if(!existList.contains(skillGroupId)){
    				record.set("SKILL_GROUP_ID", addList.get(i));
    				query.save(record);
    				changeList.add(skillGroupId);
    			}
    		}
    		this.updateAllUserGroupList();
			this.updateSkillUserCount();//更新技能组坐席数
			
			//发生变化的技能组需要进行同步
			if(changeList!=null&&changeList.size()>0){
				for (String groupId : changeList) {
					//话务能力
					if(this.getSkillCallFalg(groupId)){
						this.syncSkillCache(groupId);
						String syncGroupInfo = this.syncGroupInfo(this.getEntId(),groupId,"update");
						if(syncGroupInfo != null){
							return EasyResult.error(501, syncGroupInfo);
						}
					}
				}
				
			}
			return EasyResult.ok(null, getI18nValue("设置成功！"));
		} catch (Exception ex) {
			logger.error("设置用户技能组失败，原因："+ex.getMessage(),ex);
			return EasyResult.fail("设置用户技能组失败，原因："+ex.getMessage());
		}
	}
	
	/**
	 * 操作日志
	 * @param type：新增还是修改还是删除
	 * @param content: 日志描述
	 */
	public void getLogUtil(String type,String content){
		UserModel  user=UserUtil.getUser(getRequest());
		Yqlogger log=new Yqlogger();
		log.setCreateAcc(user.getUserAcc());
		log.setCreateName(user.getUserName());
		log.setCreateTime(DateUtil.getCurrentDateStr());
		log.setModule(Constants.APP_NAME);
		String yqlongerType="";
		if("新增".equals(type)){
			yqlongerType=Yqlogger.OPER_TYPE_ADD;
		}else if("修改".equals(type)){
			yqlongerType=Yqlogger.OPER_TYPE_UPDATE;
		}else{
			yqlongerType=Yqlogger.OPER_TYPE_DEL;
		}
		log.setOperType(yqlongerType);
		log.setContent(user.getUserAcc()+content);
		log.setBakup("");
		log.setEntId(user.getEpCode());
		log.setBusiOrderId(user.getBusiOrderId());
		LogUtil.insertLog(getDbName(),log);
	}
	
	
	/**
	 * 删除成员
	 */
	public JSONObject actionForDelWorkUser() {
		JSONObject obj = getJSONObject();
		try {
			if(obj!=null&&obj.size()>0) {
				EasyRecord record = new EasyRecord(getTableName("CC_SKILL_GROUP_USER"),"SKILL_GROUP_ID","USER_ID");
				record.put("SKILL_GROUP_ID",obj.getString("SKILL_GROUP_ID"));
				record.put("USER_ID",obj.getString("USER_ID"));
				record.put("WORK_GROUP_ID",obj.getString("WORK_GROUP_ID"));
				record.put("ENT_ID",getEntId());
				record.put("BUSI_ORDER_ID",getBusiOrderId());
				this.getQuery().deleteById(record);
			}
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "删除人员出错：" + e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok("",getI18nValue("操作成功"));
	}
	
    /**
     * 更改用户部门
     */
    public EasyResult actionForUpdateUserNode(){
    	EasyQuery query = this.getQuery();
    	JSONArray jsonArray = this.getJSONArray();
    	String userId = this.getRequest().getParameter("userId");
    	String userAcc = this.getRequest().getParameter("userAcc");
    	String skillGroupId = this.getRequest().getParameter("deptID");
    	String[] useIds = userId.split(",");
    	String[] userAccs = userAcc.split(",");
     
    	try {
    		query.begin();
    		if(jsonArray != null && jsonArray.size() > 0){
    			JSONObject json = jsonArray.getJSONObject(0);
    			String resName = json.getString("resName");
    			if(json != null && StringUtils.isNotBlank(json.getString("resId"))){
    				String resId = json.getString("resId");
    				if(resId.equals(skillGroupId)){
    					return EasyResult.fail(getI18nValue("不可重新分配当前部门，请重新选择"));
    				}
    				
    					for(int i=0;i<useIds.length;i++){
    						String uerid = useIds[i];
    						
    						EasySQL sql=new EasySQL("select count(1) from "+getTableName("CC_SKILL_GROUP_USER")+" where 1=1  AND BUSI_ORDER_ID=? AND ENT_ID=?  and SKILL_GROUP_ID=? AND USER_ID=?");
    	    				Boolean isY=this.getQuery().queryForExist(sql.getSQL(),new Object[]{uerid,resId,getEntId(),getBusiOrderId()});
    						//删除用户关联的原先部门
    						this.getQuery().execute("delete from " + getTableName("CC_SKILL_GROUP_USER") + " where USER_ID = ? and ENT_ID = ? and BUSI_ORDER_ID = ? "
    								+" and SKILL_GROUP_ID = ?", 
    								new Object[]{uerid,getEntId(),getBusiOrderId(),skillGroupId});
    						//新增用户部门关系
    						String[]  keys = {"SKILL_GROUP_ID","USER_ID"};
    						EasyRecord easyRecord = new EasyRecord(getTableName("CC_SKILL_GROUP_USER"),keys);
    						easyRecord.set("SKILL_GROUP_ID",resId);
    						easyRecord.set("USER_ID", uerid);
    						easyRecord.set("BUSI_ORDER_ID", getBusiOrderId());
    						easyRecord.set("ENT_ID", getEntId());
    						easyRecord.set("IDX_ORDER", 1);//默认设置1
    						if(isY){
    							query.update(easyRecord);
    						}else{
    							this.getQuery().save(easyRecord);
        						updateSkillUserCount();
        						getLogUtil("修改","为用户 "+userAccs[i]+" 分配到部门-"+resName);
    						}
    					}
    			}
    		}
    		query.commit();
			return EasyResult.ok(null, getI18nValue("分配用户部门成功！"));
		} catch (Exception ex) {
			try {
				query.roolback();
			} catch (SQLException e) {
				logger.error("e"+e.getMessage());
			}
			logger.error(CommonUtil.getClassNameAndMethod(this)+"更新用户部门失败，原因：" + ex.getMessage(), ex);
			return EasyResult.fail(getI18nValue("更新用户部门失败，原因：")+ex.getMessage());
		}
    }
	
}
