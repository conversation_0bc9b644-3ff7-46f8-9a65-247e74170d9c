package com.yunqu.cc.base.servlet.system;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.apache.log4j.Logger;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.annontation.InfAuthCheck;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.BaseI18nUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.base.base.AppBaseServlet;
import com.yunqu.cc.base.base.CommonLogger;
import com.yunqu.cc.base.base.Constants;

@WebServlet("/servlet/queue")
public class QueueServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
    
	private Logger logger = CommonLogger.logger;
	
	//新增
	@InfAuthCheck(resId = "cc-base-org-queuegl", msg="无权访问")
	public JSONObject actionForAddQueue() {
		JSONObject data = getJSONObject("QUEUE");
		JSONArray skillArr = getJSONObject().getJSONArray("skillGroup");
		JSONArray skillList = new JSONArray();
		int id = CommonUtil.nextSeq(null, "CC_SKILL_GROUP");//不能与技能组ID重复
		UserModel user = UserUtil.getUser(getRequest());
		try {
			if(data!=null&&skillArr!=null&&data.size()>0&&skillArr.size()>0) {
				EasyRecord ercd = new EasyRecord(getTableName("CC_SKILL_QUEUE_GROUP"));
				for(int i=0;i<skillArr.size();i++) {
					JSONObject obj = skillArr.getJSONObject(i);
					JSONObject skillData = new JSONObject();
					skillData.put("ID", obj.getString("ID"));
					skillData.put("NAME", obj.getString("NAME"));
					skillList.add(skillData);
					ercd.put("QUEUE_GROUP_ID",RandomKit.randomStr());
					ercd.put("ENT_ID",getEntId());
					ercd.put("BUSI_ORDER_ID",getBusiOrderId());
					ercd.put("QUEUE_ID",id);
					ercd.put("SKILL_GROUP_ID",obj.getString("ID"));
					ercd.put("CREATOR",user.getUserAcc());
					ercd.put("CREATE_TIME",EasyCalendar.newInstance().getDateTime("-"));
					getQuery().save(ercd);
				}
					data.put("QUEUE_ID", id);
					data.put("CREATE_ACC",user.getUserAcc());
					data.put("CREATE_NAME",user.getUserName());
					data.put("CREATE_DEPT",user.getDeptCode());
					data.put("CREATE_DEPT_NAME",user.getDeptName());
					data.put("CREATE_TIME",EasyCalendar.newInstance().getDateTime("-"));
					data.put("ENT_ID",getEntId());
					data.put("BUSI_ORDER_ID",getBusiOrderId());
					data.put("SKILL_GROUP_LIST",skillList.toJSONString());
					EasyRecord record = new EasyRecord(getTableName("CC_SKILL_QUEUE")).setColumns(data);
					getQuery().save(record);
			}else {
				return EasyResult.error();
			}
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "新增队列出错:" + e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok("",getI18nValue("新增成功"));
	}

	//修改
	@InfAuthCheck(resId = "cc-base-org-queuegl", msg="无权访问")
	public JSONObject actionForEditQueue() {
		EasyQuery query = getQuery();
		JSONObject data = getJSONObject("QUEUE");
		String queueId = getJsonPara("QUEUE.QUEUE_ID");
		JSONArray skillArr = getJSONObject().getJSONArray("skillGroup");
		JSONArray skillList = new JSONArray();
		UserModel user = UserUtil.getUser(getRequest());
		try {
			query.begin();
			//检查是否有渠道使用此队列，如果有则无法修改类型（针对全媒体）
			if(query.queryForInt("SELECT COUNT(1) FROM " + getTableName("CC_SKILL_QUEUE_CHANNEL") + " WHERE QUEUE_ID = ?", queueId) > 0) {
				if("01".equals(data.getString("QUEUE_TYPE"))) {
					query.roolback();
					return EasyResult.error(500,BaseI18nUtil.getI18nValue(getRequest(), Constants.APP_NAME, "有渠道正在使用此全媒体队列，无法修改队列类型"));
				}
			}

			String skillGroups = getJSONObject().getString("skillGroups");
			List<String> addList = new ArrayList<String>(Arrays.asList(skillGroups.split(",")));// 保存新增的技能组list
			EasySQL sql = new EasySQL("SELECT SKILL_GROUP_ID FROM "+getTableName("CC_SKILL_QUEUE_GROUP"));
			sql.append(queueId,"WHERE QUEUE_ID = ?");
			List<JSONObject> skillGroupList = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			List<String> delList = new ArrayList<String>();// 保存删除的技能list
			for(int i=0;i<skillGroupList.size();i++) {
				delList.add(skillGroupList.get(i).getString("SKILL_GROUP_ID"));
			}
			List<String> tempList = new ArrayList<String>(addList);
			addList.removeAll(delList);
			delList.removeAll(tempList);
			//删除技能组关联信息
			if(!delList.isEmpty()) {
				EasySQL sql2 = new EasySQL("DELETE FROM "+getTableName("CC_SKILL_QUEUE_GROUP"));
				sql2.append("WHERE SKILL_GROUP_ID IN (\""+StringUtils.join(delList.toArray(), "\",\"")+"\")");
				sql2.append(queueId,"AND QUEUE_ID = ?");
				query.execute(sql2.getSQL(), sql2.getParams());
			}
			//添加技能组关联信息
			EasyRecord ercd = new EasyRecord(getTableName("CC_SKILL_QUEUE_GROUP"));
			for(int i=0;i<skillArr.size();i++) {
				JSONObject obj = skillArr.getJSONObject(i);
				if(tempList.contains(obj.getString("ID"))) {
					JSONObject skillData = new JSONObject();
					skillData.put("ID", obj.getString("ID"));
					skillData.put("NAME", obj.getString("NAME"));
					skillList.add(skillData);
					if(!addList.isEmpty() && addList.contains(obj.getString("ID"))) {
						ercd.put("QUEUE_GROUP_ID",RandomKit.randomStr());
						ercd.put("ENT_ID",getEntId());
						ercd.put("BUSI_ORDER_ID",getBusiOrderId());
						ercd.put("QUEUE_ID",queueId);
						ercd.put("SKILL_GROUP_ID",obj.getString("ID"));
						ercd.put("CREATOR",user.getUserAcc());
						ercd.put("CREATE_TIME",EasyCalendar.newInstance().getDateTime("-"));
						query.save(ercd);
					}
				}
			}
			data.put("SKILL_GROUP_LIST",skillList.toJSONString());
			EasyRecord record = new EasyRecord(getTableName("CC_SKILL_QUEUE"),"QUEUE_ID").setColumns(data);
			query.update(record);
			query.commit();
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "修改队列出错:" + e.getMessage(),e);
			try {
				query.roolback();
			} catch (SQLException e1) {
				e1.printStackTrace();
			}
			return EasyResult.error();
		}
		return EasyResult.ok("",getI18nValue("修改成功"));
	}
	
	//删除
	@InfAuthCheck(resId = "cc-base-org-queuegl", msg="无权访问")
	public JSONObject actionForDelQueue() {
		String id = getJsonPara("queueId");
		try {
			//检查是否有渠道使用此队列，如果有则无法修改类型（针对全媒体）
			if(getQuery().queryForInt("SELECT COUNT(1) FROM " + getTableName("CC_SKILL_QUEUE_CHANNEL") + " WHERE QUEUE_ID = ?", id) > 0) {
				return EasyResult.error(500,BaseI18nUtil.getI18nValue(getRequest(), Constants.APP_NAME, "有渠道正在使用此全媒体队列，无法删除该队列"));
			}
			//删除队列
			EasyRecord delRecord = new EasyRecord(getTableName("CC_SKILL_QUEUE"),"QUEUE_ID").setPrimaryValues(id);
			getQuery().deleteById(delRecord);
			//删除队列关联技能组信息
			EasyRecord delSkill = new EasyRecord(getTableName("CC_SKILL_QUEUE_GROUP"),"QUEUE_ID").setPrimaryValues(id);
			getQuery().deleteById(delSkill);
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "删除队列出错:" + e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok("",getI18nValue("修改成功"));
	}
	
	//修改状态
	@InfAuthCheck(resId = "cc-base-org-queuegl", msg="无权访问")
	public JSONObject actionForEditStatus() {
		String id = getJsonPara("id");
		String status = getJsonPara("status");
		EasyRecord record = new EasyRecord(getTableName("CC_SKILL_QUEUE"), "QUEUE_ID").setPrimaryValues(id);
		record.put("ENABLE_STATUS", status);
		try {
			this.getQuery().update(record);
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "修改常见问题状态出错：" + e.getMessage(), e);
			return EasyResult.error();
		}
		return EasyResult.ok("",getI18nValue("修改成功"));
	}
	
	//技能组优先级修改
	@InfAuthCheck(resId = "cc-base-org-queuegl", msg="无权访问")
	public EasyResult actionForUpdateIdxOrder() {
		try {
			JSONObject queueSkillJson = this.getJSONObject("QUEUE_SKILL");
			String skillGroupId = queueSkillJson.getString("SKILL_GROUP_ID");
			if(StringUtils.isNotBlank(skillGroupId)) {
				String[] groupId = skillGroupId.split(",");
				for(int i=0;i<groupId.length;i++) {
					this.getQuery().execute("UPDATE " + this.getTableName("CC_SKILL_QUEUE_GROUP") + " SET PRIORITY = ? WHERE QUEUE_ID = ? AND SKILL_GROUP_ID = ?", queueSkillJson.getString("PRIORITY"), queueSkillJson.getString("QUEUE_ID"),groupId[i]);
				}
			}
			return EasyResult.ok();
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
			return EasyResult.error();
		}
	}
	
	/**
	 * 队列删除技能组
	 */
	@InfAuthCheck(resId = "cc-base-org-queuegl", msg="无权访问")
	public JSONObject actionForDelQueueSkill() {
		String queueId = getJsonPara("QUEUEID");
		String skillGroupId = getJsonPara("skillGroupId");
		if(StringUtils.isNoneBlank(skillGroupId)&&StringUtils.isNoneBlank(queueId)) {
			try {
				if("[".equals(String.valueOf(skillGroupId.charAt(0)))&&"]".equals(String.valueOf(skillGroupId.charAt(skillGroupId.length()-1)))) {
					skillGroupId = skillGroupId.replace("[", "").replace("]", "").replace("\"", "");
				}
				String[] sgIds = skillGroupId.split(",");
				EasyRecord record = new EasyRecord(getTableName("CC_SKILL_QUEUE_GROUP"),"QUEUE_ID","SKILL_GROUP_ID");
				for(int i=0;i<sgIds.length;i++) {
					record.put("QUEUE_ID", queueId);
					record.put("SKILL_GROUP_ID",sgIds[i]);
					getQuery().deleteById(record);
				}
				deletSkillGroupList(queueId, sgIds);
			} catch (Exception e) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
				return EasyResult.error();
			}
		}else {
			return EasyResult.error();
		}
		return EasyResult.ok("",getI18nValue("修改成功"));
	}
	
	@InfAuthCheck(resId = "cc-base-org-queuegl", msg="无权访问")
	public JSONObject actionForSaveSkillGroup() {
		JSONObject data = getJSONObject();
		String queueId = data.getString("queueId");
		JSONArray skillArr = data.getJSONArray("skillGroup");
		JSONArray skillList = new JSONArray();
		UserModel user = UserUtil.getUser(getRequest());
		try {
			if(data!=null&&skillArr!=null&&data.size()>0&&skillArr.size()>0) {
				EasyRecord ercd = new EasyRecord(getTableName("CC_SKILL_QUEUE_GROUP"));
				for(int i=0;i<skillArr.size();i++) {
					JSONObject obj = skillArr.getJSONObject(i);
					JSONObject skillData = new JSONObject();
					skillData.put("ID", obj.getString("SKILL_GROUP_ID"));
					skillData.put("NAME", obj.getString("SKILL_GROUP_NAME"));
					skillList.add(skillData);
					ercd.put("QUEUE_GROUP_ID",RandomKit.randomStr());
					ercd.put("ENT_ID",getEntId());
					ercd.put("BUSI_ORDER_ID",getBusiOrderId());
					ercd.put("QUEUE_ID",queueId);
					ercd.put("SKILL_GROUP_ID",obj.getString("SKILL_GROUP_ID"));
					ercd.put("CREATOR",user.getUserAcc());
					ercd.put("CREATE_TIME",EasyCalendar.newInstance().getDateTime("-"));
					getQuery().save(ercd);
				}
				//修改SKILL_GROUP_LIST
				JSONObject getData = getQuery().queryForRow("SELECT SKILL_GROUP_LIST FROM " + getTableName("CC_SKILL_QUEUE") + " WHERE QUEUE_ID = ?",new Object[] {queueId},new JSONMapperImpl());
				JSONArray list = getData.getJSONArray("SKILL_GROUP_LIST");
				if(list!=null&&list.size()>0) {
					for(int k=0;k<skillList.size();k++) {
						list.add(skillList.getJSONObject(k));
					}
				}else {
					list = skillList;
				}
				EasyRecord record = new EasyRecord(getTableName("CC_SKILL_QUEUE"),"QUEUE_ID").setPrimaryValues(queueId);
				record.put("SKILL_GROUP_LIST", list.toJSONString());
				getQuery().update(record);
			}else {
				return EasyResult.error();
			}
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "关联队列出错:" + e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok("",getI18nValue("修改成功"));
	}
	
	@InfAuthCheck(resId = "cc-base-org-queuegl", msg="无权访问")
	private void deletSkillGroupList(String queueId,String[] ids) {
		try {
			JSONObject data = getQuery().queryForRow("SELECT SKILL_GROUP_LIST FROM " + getTableName("CC_SKILL_QUEUE") + " WHERE QUEUE_ID = ?",new Object[] {queueId},new JSONMapperImpl());
			JSONArray list = data.getJSONArray("SKILL_GROUP_LIST");
			for(int i=0;i<ids.length;i++) {
				for(int j=0;j<list.size();j++) {
					JSONObject obj = list.getJSONObject(j);
					if(ids[i].equals(obj.getString("ID"))) {
						list.remove(obj);
					}
				}
			}
			EasyRecord record = new EasyRecord(getTableName("CC_SKILL_QUEUE"),"QUEUE_ID").setPrimaryValues(queueId);
			record.put("SKILL_GROUP_LIST", list.toJSONString());
			getQuery().update(record);
			
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
			return;
		}
		
	}
	/**
	 * 修改状态
	 */
	@InfAuthCheck(resId = "cc-base-org-queuegl", msg="无权访问")
	public JSONObject actionForModifyPriority(){
		String PRIORITY = getJsonPara("PRIORITY");
		String QUEUE_ID = getJsonPara("QUEUE_ID");
		EasyRecord record = new EasyRecord(getTableName("CC_SKILL_QUEUE"),"QUEUE_ID").setPrimaryValues(QUEUE_ID);
		record.set("PRIORITY", PRIORITY);
		try {
			this.getQuery().update(record);
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			error(e.getMessage(), e);
			return EasyResult.fail();
		}
		return EasyResult.ok("",getI18nValue("修改成功!"));
	}
	/**
	 * 批量修改优先级
	 */
	public JSONObject actionForUpdateProiroy(){
			String jsonPara = getJsonPara("queueList");
			String replace = jsonPara.replace("[","").replace("\"", "").replace("]", "");
			String[] quueids = replace.split(",");
			for(String id:quueids){
				EasyRecord record = new EasyRecord(getTableName("CC_SKILL_QUEUE"),"QUEUE_ID").setPrimaryValues(id);
				record.set("PRIORITY", getJsonPara("PRIORITY"));
				try {
					this.getQuery().update(record);
				} catch (SQLException e) {
					// TODO Auto-generated catch block
					logger.error(e.getMessage());
					return EasyResult.fail();
				}	
			}
			return EasyResult.ok("",getI18nValue("批量修改成功"));
	}
	/**
	 * 批量关联技能组
	 * @throws SQLException 
	 */
	public JSONObject actionForSaveQConnectGroup() {
		String str = getJsonPara("queueIds");
		String jsonPara = getJsonPara("skillList");
		String replace = str.replace("[","").replace("\"","").replace("]", "");
		String[] queueIds = replace.split(",");
		//将被选择的技能组id和name变为一个jsonArray
		JSONArray jsonArray = JSONArray.parseArray(jsonPara);
		EasyQuery query =getQuery();
		try {
			query.begin();
			//遍历选中的队列ID
			for (String i : queueIds) {
				boolean addBoolean=true;
				boolean addBoolean2=true;
				//从该表选择队列list字段，该字段为jsonArray格式
				EasySQL sql = new EasySQL("SELECT SKILL_GROUP_LIST FROM "+getTableName("CC_SKILL_QUEUE"));
				sql.append("WHERE 1 =1");
				sql.append(i," AND QUEUE_ID = ?");	
				//从该表选择技能组id字段
				EasySQL sql2 = new EasySQL("SELECT SKILL_GROUP_ID FROM "+getTableName("CC_SKILL_QUEUE_GROUP"));
				sql2.append(" WHERE 1=1");
				sql2.append(i," AND QUEUE_ID = ?");
				//将得到的SKILL_GROUP_ID变为jsonObject格式的List
				List<JSONObject> queryForList2 = query.queryForList(sql2.getSQL(), sql2.getParams(),new JSONMapperImpl());
				//将得到的SKILL_GROUP_LIST变为JSONObject格式
				JSONObject queryForList = query.queryForRow(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
				//将queryForList这个JSONObject的value单独拿出变为jsonArray3
				JSONArray jsonArray3 = queryForList.getJSONArray("SKILL_GROUP_LIST");
				//新建一个jsonArray2
				JSONArray jsonArray2 = new JSONArray();
				//遍历选中的技能组id，判断是否需要添加
				for (int j =0;j<jsonArray.size();j++) {	
					String ID = jsonArray.getJSONObject(j).getString("SKILL_GROUP_ID");
					String NAME = jsonArray.getJSONObject(j).getString("SKILL_GROUP_NAME");
					//遍历已经关联过的技能组id，当选中的队列id不等于已关联的技能组id就添加到jsonarry3中				
						for(int t=0;t<jsonArray3.size();t++){
							JSONObject skilled = jsonArray3.getJSONObject(t);
							String selectedid = skilled.getString("ID");
									if(selectedid.equals(ID)){
										addBoolean=false;								
									}							
						}
						//遍历该id已关联的技能组，判断选择的技能组是否在内，有则不添加，无则添加
						for(int x=0;x<queryForList2.size();x++){
							String SKILL_GROUP_ID = queryForList2.get(x).getString("SKILL_GROUP_ID");
							if(ID.equals(SKILL_GROUP_ID)){
								addBoolean2=false;
							}
						}
						if(addBoolean){
							JSONObject json =new JSONObject();
							json.put("ID", ID);
							json.put("NAME",NAME);
							jsonArray3.add(json);
						}		
						if(addBoolean2){
							EasyRecord record2 = new EasyRecord(getTableName("CC_SKILL_QUEUE_GROUP"),"QUEUE_GROUP_ID").setPrimaryValues(RandomKit.randomStr());
							record2.set("QUEUE_ID", i);
							record2.set("ENT_ID", getEntId());
							record2.set("BUSI_ORDER_ID", getBusiOrderId());
							record2.set("SKILL_GROUP_ID", ID);
							record2.set("PRIORITY", "99");
							record2.set("CREATOR", UserUtil.getUser(getRequest()).getUserAcc());
							record2.set("CREATE_TIME", EasyCalendar.newInstance().getDateTime("-"));
							query.save(record2);
						}
						
				}
				//更新当前队列的技能组id字段
					EasyRecord record = new EasyRecord(getTableName("CC_SKILL_QUEUE"),"QUEUE_ID").setPrimaryValues(i);
					record.set("SKILL_GROUP_LIST", jsonArray3.toJSONString());
					query.update(record);	
				}
				query.commit();
			} catch (SQLException e) {
			// TODO Auto-generated catch block
			logger.error(e.getMessage());
			try {
				query.roolback();
			} catch (SQLException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
			return EasyResult.fail();
		}	
		return EasyResult.ok("",getI18nValue("批量修改成功"));
	}
	/**
	 * 批量删除技能组
	 * @return
	 */
	public JSONObject actionForDeleteQConnectGroup(){
		String str = getJsonPara("queueIds");
		String jsonPara = getJsonPara("skillList");
		String replace = str.replace("[","").replace("\"","").replace("]", "");
		String[] queueIds = replace.split(",");
		JSONArray jsonArray = JSONArray.parseArray(jsonPara);
		EasyQuery query =getQuery();
		try {
			query.begin();
			for (String i : queueIds) {
	
				EasySQL sql = new EasySQL("SELECT SKILL_GROUP_LIST FROM "+getTableName("CC_SKILL_QUEUE"));
				sql.append("WHERE 1 =1");
				sql.append(i," AND QUEUE_ID = ?");		
				EasySQL sql2 = new EasySQL("SELECT SKILL_GROUP_ID FROM "+getTableName("CC_SKILL_QUEUE_GROUP"));
				sql2.append(" WHERE 1=1");
				sql2.append(i," AND QUEUE_ID = ?");
				List<JSONObject> queryForList2 = query.queryForList(sql2.getSQL(), sql2.getParams(),new JSONMapperImpl());
				JSONObject queryForList = query.queryForRow(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
				JSONArray jsonArray3 = queryForList.getJSONArray("SKILL_GROUP_LIST");
				JSONArray jsonArray2 = new JSONArray();
				//遍历选中的技能组id，判断是否需要删除
				for (int j =0;j<jsonArray.size();j++) {	
					String ID = jsonArray.getJSONObject(j).getString("SKILL_GROUP_ID");
					String NAME = jsonArray.getJSONObject(j).getString("SKILL_GROUP_NAME");
					//遍历已经关联过的技能组id，当选中的队列id不等于已关联的技能组id就添加到jsonarry3中				
						for(int t=0;t<jsonArray3.size();t++){
							JSONObject skilled = jsonArray3.getJSONObject(t);
							String selectedid = skilled.getString("ID");
									if(selectedid.equals(ID)){
										JSONObject json =new JSONObject();
										json.put("ID", ID);
										json.put("NAME",NAME);
										jsonArray3.remove(t);
									}		
									//遍历该id已关联的技能组，判断选择的技能组是否在内，有则不添加，无则添加
									for(int x=0;x<queryForList2.size();x++){
										String SKILL_GROUP_ID = queryForList2.get(x).getString("SKILL_GROUP_ID");
										if(ID.equals(SKILL_GROUP_ID)){
											EasyRecord record2 = new EasyRecord(getTableName("CC_SKILL_QUEUE_GROUP"),"SKILL_GROUP_ID").setPrimaryValues(SKILL_GROUP_ID);
											query.deleteById(record2);
										}
									}
		
						}						
				}
				//更新当前队列的技能组id字段
					EasyRecord record = new EasyRecord(getTableName("CC_SKILL_QUEUE"),"QUEUE_ID").setPrimaryValues(i);
					record.set("SKILL_GROUP_LIST", jsonArray3.toJSONString());
					query.update(record);
				}
				query.commit();
			} catch (SQLException e) {
			// TODO Auto-generated catch block
			logger.error(e.getMessage());
			try {
				query.roolback();
			} catch (SQLException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
			return EasyResult.fail();
		}	
		return EasyResult.ok("",getI18nValue("批量修删除成功"));
	}
//	//关联渠道
//	public JSONObject actionForConnectChannel() {
//		JSONObject obj = getJSONObject();
//		String queId = obj.getString("id");
//		//选定的渠道ID、渠道KEY
//		JSONArray chIds = obj.getJSONObject("channelData").getJSONArray("channelId");
//		JSONArray chKeys = obj.getJSONObject("channelData").getJSONArray("channelKey");
//		if(StringUtils.isNotBlank(queId)&&chIds!=null&&chIds.size()>0) {
//			try {
//				
//				saveChannelQueue(chKeys, chIds, queId);
//				
//			} catch (Exception e) {
//				// TODO Auto-generated catch block
//				logger.error(CommonUtil.getClassNameAndMethod(this) + "出错：" + e.getMessage(),e);
//				return EasyResult.error();
//			}
//		}
//		return EasyResult.ok();
//	}
//	
//	//关联渠道保存
//	private void saveChannelQueue(JSONArray keys,JSONArray ids,String queueId) {
//		try {
//			//查询渠道关联表
//			EasySQL sql = new EasySQL("SELECT CHANNEL_KEY,CHANNEL_ID,QUEUE_ID FROM " + getTableName("CC_SKILL_QUEUE_CHANNEL") + " WHERE 1=1");
//			sql.append(queueId,"AND QUEUE_ID = ?");
//			sql.append(getEntId(),"AND ENT_ID = ?");
//			sql.append(getBusiOrderId(),"AND BUSI_ORDER_ID = ?");
//			List<JSONObject> qacInfo = this.getQuery().queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
//			EasyRecord record = new EasyRecord(getTableName("CC_SKILL_QUEUE_CHANNEL"));
//			
//			if(qacInfo!=null&&qacInfo.size()>0) {
//				for(int i=0;i<ids.size();i++) {
//					boolean flag = true;
//					for(JSONObject obj:qacInfo) {
//						if(keys.getString(i).equals(obj.getString("CHANNEL_KEY"))&&queueId.equals(obj.getString("QUEUE_ID"))){
//							flag = false;
//						}
//					}
//					if(flag) {
//						record.put("QUEUE_CHANNEL_ID", RandomKit.uniqueStr());
//						record.put("QUEUE_ID",queueId);
//						record.put("CHANNEL_KEY",keys.get(i));
//						record.put("CHANNEL_ID",ids.get(i));
//						record.put("ENT_ID",getEntId());
//						record.put("BUSI_ORDER_ID",getBusiOrderId());
//						record.put("CREATE_ACC",UserUtil.getRequestUserAcc(getRequest()));
//						record.put("CREATE_TIME",EasyCalendar.newInstance().getDateTime("-"));
//						this.getQuery().save(record);
//					}
//				}
//			}else {
//				for(int i=0;i<ids.size();i++) {
//					record.put("QUEUE_CHANNEL_ID", RandomKit.uniqueStr());
//					record.put("QUEUE_ID",queueId);
//					record.put("CHANNEL_KEY",keys.get(i));
//					record.put("CHANNEL_ID",ids.get(i));
//					record.put("ENT_ID",getEntId());
//					record.put("BUSI_ORDER_ID",getBusiOrderId());
//					record.put("CREATE_ACC",UserUtil.getRequestUserAcc(getRequest()));
//					record.put("CREATE_TIME",EasyCalendar.newInstance().getDateTime("-"));
//					this.getQuery().save(record);
//				}
//			}
//		} catch (SQLException e) {
//			logger.error(CommonUtil.getClassNameAndMethod(this) + "出错：" + e.getMessage(),e);
//		}
//	}
}
