package com.yunqu.cc.base.servlet.system;

import java.sql.SQLException;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.annontation.InfAuthCheck;
import com.yq.busi.common.annontation.InfAuthCheck.ResIdCheckType;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.base.base.AppBaseServlet;
import com.yunqu.cc.base.base.CommonLogger;
import com.yunqu.cc.base.base.Constants;

@WebServlet("/servlet/sidebarMenu/*")
public class SidebarMenuServlet extends AppBaseServlet {

	private static final long serialVersionUID = 1L;
	
	private Logger logger = CommonLogger.logger;
	
	/**
	 * 侧边栏目录增加
	 * @return
	 */
	@InfAuthCheck(resId={"cc-base-system-xtpz-cspz-qycspz, cc-base-system-xtpz-cbl"}, checkType=ResIdCheckType.OR, msg="没有权限")
	public JSONObject actionForMenuAdd(){
		JSONObject obj=this.getJSONObject("menu");
		JSONObject add=new JSONObject();
		String date=DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT);
		String menuId=obj.getString("MENU_ID");
		String busiType=obj.getString("BUSI_TYPE");
		String entId=getEntId();
		//判断menuId是否唯一
		if(isExit(menuId, entId, null,busiType)){
			return EasyResult.error(500,"该目录已经存在,可能是工单扩展或者侧边栏中的一个");
		}
		add.put("ID",RandomKit.randomStr());
		add.put("MENU_ID",menuId);
		add.put("MENU_NAME",obj.getString("MENU_NAME"));
		add.put("MENU_URL",obj.getString("MENU_URL"));
		add.put("STATUS",obj.getString("STATUS"));
		add.put("SORT_NUM",obj.getString("SORT_NUM"));
		add.put("BAKUP",obj.getString("BAKUP"));
		add.put("TYPE",obj.getString("TYPE"));
		add.put("BUSI_ID",obj.getString("BUSI_ID"));
		add.put("BUSI_TYPE",obj.getString("BUSI_TYPE"));
		add.put("CREATE_TIME",date);
		UserModel longUser=UserUtil.getUser(this.getRequest());//获取登陆用户
		add.put("CREATE_ACC",longUser.getUserAcc());
		add.put("CREATE_NAME",longUser.getUserName());
		add.put("CREATE_DEPT",longUser.getDeptCode());
		add.put("CREATE_DEPT_NAME",longUser.getDeptName());
		add.put("BULIT_IN","N");
		add.put("ENT_ID", entId);
		add.put("BUSI_ORDER_ID", longUser.getBusiOrderId());
		try {
			EasyRecord record = new EasyRecord(getTableName("C_CF_CALLIN_OP_MENU"), "ID").setColumns(add);
			this.getQuery().save(record);
			return EasyResult.ok(record, "添加成功");
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)
					+ "添加侧边栏数据出错，原因：" + e.getMessage(), e);
			return EasyResult.error(500,e.getMessage());
		}
	}
	/**
	 * 判断数据是否存在
	 * @param menuId 菜单标识
	 * @param entId 企业ID
	 * @param id 自增id（用于修改的时候判断自增id不同但是menuId相同的数据）
	 * @return
	 */
	public boolean isExit(String menuId,String entId,String id,String busiType){
		boolean flag=true;
		try {
			if(StringUtils.isNoneBlank(id)){
				flag = getQuery().queryForExist("select count(1) from "+getTableName("C_CF_CALLIN_OP_MENU")
				+" WHERE MENU_ID=? AND ENT_ID=? AND ID<>?",new Object[]{menuId,entId,id});
			}else{
				flag = getQuery().queryForExist("select count(1) from "+getTableName("C_CF_CALLIN_OP_MENU")
				+" WHERE MENU_ID=? AND ENT_ID=? and BUSI_TYPE=?",new Object[]{menuId,entId,busiType});
			}
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)
					+ "判断侧边栏数据是否存在出错，原因：" + e.getMessage(), e);
		}
		return flag;
	}
	
	/**
	 * 侧边栏目录修改
	 * @return
	 */
	@InfAuthCheck(resId={"cc-base-system-xtpz-cspz-qycspz, cc-base-system-xtpz-cbl"}, checkType=ResIdCheckType.OR, msg="没有权限")
	public JSONObject actionForMenuEdit(){
		JSONObject obj=this.getJSONObject("menu");
		JSONObject add=new JSONObject();
		String id=obj.getString("id");
		String menuId=obj.getString("MENU_ID");
		String busiType=obj.getString("BUSI_TYPE");
		String entId=getEntId();
		//判断menuId是否唯一
		if(isExit(menuId, entId, id,busiType)){
			return EasyResult.error(500,"该目录已经存在");
		}
		add.put("ID",id);
		add.put("MENU_NAME",obj.getString("MENU_NAME"));
		add.put("MENU_URL",obj.getString("MENU_URL"));
		add.put("STATUS",obj.getString("STATUS"));
		add.put("BAKUP",obj.getString("BAKUP"));
		add.put("SORT_NUM",obj.getString("SORT_NUM"));
		try {
			EasyRecord record = new EasyRecord(getTableName("C_CF_CALLIN_OP_MENU"), "ID").setColumns(add);
			this.getQuery().update(record);
			return EasyResult.ok(record, "修改成功");
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)
					+ "修改侧边栏数据出错，原因：" + e.getMessage(), e);
			return EasyResult.error(500,e.getMessage());
		}
	}
	
	/**
	 * 删除侧边栏
	 * @return
	 */
	@InfAuthCheck(resId={"cc-base-system-xtpz-cspz-qycspz, cc-base-system-xtpz-cbl"}, checkType=ResIdCheckType.OR, msg="没有权限")
	public JSONObject actionForMenuDel(){
		try {
			String id = getJsonPara("id");
			EasyRecord record = new EasyRecord(getTableName("C_CF_CALLIN_OP_MENU"),"ID").setPrimaryValues(id);
			this.getQuery().deleteById(record);
			return EasyResult.ok(record,"删除成功");
		} catch(Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)
					+ "删除侧边栏数据出错，原因：" + e.getMessage(), e);
			return EasyResult.fail();
		}
	}
	
	/**
	 * 修改状态
	 * @return
	 */
	@InfAuthCheck(resId={"cc-base-system-xtpz-cspz-qycspz, cc-base-system-xtpz-cbl"}, checkType=ResIdCheckType.OR, msg="没有权限")
	public JSONObject actionForMenuStatusEdit(){
		try {
			String id = getJsonPara("id");
			EasyRecord record = new EasyRecord(getTableName("C_CF_CALLIN_OP_MENU"),"ID").setPrimaryValues(id);
			JSONObject add=new JSONObject();
			add.put("STATUS", this.getJsonPara("status"));
			record.setColumns(add);
			this.getQuery().update(record);
		} catch(Exception e) {
			error(e.getMessage(), e);
			return EasyResult.fail();
		}
		return EasyResult.ok("",getI18nValue("操作成功"));
	}
	/**
	 * 同步系统配置侧边栏数据
	 * @return
	 */
	@InfAuthCheck(resId={"cc-base-system-xtpz-cspz-qycspz, cc-base-system-xtpz-cbl"}, checkType=ResIdCheckType.OR, msg="没有权限")
	public JSONObject actionForSynCblData(){
		JSONObject obj=this.getJSONObject();
		EasySQL sql= new EasySQL("select DISTINCT t1.* from "+getTableName("C_MEDIA_OP_MENU")+" t1 LEFT JOIN   "+getTableName("C_MEDIA_OP_MENU_BUSI")+" t2");
		sql.append(" on t1.ID=t2.OP_MENU_ID where 1=1");
		sql.append(" AND t1.ENT_ID = ? ");
		sql.append(" AND t1.BUSI_ORDER_ID = ? ");
		sql.append( " AND t2.BUSI_ID=? ");
		sql.append("ORDER BY t1.SORT_NUM ASC, t1.CREATE_TIME DESC");
		try {
			List<EasyRow> list=getQuery().queryForList(sql.getSQL(), new Object[] {getEntId(),getBusiOrderId(),Constants.BUSI_ID_TP});
			if(list!=null&&list.size()>0){
				for (EasyRow easyRow : list) {
					JSONObject add=new JSONObject();
					String date=DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT);
					String menuId=easyRow.getColumnValue("MENU_ID");
					String entId=getEntId();
					//判断menuId是否唯一
					if(isExit(menuId, entId, null,obj.getString("BUSI_TYPE"))){
						continue;
					}
					add.put("ID",RandomKit.randomStr());
					add.put("MENU_ID",menuId);
					add.put("MENU_NAME",easyRow.getColumnValue("MENU_NAME"));
					add.put("MENU_URL",easyRow.getColumnValue("MENU_URL"));
					add.put("STATUS",easyRow.getColumnValue("STATUS"));
					add.put("TYPE",easyRow.getColumnValue("TYPE"));
					add.put("SORT_NUM",easyRow.getColumnValue("SORT_NUM"));
					add.put("TYPE",obj.getString("TYPE"));
					add.put("BUSI_ID",obj.getString("BUSI_ID"));
					add.put("BUSI_TYPE",obj.getString("BUSI_TYPE"));
					add.put("CREATE_TIME",date);
					UserModel longUser=UserUtil.getUser(this.getRequest());//获取登陆用户
					add.put("CREATE_ACC",longUser.getUserAcc());
					add.put("CREATE_NAME",longUser.getUserName());
					add.put("CREATE_DEPT",longUser.getDeptCode());
					add.put("CREATE_DEPT_NAME",longUser.getDeptName());
					add.put("BULIT_IN","N");
					add.put("ENT_ID", entId);
					add.put("BUSI_ORDER_ID", longUser.getBusiOrderId());
					EasyRecord record = new EasyRecord(getTableName("C_CF_CALLIN_OP_MENU"), "ID").setColumns(add);
					this.getQuery().save(record);
				}				
			}
			return EasyResult.ok("同步成功");	
		} catch (SQLException e) {
			error(e.getMessage(), e);
			return EasyResult.fail();
		}
	}
}
