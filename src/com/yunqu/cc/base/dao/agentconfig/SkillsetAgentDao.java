package com.yunqu.cc.base.dao.agentconfig;

import java.util.ArrayList;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.model.TreeNode;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.user.DeptMgr;
import com.yq.busi.common.util.JsonUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.base.base.AppDaoContext;
import org.easitline.common.utils.string.StringUtils;

@WebObject(name = "skillsetAgent")
public class SkillsetAgentDao extends AppDaoContext {
	@WebControl(name = "getDeptZtree", type = Types.TREE)
	public JSONObject getDeptZtree() {
		UserModel user = UserUtil.getUser(this.request);
		String depCode = user.getLevelOneDeptCode();
		TreeNode usersByDeptCode = DeptMgr.getDeptTreeNode(new ArrayList<TreeNode>(), depCode, user.getSchemaName(), false, false, true, getEntId());
		JSONObject jsonObject = JsonUtil.toJSONObject(usersByDeptCode);
		return getTree(jsonObject);
	}

	@WebControl(name = "getAgent", type = Types.LIST)
	public JSONObject getAgent() {
		//String id=	param.getString("deptCode");//部门编号
		String depCode = UserUtil.getUser(this.request).getEpCode();
		EasySQL sql = new EasySQL();
		sql = this.getEasySQL("select ccdr.RS_ID,ccdr.RS_NAME,ccdr.EX1 ,cyem.* from " + getTableName("C_CF_DICT_RESOURCE") + " ccdr ");
		sql.append("left join " + getTableName("C_CF_DICTGROUP") + " ccdg on ccdr.DICT_GROUP_ID = ccdg.id ");
		sql.append("left join " + getTableName("C_CF_DICT") + " ccd on ccd.dict_group_id = ccdg.id  and ccdr.DICT_CODE=ccd.code ");
		//当前企业的坐席类型
		sql.append(depCode, " and ccdg.ep_code = ?  and ccdg.code='AGENT_TYPE' ");
		sql.append(this.getBusiOrderId(), " and ccdg.BUSI_ORDER_ID=? ");
		sql.append("left join "+getTableName("C_YG_EMPLOYEE_MEDIA")+" cyem on cyem.agent_acc = ccdr.RS_ID");
		//坐席类型
		sql.append("where RS_TYPE = 01 ");//坐席配置
		if (!"-1".equals(param.getString("deptCode"))) {//组织架构
			sql.appendRLike(param.getString("deptCode"), " and ex1 like ? ");//坐席部门
		} else {
			sql.appendRLike(depCode, " and ex1 like ? ");//坐席企业
		}
		sql.append(param.getString("agentInfo"), " and ( ccdr.RS_ID=?  OR ccdr.RS_NAME=? )");
		String agentType;//默认选中语音
		if (StringUtils.isBlank(param.getString("agentType"))) {
			agentType = "01";
		} else {
			agentType = param.getString("agentType");
		}
		sql.append(agentType, " and  ccd.code=? ");
		sql.append(depCode, " and ccdg.ep_code = ?  and ccdg.code='AGENT_TYPE' ");//当前企业的坐席类型
		//System.out.println(sql.getSQL());
		return this.queryForPageList(sql.getSQL(), sql.getParams());
	}

	@WebControl(name = "getAgentConfig", type = Types.RECORD)
	public JSONObject getAgentConfig() {
		return this.queryForRecord(new EasyRecord(getTableName("C_YG_EMPLOYEE_MEDIA"), "AGENT_ACC").setPrimaryValues(param.getString("agent.ACC")));
	}

	@WebControl(name = "loginStatus", type = Types.RECORD)
	public JSONObject loginStatus() {
		String depCode = UserUtil.getUser(this.request).getEpCode();
		return DictCache.getJsonEnableDictListByGroupCode(depCode, "MEDIA_AGENT_LOGIN_STATUS");
	}

	/**
	 * 选择人员
	 *
	 * @return
	 */
	@WebControl(name = "getUser", type = Types.LIST)
	public JSONObject getUser() {
		String skillId = param.getString("skillId");//技能组编号
		String depCode = UserUtil.getUser(this.request).getEpCode();
		EasySQL sql = new EasySQL();
		sql = this.getEasySQL("select ccdr.RS_ID,ccdr.RS_NAME,ccdr.EX1 from "+getTableName("C_CF_DICT_RESOURCE")+" ccdr ");
		sql.append("left join "+getTableName("C_CF_DICTGROUP")+" ccdg on ccdr.DICT_GROUP_ID = ccdg.id ");
		sql.append("left join "+getTableName("C_CF_DICT")+" ccd on ccd.dict_group_id = ccdg.id  and ccdr.DICT_CODE=ccd.code ");
		//当前企业的坐席类型
		sql.append(depCode, "and ccdg.ep_code = ?  and ccdg.code='AGENT_TYPE' ");
		sql.append(this.getBusiOrderId(), " and ccdg.BUSI_ORDER_ID=? ");
		//坐席类型
		//坐席配置
		sql.append("where RS_TYPE = 01 ");
		//组织架构
		if (!"-1".equals(param.getString("dept_id"))) {
			//坐席部门
			sql.appendRLike(param.getString("dept_id"), " and ex1 like ? ");
		} else {
			sql.appendRLike(depCode, " and ex1 like ? ");//坐席企业
		}
		sql.append(" AND RS_ID  not  in(SELECT  USER_ACC  FROM C_CF_AGENT_SKILL WHERE SKILL_ID= '" + skillId + "' )");
		sql.appendRLike(param.getString("userInfo"), " and ( ccdr.RS_ID like ?  OR ccdr.RS_NAME like ? )");
		sql.append(param.getString("agentType"), " and  ccd.code=? ", false);
		//当前企业的坐席类型
		sql.append(depCode, " and ccdg.ep_code = ?  and ccdg.code='AGENT_TYPE' ");
		return this.queryForList(sql.getSQL(), sql.getParams());
	}

	@WebControl(name = "getSkillAgentList", type = Types.LIST)
	public JSONObject getSkillAgentList() {
		//String depCode=UserUtil.getUser(this.request).getEpCode();
		EasySQL sql = this.getEasySQL("select * from "+getTableName("C_CF_AGENT_SKILL")+" where 1=1");
		sql.append(param.getString("SKILL_ID"), " and SKILL_ID =? ", false);
		sql.append(" order by  SORT_NUM,USER_ACC ");
		return this.queryForList(sql.getSQL(), sql.getParams());
	}

	@WebControl(name = "getAgentSkillList", type = Types.LIST)
	public JSONObject getAgentSkillList() {
		//String depCode=UserUtil.getUser(this.request).getEpCode();
		EasySQL sql = this.getEasySQL("select  ccs.*,ccas.skill_id,ccas.SORT_NUM from "+getTableName("C_CF_SKILLS")+"  ccs  ");
		sql.append("  left  join "+getTableName("C_CF_AGENT_SKILL")+" ccas on ccs.id=ccas.skill_id ");
		sql.append(param.getString("userAcc"), " and  ccas.user_acc=?  ", false);
		sql.append(" where 1=1");
		sql.append(param.getString("agentType"), " and  ccs.SESSION_TYPE =? ", false);
		sql.append(" order by  ccs.SORT_NUM, ccs.ID ");
		return this.queryForList(sql.getSQL(), sql.getParams());
	}

}
