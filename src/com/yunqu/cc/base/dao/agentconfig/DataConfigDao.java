package com.yunqu.cc.base.dao.agentconfig;

import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.base.base.AppDaoContext;
import com.yunqu.cc.base.base.CommonLogger;
import com.yunqu.cc.base.utils.StringUtil;

@WebObject(name="dataConfig")
public class DataConfigDao extends AppDaoContext {
	private Logger logger = CommonLogger.logger;

	@WebControl(name="dataList", type = Types.LIST)
	public JSONObject dataList(){
		UserModel user = UserUtil.getUser(request);
	    EasySQL sql=getEasySQL("  SELECT T2.ID, T2.SKILL_ID,T2.SKILL_NAME,T2.SKILL_CODE,T2.QUEUE_CODE,T2.QUEUE_NAME,T2.BUSI_CODE,T2.BUSI_NAME");
	    sql.append("   ,T2.AREA_CODE,T2.AREA_NAME ");
	    sql.append("   FROM "+user.getSchemaName()+".C_QUEUE T2  LEFT JOIN "+user.getSchemaName()+".CC_SKILL_GROUP T1 ON T1.SKILL_GROUP_ID = T2.SKILL_ID where 1=1");
	    sql.appendLike(this.param.getString("SKILL_CODE")," AND T2.SKILL_CODE LIKE? ");
	    sql.append(this.param.getString("AREA_CODE")," AND T2.AREA_CODE=?");
	    sql.append(this.param.getString("DIVISION_CODE")," AND T2.DIVISION_CODE=?");
	    sql.append(this.param.getString("LINE_TYPE_CODE")," AND T2.LINE_TYPE_CODE=?");
	    sql.append(this.param.getString("BUSI_CODE")," AND T2.BUSI_CODE=?");
	    JSONObject json=this.queryForPageList(sql.getSQL(),sql.getParams());
	    return json;
	}
	@WebControl(name="dataRecord", type = Types.RECORD)
	public JSONObject dataRecord(){
		UserModel user = UserUtil.getUser(request);
		String id=this.param.getString("da.ID");
		EasySQL sql=getEasySQL(" SELECT * from "+user.getSchemaName()+".C_QUEUE ");
		  sql.append(" where ID='"+id+"'");
		  logger.debug(CommonUtil.getClassNameAndMethod(this)+"sql="+sql.getSQL());
		  JSONObject obj=this.queryForRecord(sql.getSQL(), sql.getParams(),null);
		  StringUtil.addPrefix(obj, "da");
		  return  obj;
	}
	/**
	 *获取YCBUSI数据
	 * @return
	 */
	@WebControl(name="group",type=Types.DICT)
	public JSONObject group(){
		UserModel user=UserUtil.getUser(request);
		JSONObject obj =getDictByQuery("select SKILL_GROUP_ID,SKILL_GROUP_NAME from "+user.getSchemaName()+".CC_SKILL_GROUP where ENT_ID='"+user.getEpCode()+"'",new Object[]{});
		return obj;
	}
}
