package com.yunqu.cc.base.dao.entmgr;

import java.sql.SQLException;
import java.util.LinkedHashMap;
import java.util.List;

import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.annontation.InfAuthCheck;
import com.yq.busi.common.base.CEConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.base.base.AppDaoContext;
import com.yunqu.cc.base.base.CommonLogger;
import com.yunqu.cc.base.base.Constants;
import com.yunqu.cc.base.utils.StringUtil;
import com.yunqu.yc.sso.impl.YCUserPrincipal;

@WebObject(name="skillGroup")
public class SkillGroupDao extends AppDaoContext {

	private static Logger logger = CommonLogger.getLogger();
	
	/*@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		EasyRecord skillGroup = new EasyRecord(getTableName("CC_SKILL_GROUP"), "SKILL_GROUP_ID");
		skillGroup.setPrimaryValues(param.getString("pk"));
		return queryForRecord(skillGroup);
	}*/
	
	@WebControl(name="groupTypeDict",type=Types.DICT)
	public JSONObject groupTypeDict(){
		JSONObject result = new JSONObject();
		String level = (String)this.getMethodParam(0);
		String property = CEConstants.getCcBaseContext().getProperty(Constants.SKILL_GROUP_OPEN_TYPE, "");
		LinkedHashMap<String, String> dict = new LinkedHashMap<String, String>();
		if(property.equals(Constants.MULTI_LEVEL_PRO)) {
			dict.put("leader", getI18nValue("总监"));
			dict.put("team", getI18nValue("团队"));
			dict.put("media", getI18nValue("全媒体"));
			dict.put("voice", getI18nValue("语音"));
		}else if("0".equals(level)) {// 第一层
			dict.put("leader", getI18nValue("总监"));
		}else if("1".equals(level)) {// 第二层
			dict.put("team", getI18nValue("团队"));
		}else if("2".equals(level)) {// 第三层
			dict.put("media", getI18nValue("全媒体"));
			dict.put("voice", getI18nValue("语音"));
		}
			
		result.put("data", dict);
		return result;
	}
	
	@WebControl(name = "workGroupList", type = Types.LIST)
	public JSONObject workGroupList() {
		EasySQL sql = new EasySQL("SELECT ID,NAME AS NAME,TYPE,");
		sql.append("(SELECT COUNT(1) FROM " + this.getTableName("C_CF_WORKGROUP_USER") + " T2 WHERE T2.WORKGROUP_ID = T1.ID) USER_COUNT");
		sql.append("FROM " + getTableName("C_CF_WORKGROUP") + " T1 where 1=1");
		sql.append(this.getEntId()," AND ENT_ID=?",false);
		sql.append(this.getBusiOrderId()," AND BUSI_ORDER_ID=?",false);
		sql.append("4", "AND TYPE = ?");
		sql.appendLike(this.param.getString("workGroupName"), "AND NAME LIKE ?");
		sql.append(this.param.getString("groupId"), "AND ID NOT IN (SELECT WORKGROUP_ID FROM " + this.getTableName("C_CF_WORKGROUP_SKILL") + " WHERE SKILLGROUP_ID = ?)");
		sql.append("ORDER BY TYPE,NAME");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	/**
	 * 技能组
	 * @return
	 */
	@WebControl(name = "getUsersByDeptCode", type = Types.OTHER)
	public JSONObject getUsersByDeptCode() {
		this.param.put("pageSize", "2000");
		EasySQL sql=this.getEasySQL("select SKILL_GROUP_ID ID,SKILL_GROUP_NAME NAME,P_GROUP_ID PARENT_ID from ");
		sql.append(getTableName("CC_SKILL_GROUP"));
		sql.append(" where 1=1 ");
		sql.appendRLike(this.param.getString("groupType")," and GROUP_TYPE like ?");
		sql.append(this.getEntId(), "and ENT_ID = ? ");
		sql.append(this.getBusiOrderId(), "and BUSI_ORDER_ID = ? ");
		sql.append("order by IDX_ORDER");
		JSONObject obj=this.queryForPageList(sql.getSQL(), sql.getParams());
		this.param.put("pageSize", "10");
		return obj;
	}
	
	//溢出技能组
	@WebControl(name="spillGroup",type=Types.DICT)
	public JSONObject spillGroup(){
		String skillGroupId = (String)getMethodParam(0);
		return getDictByQuery("select SKILL_GROUP_ID,SKILL_GROUP_NAME from "+getTableName("CC_SKILL_GROUP")+" where SKILL_GROUP_TYPE=? and SKILL_GROUP_ID !=? ", "voice",skillGroupId);
	}
	
	@WebControl(name="list",type=Types.LIST)
	public JSONObject list(){
		String userId = param.getString("userId");
		String taskId = param.getString("taskId");
		if(StringUtils.isNotBlank(userId)){
			EasySQL sql=this.getEasySQL("select t1.*,t2.USER_ID as CHECKED from ").append(getTableName("cc_skill_group t1"));
			sql.append(" left join ").append(getTableName("CC_SKILL_GROUP_USER t2")).append(" on t1.SKILL_GROUP_ID = t2.SKILL_GROUP_ID");
			sql.append(" and t1.ENT_ID = t2.ENT_ID and t1.BUSI_ORDER_ID = t2.BUSI_ORDER_ID");
			sql.append(userId," and t2.USER_ID = ?");
			sql.append(" where 1=1 ");
			sql.append(getEntId()," and t1.ENT_ID = ?");
			sql.append(getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
			return queryForList(sql.getSQL(), sql.getParams(), null);
		}else if(StringUtils.isNotBlank(taskId)){
			EasySQL sql=this.getEasySQL("select t1.*,t2.TASK_ID as CHECKED from ").append(getTableName("cc_skill_group t1"));
			sql.append(" left join ").append(getTableName("CC_TASK_GROUP t2")).append(" on t1.SKILL_GROUP_ID = t2.SKILL_GROUP_ID");
			sql.append(taskId, " and t2.TASK_ID = ?");
			sql.append(" where 1=1 ");
			sql.append(getEntId()," and t1.ENT_ID = ?");
			sql.append(getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
			sql.appendLike(this.param.getString("groupName")," and t1.SKILL_GROUP_NAME LIKE ?");
			sql.append(this.param.getString("queue")," and t1.QUEUE_STRATEGY = ?");
			sql.appendSort(param.getString("sortName"), param.getString("sortType"),"t1.CREATE_TIME desc");
			return queryForList(sql.getSQL(), sql.getParams(), null);
		}else{
			EasySQL sql=this.getEasySQL("select t1.* from ").append(getTableName("cc_skill_group t1"));
			sql.append(" where 1=1 ");
			sql.append(getEntId()," and t1.ENT_ID = ?");
			sql.append(getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
			sql.appendLike(this.param.getString("groupName")," and t1.SKILL_GROUP_NAME LIKE ?");
			sql.append(this.param.getString("queue")," and t1.QUEUE_STRATEGY = ?");
			sql.appendSort(param.getString("sortName"), param.getString("sortType"),"t1.CREATE_TIME desc");
			return queryForList(sql.getSQL(), sql.getParams(), null);
		}
	}
	
	@WebControl(name="skillGroupList",type=Types.LIST)
	public JSONObject skillGroupList(){
		EasySQL sql=this.getEasySQL("");
//		this.param.getString("groupType")
		if("003".equals(this.getUserPrincipal().getBusiId())){
			sql.append("select t1.*,t2.KEY_NAME from ").append(getTableName("cc_skill_group t1"));
			sql.append(" left join CC_CHANNEL_KEY t2 on t1.ENT_ID = t2.ENT_ID and t1.KEY_ID = t2.KEY_ID");
		}else{
			sql.append("select t1.* from ").append(getTableName("cc_skill_group t1"));
		}
		sql.append(" where 1=1 ");
		sql.append(getEntId()," and t1.ENT_ID = ?");
		sql.append(getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
		sql.appendLike(this.param.getString("groupName")," and t1.SKILL_GROUP_NAME LIKE ?");
		sql.append(this.param.getString("queue")," and t1.QUEUE_STRATEGY = ?");
		sql.appendSort(param.getString("sortName"), param.getString("sortType"),"t1.IDX_ORDER,t1.CREATE_TIME desc");
		return queryForPageList(sql.getSQL(), sql.getParams(), null);
	}
	/*@WebControl(name="agentListSelect",type=Types.TEMPLATE)
	public  JSONObject agentListSelect(){
		EasySQL sql=this.getEasySQL("select t1.USER_ID,t1.AGENT_PHONE,t2.AGENT_NAME,t1.USER_STATE from CC_USER t1,");
		sql.append(getTableName("cc_busi_user t2"));
		sql.append(" where t1.USER_STATE <> 9 and t1.USER_ID = t2.USER_ID and t2.USER_STATE = 0 and t1.ADMIN_FLAG = 0 ");
		sql.append(this.getEntId()," and t2.ENT_ID = ? ");
		sql.append(this.getBusiOrderId()," and t2.BUSI_ORDER_ID = ? ");
		sql.appendLike(param.getString("agentPhone")," and t1.AGENT_PHONE like ? ");
		sql.append(" and t1.USER_ID not in (select USER_ID from ");
		sql.append(getTableName("CC_SKILL_GROUP_USER"));
		sql.append(" where 1=1");
		sql.append(this.getEntId(), " and ENT_ID = ?");
		sql.append(this.getBusiOrderId(), " and BUSI_ORDER_ID = ?");
		
		if(this.getBusiId().indexOf("02")<0){					//如果不是云电销
			sql.append(param.getString("pk"), "and SKILL_GROUP_ID = ?");
		}
		sql.append(") order by t1.AGENT_PHONE");
		return queryForList(sql.getSQL(),sql.getParams());
	}*/
	
	
	/**
	 * 坐席选择器回显
	 */
	@WebControl(name="killAgentSelect",type=Types.TEMPLATE)
	public  JSONObject killAgentSelect(){
		EasySQL sql=this.getEasySQL("select t1.USER_ID,t1.AGENT_PHONE,t2.AGENT_NAME,t1.USER_STATE from CC_USER t1,");
		sql.append(getTableName("cc_busi_user t2"));
		sql.append(" where t1.USER_STATE <> 9 and t1.USER_ID = t2.USER_ID and t2.USER_STATE = 0 and t1.ADMIN_FLAG = 0 ");
		sql.append(this.getEntId()," and t2.ENT_ID = ? ");
		sql.append(this.getBusiOrderId()," and t2.BUSI_ORDER_ID = ? ");
		sql.appendLike(param.getString("agentPhone")," and t1.AGENT_PHONE like ? ");
		sql.append(" and t1.USER_ID in (select USER_ID from ");
		sql.append(getTableName("CC_SKILL_GROUP_USER"));
		sql.append(" where 1=1");
		sql.append(this.getEntId(), " and ENT_ID = ?");
		sql.append(this.getBusiOrderId(), " and BUSI_ORDER_ID = ?");
		sql.append(param.getString("pk"), "and SKILL_GROUP_ID = ?");
		sql.append(") order by t1.AGENT_PHONE");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name="getSelectedUser",type=Types.DICT)
	public  JSONObject getSelectedUser(){
		return getDictByQuery("select t1.USER_ID,t2.USERNAME from "+getTableName("CC_SKILL_GROUP_USER")+" t1 INNER JOIN cc_user t2  on t1.USER_ID=t2.USER_ID where SKILL_GROUP_ID= ? ", new Object[]{param.getString("pk")});
	}
	
	@WebControl(name="groupUserList",type=Types.LIST)
	public JSONObject groupUserList(){
		EasySQL sql = this.getEasySQL("select T4.NAME WORK_GROUP_NAME,T4.ID WORK_GROUP_ID,t3.SKILL_GROUP_ID,t1.USER_ID,t1.ENT_ID,t1.AGENT_NAME,t1.BUSI_ORDER_ID,t1.ROLE_ID,t1.USER_STATE,t1.INBOUND,t1.OUTBOUND, t2.USERNAME,t2.USER_ACCT,t2.AGENT_PHONE,t1.GROUP_LIST,t3.IDX_ORDER,t2.USER_STATE CENTER_USER_STATE from ");
		sql.append(getTableName("CC_BUSI_USER t1, "));
		sql.append("CC_USER t2, ");
		sql.append(getTableName("CC_SKILL_GROUP_USER t3 "));
		
		sql.append("LEFT JOIN " + this.getTableName("C_CF_WORKGROUP") + " T4 ON T3.WORK_GROUP_ID = T4.ID");
		
		sql.append(" where t1.USER_ID = t2.USER_ID and t2.ADMIN_FLAG = 0 and t1.USER_ID = t3.USER_ID ");
		sql.append(getBusiOrderId()," and t1.BUSI_ORDER_ID = ? " );
		sql.append(getEntId(), " and t1.ENT_ID = ? ");
		sql.append(param.getString("groupId")," and t3.SKILL_GROUP_ID = ?");
		sql.append(param.getString("workGroupId"), " and t3.WORK_GROUP_ID = ? ");
		sql.append(" order by t3.IDX_ORDER, t2.AGENT_PHONE");
		return this.queryForPageList(sql.getSQL(), sql.getParams(),null);
	}
	
	@WebControl(name="groupDict",type=Types.DICT)
	public JSONObject groupDict(){
		EasySQL sql = this.getEasySQL("select SKILL_GROUP_ID,SKILL_GROUP_NAME from "+getTableName("CC_SKILL_GROUP")+" where 1=1 ");
		sql.append(this.getBusiOrderId()," and BUSI_ORDER_ID = ? " );
		sql.append(this.getEntId(), " and ENT_ID = ? ");
		
		String allGroup=param.getString("allGroup");
		
		if(!"1".equals(allGroup)){
			YCUserPrincipal user = this.getUserPrincipal();
			if(user.getRoleType() == Constants.ROLE_TYPE_3){				//座席
				sql.append(" and SKILL_GROUP_ID = -1");
			}else if(user.getRoleType() == Constants.ROLE_TYPE_2){			//班长
				String skillGroupId = getSkillGroupId();
				if(StringUtils.isNotBlank(skillGroupId)){
					sql.append(skillGroupId, " and SKILL_GROUP_ID = ?","-1");
				}else{
					sql.append(skillGroupId, " and SKILL_GROUP_ID = -1");
				}
			}
		}
		sql.append(" order by IDX_ORDER");
		return this.getDictByQuery(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="groupChannelList",type=Types.LIST)
	public JSONObject groupChannelList(){
		EasySQL sql = this.getEasySQL("select t1.SKILL_GROUP_ID,t1.CHANNEL_ID,t1.KEY_ID,t2.CHANNEL_NAME,t2.CHANNEL_TYPE,t3.KEY_CODE,t3.KEY_NAME,t3.KEY_TYPE from ");
		sql.append(getTableName("CC_SKILL_GROUP_CHANNEL t1, "));
		sql.append("CC_CHANNEL t2,CC_CHANNEL_KEY t3");
		sql.append(" where t1.CHANNEL_ID = t2.CHANNEL_ID and t1.KEY_ID = t3.KEY_ID ");
		sql.append(getBusiOrderId()," and t1.BUSI_ORDER_ID = ? " );
		sql.append(getEntId(), " and t1.ENT_ID = ? ");
		sql.append(param.getString("skillGroupId")," and t1.SKILL_GROUP_ID = ?");
		return this.queryForList(sql.getSQL(), sql.getParams(),null);
	}
	
	
	/**
	 * 用户选择列表
	 * @return
	 */
	@WebControl(name="agentListSelect",type=Types.LIST)
	public  JSONObject agentListSelect(){
		EasySQL sql=this.getEasySQL("select t1.USER_ID,t1.AGENT_PHONE,t2.AGENT_NAME,t2.GROUP_LIST,t2.ROLE_LIST,t1.USER_ACCT from CC_USER t1,");
		sql.append(getTableName("cc_busi_user t2"));
		sql.append(" where t1.USER_STATE =0 and t1.USER_ID = t2.USER_ID and t2.USER_STATE = 0 and t1.ADMIN_FLAG = 0");
		sql.append(this.getEntId()," and t2.ENT_ID = ? ");
		sql.append(this.getBusiOrderId()," and t2.BUSI_ORDER_ID = ? ");
		sql.appendLike(param.getString("agentPhone")," and t1.AGENT_PHONE like ? ");
		sql.appendLike(param.getString("agentName"), "and t2.AGENT_NAME like ?");
		sql.appendLike(param.getString("userAcct"), "and t1.USER_ACCT like ?");
		sql.append(" and t1.USER_ID not in (select USER_ID from ");
		sql.append(getTableName("CC_SKILL_GROUP_USER"));
		sql.append(" where 1=1");
		sql.append(this.getEntId(), " and ENT_ID = ?");
		sql.append(this.getBusiOrderId(), " and BUSI_ORDER_ID = ?");
		sql.append(param.getString("groupId"), "and SKILL_GROUP_ID = ?");
		sql.append(") order by t1.AGENT_PHONE");
		return queryForPageList(sql.getSQL(),sql.getParams());
	}
	
	
	/**
	 * 部门用户选择列表
	 * @return
	 */
	@WebControl(name="agentListGroupSelect",type=Types.LIST)
	public  JSONObject agentListGroupSelect(){
		String grouSql="select SKILL_GROUP_ID from "+getTableName("CC_SKILL_GROUP")+" where SKILL_GROUP_TYPE = ? ";
		String[] stringArray=null;
		try {
			List<EasyRow> list=this.getQuery().queryForList(grouSql,new Object[]{Constants.STRUCT});
			stringArray=new String[list.size()];
			for (int i = 0; i < list.size(); i++) {
				stringArray[i]=list.get(i).getColumnValue("SKILL_GROUP_ID");
			}
		} catch (SQLException e) {
			e.printStackTrace();
		}
		EasySQL sql=this.getEasySQL("select distinct t1.USER_ID,t1.AGENT_PHONE,t1.USERNAME AGENT_NAME,t3.DEPT_NAME as GROUP_LIST,");
		sql.append(" t2.ROLE_LIST,t1.USER_ACCT from CC_USER t1  ");
		sql.append(" left join ").append(getTableName("cc_busi_user")).append( " t2 on t1.USER_ID = t2.USER_ID ");
		sql.append(this.getBusiOrderId(), " and t2.BUSI_ORDER_ID = ? ");
		sql.append(" and t2.USER_STATE = 0 ");
		sql.append(" left join "+getTableName("v_cc_dept_user")+" t3 on t1.USER_ID=t3.USER_ID");
		
		//sql.append(" where t1.USER_STATE =0  and t2.USER_STATE = 0 and t1.ADMIN_FLAG = 0");
		sql.append(" where t1.USER_STATE =0 and t1.ADMIN_FLAG = 0");
		sql.append(this.getEntId()," and t1.ENT_ID = ? ");
		sql.append(this.getBusiOrderId()," AND t2.BUSI_ORDER_ID = ? ");
		//sql.append(this.getBusiOrderId()," and t2.BUSI_ORDER_ID = ? ");
		sql.appendLike(param.getString("agentPhone")," and t1.AGENT_PHONE like ? ");
		sql.appendLike(param.getString("agentName"), "and t1.USERNAME like ?");
		sql.appendLike(param.getString("userAcct"), "and t1.USER_ACCT like ?");
		sql.append(" and t1.USER_ID not in (select USER_ID from ");
		sql.append(getTableName("CC_SKILL_GROUP_USER"));
		sql.append(" where 1=1");
		sql.append(this.getEntId(), " and ENT_ID = ?");
		sql.append(this.getBusiOrderId(), " and BUSI_ORDER_ID = ?");
		//根据配置项的设置，判断是否允许同一工号属于多个部门
		
		if(CEConstants.getCcBaseContext().getProperty(Constants.MULTI_DEPT, "").equals(Constants.MULTI_DEPT_CLOSE)) {
			sql.append(" and "+StringUtil.joinSql("SKILL_GROUP_ID",stringArray)+" )");
		}else {
			sql.append(param.getString("groupId")," and SKILL_GROUP_ID = ? )");
		}
		/*EasySQL sql=this.getEasySQL("select t1.USER_ID,t1.AGENT_PHONE,t2.AGENT_NAME,t3.DEPT_NAME as GROUP_LIST,");
		sql.append(" t2.ROLE_LIST,t1.USER_ACCT from CC_USER t1  ");
		sql.append(" join ").append(getTableName("cc_busi_user")).append( " t2 on t1.USER_ID = t2.USER_ID ");
		sql.append(" left join "+getTableName("v_cc_dept_user")+" t3 on t1.USER_ID=t3.USER_ID");
		
		sql.append(" where t1.USER_STATE =0  and t2.USER_STATE = 0 and t1.ADMIN_FLAG = 0");
		sql.append(this.getEntId()," and t2.ENT_ID = ? ");
		sql.append(this.getBusiOrderId()," and t2.BUSI_ORDER_ID = ? ");
		sql.appendLike(param.getString("agentPhone")," and t1.AGENT_PHONE like ? ");
		sql.appendLike(param.getString("agentName"), "and t2.AGENT_NAME like ?");
		sql.appendLike(param.getString("userAcct"), "and t1.USER_ACCT like ?");
		sql.append(" and t1.USER_ID not in (select USER_ID from ");
		sql.append(getTableName("CC_SKILL_GROUP_USER"));
		sql.append(" where 1=1");
		sql.append(this.getEntId(), " and ENT_ID = ?");
		sql.append(this.getBusiOrderId(), " and BUSI_ORDER_ID = ?");*/
		/*sql.append(param.getString("groupId"), "and SKILL_GROUP_ID = ?");*/
		/*sql.append(" and SKILL_GROUP_ID "+StringUtils.joinSql(stringArray));
		sql.append(") order by t1.AGENT_PHONE");*/
		sql.append(" order by t1.AGENT_PHONE");
		return queryForPageList(sql.getSQL(),sql.getParams());
	}
	
	/**
	 * 技能组tree
	 * @return
	 * @throws SQLException
	 */
	@InfAuthCheck(resId = "cc-base-org-jnzgl")
	@WebControl(name="skillTree", type=Types.TREE)
	public JSONObject skillTree() throws SQLException{
		JSONObject root = new JSONObject();
		JSONObject root2 = new JSONObject();
		root.put("id", 0);
		root.put("open", true);
		//update 20190909 不获取父id
		String pk=this.param.getString("pk");
		
		JSONArray tree = new JSONArray();
		//获取配置项设置
		String property = CEConstants.getCcBaseContext().getProperty(Constants.SKILL_GROUP_OPEN_TYPE, "");
		if(property.equals(Constants.LEVEL)) {
			root.put("name",getI18nValue("语音技能组"));
			root.put("skillGroupType","voice");
			root.put("groupType",Constants.GROUP_TYPE_SKILL_CALL);
			root.put("id", 1);
			root2.put("name",getI18nValue("全媒体技能组"));
			root2.put("skillGroupType","media");
			root2.put("groupType",Constants.GROUP_TYPE_SKILL_MEDIA);
			root2.put("id", 2);
			root2.put("open", true);
			List<JSONObject> jsonArray = getTreeJson();
			root.put("children", skillNext("1", jsonArray,pk));
			root2.put("children", skillNext("2", jsonArray,pk));
			tree.add(root);
			tree.add(root2);
		}else {
			List<JSONObject> jsonArray = getTreeJson();
			root.put("name",getI18nValue("技能组"));
			root.put("groupType",Constants.GROUP_TYPE_SKILL_GROUP);
			root.put("children", skillNext("0", jsonArray,pk));
			tree.add(root);
		}
		
		JSONObject j = getTree(tree);
		j.put("prop", property);
		return j;
	}
	
	/**
	 * 查询技能组tree数据
	 * @return
	 */
	private List<JSONObject> getTreeJson() {
		EasySQL sql = this.getEasySQL("select SKILL_GROUP_ID,SKILL_GROUP_NAME,P_GROUP_ID,GROUP_TYPE,SKILL_GROUP_CODE,SKILL_GROUP_TYPE,EXT_CONF from ");
		sql.append(getTableName("CC_SKILL_GROUP"));
		sql.append(" where 1=1 ");
//		sql.appendRLike(this.param.getString("groupType")," and GROUP_TYPE like ?");
		sql.append(this.getEntId(), "and ENT_ID = ? ");
		sql.append(this.getBusiOrderId(),"and BUSI_ORDER_ID = ? ");
		
		//update by lj 20191125,用GROUP_TYPE判断，无法满足12345，必须使用SKILL_GROUP_TYPE判断
		sql.append("struct", "and SKILL_GROUP_TYPE !=? ");
		
		sql.append("order by IDX_ORDER");
		List<JSONObject> jsonArray = null;
		try {
			jsonArray = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"查询技能组sql出错");
		}
		return jsonArray;
	}

	/**
	 * 获取部门对象
	 * @return
	 */
	@WebControl(name="skillInfo",type=Types.RECORD)
	public JSONObject skillInfo(){
		EasySQL sql = this.getEasySQL("select t1.*,t2.GROUP_TYPE_NAME,t2.CALL_FLAG from");
		sql.append(this.getTableName("CC_SKILL_GROUP t1"));
		sql.append("left join").append(this.getTableName("CC_GROUP_TYPE")).append("t2 on t1.GROUP_TYPE = t2.GROUP_TYPE");
		sql.append("where 1=1");
		sql.append(param.getString("pk"), "and t1.SKILL_GROUP_ID = ?",false);
		JSONObject jsonObject=this.queryForRecord(sql.getSQL(), sql.getParams(), null);
		JSONObject json=jsonObject.getJSONObject("data");
		if(json.getJSONObject("EXT_CONF")!=null){
			json.put("SPILL_GROUP",json.getJSONObject("EXT_CONF").getString("SPILL_GROUP"));
			json.put("MGR_TYPE",json.getJSONObject("EXT_CONF").getString("MGR_TYPE"));
			jsonObject.remove("data");
		}
		jsonObject.put("data",json);
		return jsonObject;
	}
	
	
	/*
	 * 获取部门成员
	 */
	@InfAuthCheck(resId = "cc-base-org-jnzgl")
	@WebControl(name="skillRecord",type=Types.RECORD)
	public  JSONObject skillRecord(){
		String id = param.getString("pk");
		if("0".equals(id)){
			id = null;
		}
		
		//技能组类型：技能组类型，voice 语音  media 全媒体 leader 总监 team团队 struct 部门
		String skillGroupType = param.getString("skillGroupType");
		
		EasySQL sql = this.getEasySQL("select distinct t1.USER_ID,t1.GROUP_LIST,t1.OUTBOUND,t1.INBOUND,t1.AGENT_NAME,t1.ROLE_ID,t1.USER_STATE,t2.MOBILE,t1.PREFIX_NUM, t2.USERNAME,t2.USER_ACCT,t3.IS_LEADER, ");
		sql.append("t2.AGENT_PHONE,t2.USER_STATE CENTER_USER_STATE,t3.IDX_ORDER,t4.SKILL_GROUP_NAME,t4.SKILL_GROUP_ID,t4.BUSI_ORDER_ID,t1.ROLE_LIST from ");
		sql.append(getTableName("CC_BUSI_USER t1, "));
		sql.append("CC_USER t2, ");
		sql.append(getTableName("CC_SKILL_GROUP_USER t3,"));
		sql.append(getTableName("CC_SKILL_GROUP t4"));
		sql.append(" where t1.USER_ID = t2.USER_ID and t2.ADMIN_FLAG = 0 and t2.USER_STATE =0 and t1.BUSI_ORDER_ID = t3.BUSI_ORDER_ID and t1.USER_ID = t3.USER_ID and t3.SKILL_GROUP_ID = t4.SKILL_GROUP_ID  ");
		sql.append(getBusiOrderId()," and t1.BUSI_ORDER_ID = ? " );
		/*sql.append(this.param.getString("groupType")," and t4.GROUP_TYPE = ?");*/
		
		if(Constants.SKILL_GROUP_TYPE_STRUCT.equals(skillGroupType)){
			sql.append(Constants.SKILL_GROUP_TYPE_STRUCT," and t4.SKILL_GROUP_TYPE = ? " );
		}else{
			sql.append(Constants.SKILL_GROUP_TYPE_STRUCT," and t4.SKILL_GROUP_TYPE != ? " );
		}
		
		sql.append(getEntId(), " and t1.ENT_ID = ? ");
		sql.append(" and t3.SKILL_GROUP_ID = '"+id+"'");
		sql.append(param.getString("workGroupId"), "and t3.WORK_GROUP_ID = ?");
		sql.appendLike(param.getString("condition"), " and t2.AGENT_PHONE like ? ");
		sql.appendLike(param.getString("agentName"), "and t1.AGENT_NAME like ?");
		sql.appendLike(param.getString("userAcct"), "and t2.USER_ACCT like ?");
		sql.appendLike(param.getString("agentPhone"), "and t2.AGENT_PHONE like ?");
		sql.append(" order by t2.AGENT_PHONE,t3.IDX_ORDER ");
		
		logger.info(sql.getSQL()+","+JSON.toJSONString(sql.getParams()));
		
		return this.queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	/**
	 * 获取下级部门
	 * @param pId
	 * @param skillList
	 * @return
	 */
	private JSONArray skillNext(String pId,  List<JSONObject> skillList,String pk){
		JSONArray teamChildren = new JSONArray();
		for (JSONObject skill : skillList) {
			if(skill.getString("P_GROUP_ID").equals(pId)){
				String code=skill.getString("SKILL_GROUP_CODE");
				if(code.equals(pk)){
					skill.put("checked", true);
				}
				skill.put("name", skill.getString("SKILL_GROUP_NAME"));
				skill.put("id", skill.getString("SKILL_GROUP_ID"));
				skill.put("open", true);
				skill.put("skillGroupCode", skill.getString("SKILL_GROUP_CODE"));
				skill.put("children", skillNext(skill.getString("SKILL_GROUP_ID"), skillList,pk));
				skill.put("skillGroupType", skill.getString("SKILL_GROUP_TYPE"));
				skill.put("groupType", skill.getString("GROUP_TYPE"));
				teamChildren.add(skill);
			}
		}
		return teamChildren;
	}
	
	/**
	 * 技能组关联工作组
	 * @return
	 */
	@WebControl(name="groupGroupList",type=Types.LIST)
	public JSONObject groupGroupList(){
		String id = param.getString("pk");
		if("0".equals(id)){
			id = null;
		}
		
		EasySQL sql = this.getEasySQL("SELECT T2.NAME WORK_GROUP_NAME,T1.PRIORITY,T1.ID,T1.WORKGROUP_ID,");
		sql.append("(SELECT COUNT(1) FROM " + this.getTableName("C_CF_WORKGROUP_USER") + " T3 WHERE T3.WORKGROUP_ID = T1.WORKGROUP_ID) WORK_GROUP_USER_COUNT,");
		sql.append("(SELECT COUNT(1) FROM " + this.getTableName("CC_SKILL_GROUP_USER") + " T4 WHERE T4.WORK_GROUP_ID = T1.WORKGROUP_ID AND T4.SKILL_GROUP_ID = T1.SKILLGROUP_ID) SKILL_WORK_USER_COUNT");
		sql.append("FROM " + getTableName("C_CF_WORKGROUP_SKILL") + " T1");
		sql.append("LEFT JOIN " + this.getTableName("C_CF_WORKGROUP") + " T2 ON T1.WORKGROUP_ID = T2.ID");
		sql.append("WHERE 1 = 1");

		sql.append(getBusiOrderId()," and t1.BUSI_ORDER_ID = ? " );
		
		sql.append(getEntId(), " and t1.ENT_ID = ? ");
		sql.append(id, "AND T1.SKILLGROUP_ID = ?", false);
		sql.appendLike(param.getString("workGroupName"), "and t2.NAME like ?");
		sql.append("ORDER BY T1.PRIORITY,T2.SORT_NUM");
		
		logger.info(sql.getSQL()+","+JSON.toJSONString(sql.getParams()));
		
		return this.queryForPageList(sql.getSQL(), sql.getParams());
	}
	
}
