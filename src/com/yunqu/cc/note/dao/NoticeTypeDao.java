package com.yunqu.cc.note.dao;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.note.base.AppDaoContext;
import com.yunqu.cc.note.base.CommonLogger;
/**
 * Title:类型管理
 * Description:获取类型管理各项业务信息操作
 * <AUTHOR>
 * @version 1.0
 */
@WebObject(name = "noticeType")
public class NoticeTypeDao extends AppDaoContext {
	/**
	 * 获取类型管理列表信息
	 *
	 */
	@WebControl(name="noticeTypeList",type=Types.LIST)
	public JSONObject noticeTypeList(){
		EasySQL easySql = this.getEasySQL("select g.* from "+this.getTableName("C_NOTES_TYPE")+" g where 1=1");
		easySql.append(getEntId()," and g.EP_CODE=? ");
		easySql.append(this.getBusiOrderId()," and g.BUSI_ORDER_ID=? ");
		easySql.appendRLike(param.getString("TYPE_NAME"), "and g.TYPE_NAME like ?");
		if(ServerContext.isDebug()) {
			CommonLogger.logger.info(CommonUtil.getClassNameAndMethod(this)+">>sql:"+easySql.getSQL()+";params>>"+ easySql.getParams());
		}
		JSONObject queryForPageList = this.queryForPageList(easySql.getSQL(), easySql.getParams(),null);
		return queryForPageList;
	}
	/**
	 * 获取类型管理详情
	 *
	 */
	@WebControl(name="getNoticeType",type=Types.RECORD)
	public  JSONObject getNoticeType(){
		return this.queryForRecord(new EasyRecord(this.getTableName("C_NOTES_TYPE"), "NOTICE_TYPE_ID").setPrimaryValues(param.getString("noticeType.NOTICE_TYPE_ID")));

	}
	/**
	 * 获取类型管理下拉框
	 *
	 */
	@WebControl(name="noticeTypeDict",type=Types.DICT)
	public JSONObject noticeTypeDict(){
		return getDictByQuery("select g.NOTICE_TYPE_ID,g.TYPE_NAME from "+this.getTableName("C_NOTES_TYPE")+" g where 1=1 AND g.EP_CODE=? AND BUSI_ORDER_ID=?",new Object[] {getEntId(),this.getBusiOrderId()});
	}
}
