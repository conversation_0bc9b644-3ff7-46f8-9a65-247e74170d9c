package com.yunqu.cc.note.servlet;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.note.base.AppBaseServlet;
import com.yunqu.cc.note.base.CommonLogger;
import com.yunqu.cc.note.base.QueryFactory;
import com.yunqu.cc.note.model.NoticeGroupAndMember;
/**
 * Title:群组管理
 * Description:执行群组管理各项操作
 * <AUTHOR>
 * @version 1.0
 */
@WebServlet("/servlet/noticeGroup/*")
public class NoticeGroupServlet extends AppBaseServlet {

	private static final long serialVersionUID = 7467895634123782087L;
	private Logger logger = CommonLogger.logger;
	
	/**
	 * 跳转人员页面
	 *
	 */
	public String actionForGroup() {
	    HttpServletRequest request = getRequest();
	    String enable = request.getParameter("enable") == null ? "false" : request.getParameter("enable");
	    String expandAll = request.getParameter("expandAll") == null ? "false" : request.getParameter("expandAll");
	    String oldIds = request.getParameter("oldIds") == null ? "" : request.getParameter("oldIds");
	    String single = request.getParameter("single") == null ? "false" : request.getParameter("single");
	    String oldName = request.getParameter("oldName") == null ? "" : request.getParameter("oldName");
	    String type = request.getParameter("type") == null ? "" : request.getParameter("type");
	    setAttr("oldNames", oldName);
	    setAttr("single", single);
	    setAttr("expandAll", expandAll);
	    setAttr("oldIds", oldIds);
	    setAttr("enable", enable);
	    setAttr("type", type);
	    return "/pages/federal/noticeGroup.jsp";
	}
	/**
	 * 
	 * 加载已选中人
	 */
	  public EasyResult actionForGetMember(){
		EasyQuery query = QueryFactory.getWriteQuery();
	    JSONObject entInfo = getJSONObject();
	    String ids = entInfo.getString("ids");
	    if(!"".equals(ids)){
	    	String[] id = ids.split(",");
	    	String memberAgentId = "";
	    	String agentName = "";
	    	for (String s : id) {
	    		memberAgentId += "'" + s + "',";
	    	}
	    	memberAgentId = memberAgentId.substring(0, memberAgentId.length()-1);
	    	List<EasyRow> list = new ArrayList<EasyRow>();
	    	try {
	    		list = query.queryForList("SELECT DISTINCT t.AGENT_ID,t.AGENT_NAME FROM "+this.getTableName("C_NOTES_GROUP_MEMBER")+" t  WHERE t.AGENT_ID IN (" + memberAgentId + ") ");
	    		for (int i=0;i<list.size();i++) { 
	    			EasyRow row = list.get(i);
	    			agentName +=  row.getColumnValue("AGENT_NAME").toString() + "," ;
	    		}
	    	}
	    	catch (Exception e) {
	    		e.printStackTrace();
	    		logger.error(CommonUtil.getClassNameAndMethod(this)+"获取失败，原因："+e.getMessage(),e);
	    	}
	    	if(agentName != null && !agentName.equals("")){
	    		agentName = agentName.substring(0, agentName.length()-1);
	    	}
	    	return (EasyResult)EasyResult.ok(agentName);
	    }
	    return (EasyResult)EasyResult.ok(null);
	  }
	/**
	 * 添加群组
	 * 
	 * @return
	 */
	public EasyResult actionForAdd() {
		EasyResult result = new EasyResult();
		EasyQuery query = QueryFactory.getWriteQuery();
		try {
			JSONObject noticeGroup = this.getJSONObject("noticeGroup");
			UserModel userModel = UserUtil.getUser(getRequest());
			query.begin();
			String noticeGroupId = RandomKit.randomStr();
			
			//验证数据
			if(checkTargetName(noticeGroup.getString("T_GROUP_NAME"),noticeGroupId) > 0) {
				try {
					query.roolback();
				} catch (SQLException e) {}
				
				return EasyResult.fail("该目标名称已存在，请重新输入");
			}
			
			noticeGroup.put("T_GROUP_ID", noticeGroupId);
			noticeGroup.put("CREATE_TIME", EasyDate.getCurrentDateString());
			noticeGroup.put("CREATE_ACC", userModel.getUserAcc());
			noticeGroup.put("CREATE_NO", userModel.getUserNo());
			noticeGroup.put("CREATE_NAME", userModel.getUserName());
			noticeGroup.put("CREATE_DEPT", userModel.getDept().getDeptCode());
			noticeGroup.put("CREATE_DEPT_NAME",userModel.getDept().getDeptName());
			noticeGroup.put("ENT_ID", userModel.getEpCode());
			noticeGroup.put("BUSI_ORDER_ID",this.getBusiOrderId());
			EasyRecord record = new EasyRecord(this.getTableName("C_NOTES_GROUP"), "T_GROUP_ID").setColumns(noticeGroup);
			query.save(record);
			JSONObject jsonStr = getJSONObject();
			String agentId = jsonStr.getString("AGENT_ID");
			String agentName = jsonStr.getString("AGENT_NAME");
			String userId = jsonStr.getString("USER_ID");
			savaMember(agentId, agentName, userId, noticeGroupId,query);
			query.commit();
			result.setSuccess(null, "添加成功！");
		} catch (Exception e) {
			try {
				query.roolback();
			} catch (Exception e1) {
				this.error("添加失败，原因：" + e.getMessage(), e1);
				logger.error(CommonUtil.getClassNameAndMethod(this)+"添加群组失败，原因："+e.getMessage(),e1);
				result.addFail("添加失败，原因：" + e1.getMessage());
			}
			this.error("添加失败，原因：" + e.getMessage(), e);
			logger.error(CommonUtil.getClassNameAndMethod(this)+"添加失败，原因："+e.getMessage(),e);
			result.addFail("添加失败，原因：" + e.getMessage());
		}
		return result;
	}

	/**
	 * 修改群组
	 * 
	 * @return
	 */
	public EasyResult actionForUpdate() {
		EasyResult result = new EasyResult();
		EasyQuery query = QueryFactory.getWriteQuery();
		JSONObject noticeGroup = this.getJSONObject("noticeGroup");
		try {
			
			//验证数据
			if(checkTargetName(noticeGroup.getString("T_GROUP_NAME"),noticeGroup.getString("T_GROUP_ID")) > 0) {
				return EasyResult.fail("该目标名称已存在，请重新输入");
			}
			
			EasyRecord easy = new EasyRecord(this.getTableName("C_NOTES_GROUP"),"T_GROUP_ID").setColumns(noticeGroup);
			query.begin();
			query.update(easy);			
			JSONObject jsonStr = getJSONObject();
			String agentId = jsonStr.getString("AGENT_ID");
			String agentName = jsonStr.getString("AGENT_NAME");	
			String userId = jsonStr.getString("USER_ID");
			String noticeGroupId = noticeGroup.getString("T_GROUP_ID");			
			JSONObject json = new JSONObject();
			json.put("T_GROUP_ID", noticeGroupId);
			EasyRecord recordType = new EasyRecord(this.getTableName("C_NOTES_GROUP_MEMBER"),"T_GROUP_ID").setColumns(json);
			this.getQuery().deleteById(recordType);
			savaMember(agentId, agentName, userId, noticeGroupId,query);
			query.commit();
			result.setSuccess(null, "修改成功！");
		} catch (Exception e) {
			try {
				query.roolback();
			} catch (Exception e1) {
				this.error("修改失败，原因：" + e.getMessage(), e1);
				logger.error(CommonUtil.getClassNameAndMethod(this)+"修改群组失败，原因："+e.getMessage(),e);
				result.addFail("修改失败，原因：" + e1.getMessage());
			}
			this.error("修改失败，原因：" + e.getMessage(), e);
			logger.error(CommonUtil.getClassNameAndMethod(this)+"修改失败，原因：" + e.getMessage(), e);
			result.addFail("修改失败，原因：" + e.getMessage());
		}
		return result;
	}
	/**
	 * 新增人员插入
	 * 
	 * @return
	 */
	private void savaMember(String agentId, String agentName, String userId, String noticeGroupId,EasyQuery query) throws SQLException {
		if (agentId != null && !agentId.equals("") && agentName != null && !agentName.equals("")) {
			String[] agentIdStr = agentId.split(",");
			String[] agentNameStr = agentName.split(",");
			String[] userIdStr = userId.split(",");
			UserModel userModel = UserUtil.getUser(getRequest());
			for (int i = 0; i < agentIdStr.length; i++) {
				JSONObject noticeGroupMember = new JSONObject();
				noticeGroupMember.put("ID", RandomKit.randomStr());
				noticeGroupMember.put("T_GROUP_ID", noticeGroupId);
				noticeGroupMember.put("AGENT_NO", "");
				noticeGroupMember.put("AGENT_ID", agentIdStr[i]);
				noticeGroupMember.put("AGENT_NAME", agentNameStr[i]);
				noticeGroupMember.put("USER_ID", userIdStr[i]);
				noticeGroupMember.put("ENT_ID", userModel.getEpCode());
				noticeGroupMember.put("BUSI_ORDER_ID",this.getBusiOrderId());
				EasyRecord recordType = new EasyRecord(this.getTableName("C_NOTES_GROUP_MEMBER"),"T_GROUP_ID").setColumns(noticeGroupMember);
				query.save(recordType);
			}
		}
	}
	/**
	 * 获取选中人员
	 *
	 */
	public EasyResult actionForGetGroupMember(){
		EasyResult result = new EasyResult();
		try {
			String noticeGroupId = this.getRequest().getParameter("noticeGroupId");
			String sql = "SELECT m.*, u.USERNAME USER_NAME, u.USER_ACCT USERACCT, u.USER_ID USERID FROM "+this.getTableName("C_NOTES_GROUP_MEMBER")+ " m left join "+ "cc_user" + " u on u.USER_ID = m.USER_ID" +" WHERE T_GROUP_ID = '" + noticeGroupId + "'";
			List<JSONObject> rangs = this.getQuery().queryForList(sql, null,new JSONMapperImpl());
			result.setData(rangs);
		} catch (Exception e) {
			logger.error("获取失败，原因：" + e.getMessage(), e);
			result.addFail("获取失败，原因：" + e.getMessage());
		}
		return result;
	}
	/**
	 * 获取选中人员
	 *
	 */
	public EasyResult actionForGetGroupAndMember(){
		EasyResult result = new EasyResult();
		try {
			String groupSql = "SELECT * FROM "+this.getTableName("C_NOTES_GROUP")+"";
			List<JSONObject> groupList = this.getQuery().queryForList(groupSql, null,new JSONMapperImpl());
			List<NoticeGroupAndMember> list = new ArrayList<NoticeGroupAndMember>();
			for(int i=0;i<groupList.size();i++){
				String noticeGroupId = (String) groupList.get(i).get("T_GROUP_ID");
				NoticeGroupAndMember noticeGroupAndMember = new NoticeGroupAndMember();
				noticeGroupAndMember.setId(noticeGroupId);
				noticeGroupAndMember.setpId("0");
				noticeGroupAndMember.setName((String) groupList.get(i).get("T_GROUP_NAME"));
				noticeGroupAndMember.setNocheck(true);
				list.add(noticeGroupAndMember);
				String sql = "SELECT * FROM "+this.getTableName("C_NOTES_GROUP_MEMBER")+" WHERE T_GROUP_ID = '" + noticeGroupId + "'";
				List<JSONObject> rangs = this.getQuery().queryForList(sql, null,new JSONMapperImpl());
				for(int j = 0;j<rangs.size();j++){
					NoticeGroupAndMember noticeGroupAndMemberSub = new NoticeGroupAndMember();
					noticeGroupAndMemberSub.setId((String) rangs.get(j).get("AGENT_ID"));
					noticeGroupAndMemberSub.setpId(noticeGroupId);
					noticeGroupAndMemberSub.setName((String) rangs.get(j).get("AGENT_NAME"));
					noticeGroupAndMemberSub.setNocheck(false);
					list.add(noticeGroupAndMemberSub);
				}
			}
			result.setData(list);
		} catch (Exception e) {
			logger.error("获取失败，原因：" + e.getMessage(), e);
			result.addFail("获取失败，原因：" + e.getMessage());
		}
		return result;
	}
	
	/**
	 * 删除群组及群组下的成员
	 * @return
	 */
	public EasyResult actionForDelete(){
		JSONObject json = new JSONObject();
		JSONObject obj=this.getJSONObject();
		JSONArray ids=obj.getJSONArray("ids");
		try {
			EasyRecord noticeGroup=null;
			EasyRecord noticeGroupMember=null;
			for(int i=0;i<ids.size();i++){
				json.put("T_GROUP_ID",ids.getString(i));
				noticeGroup = new EasyRecord(""+this.getTableName("C_NOTES_GROUP")+"","T_GROUP_ID").setColumns(json);
				noticeGroupMember = new EasyRecord(""+this.getTableName("C_NOTES_GROUP_MEMBER")+"","T_GROUP_ID").setColumns(json);
				getQuery().deleteById(noticeGroup);
				getQuery().deleteById(noticeGroupMember);
			}
			return EasyResult.ok("",getI18nValue("删除成功"));
		} catch (Exception e) {
			logger.error("删除失败，原因：" + e.getMessage(), e);
			return EasyResult.error(500, " " + e.getMessage());
		}
	}
	
	/**
	 * 群组管理名称验证
	 */
	public int checkTargetName(String name,String id) {
		EasySQL sql = new EasySQL("SELECT COUNT(1) FROM " + getTableName("C_NOTES_GROUP"));
		sql.append("WHERE 1=1");
		sql.append(id," AND T_GROUP_ID <> ?");
		sql.append(name,"AND T_GROUP_NAME = ?");
		int flag = 1;
		try {
			flag = this.getQuery().queryForInt(sql.getSQL(), sql.getParams());
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "群组管理名称是否唯一出错",e);
		}
		return flag;
	}
}
