package com.yunqu.yc.quality.service;

import java.sql.SQLException;
import java.util.Calendar;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.IBaseService;
import com.yq.busi.common.service.SchemaService;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.JsonUtil;
import com.yunqu.yc.quality.base.CommonLogger;
import com.yunqu.yc.quality.base.Constants;
import com.yunqu.yc.quality.base.QueryFactory;
import com.yunqu.yc.quality.base.ServiceCommand;
import com.yunqu.yc.quality.base.ServiceID;
import com.yunqu.yc.quality.handler.QcTaskServiceHandler;

public class ScanQcTaskService extends IBaseService {

	private static Logger logger = CommonLogger.getLogger("job-scan");
	
	//全局变量、如果上一个作业任务没有完成则不进入当前任务
	private static boolean IS_RUNNING = false;
	
	@Override
	public JSONObject invokeMethod(JSONObject params) throws ServiceException {
		String command = params.getString("command");
		if (!ServiceCommand.JOB_SERVICECMD_QC_EXTRACTION_DATA.equals(command)) {
			JSONObject result = JsonUtil.createInfRespJson(params);
			result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
			result.put("respDesc", "不存在的command！");
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 不存在的command,请检查:"+command);
			return result;
		}
		/*
		 //人工质检不判断智能质检开关
		 if(!StringUtils.equals("Y", Constants.IS_START_TASK_ZN_QC)) {
			//智能质检任务配置项IS_START_TASK_ZN_QC未开启
			JSONObject result = JsonUtil.createInfRespJson(params);
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "智能质检任务配置项IS_START_TASK_ZN_QC未开启");
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 智能质检任务配置项IS_START_TASK_ZN_QC未开启");
			return result;
		}*/
		JSONArray entArr = SchemaService.findEntSchema();
		if(ServerContext.isDebug()) {
			logger.info(CommonUtil.getClassNameAndMethod(this) + "本次执行抽取的企业为："  + (entArr == null ? "[]" : entArr.toJSONString()));
		}
		if(CommonUtil.listIsNotNull(entArr)) {
			//任务开关 true:打开  flase:关闭 
			if(!IS_RUNNING) {
				try {
					IS_RUNNING = true;
					for(int i=0;i<entArr.size();i++) {
						JSONObject obj = entArr.getJSONObject(i);
						scanQcTasks(obj.getString("SCHEMA_NAME"), obj.getString("ENT_ID"));
					}
				}catch (Exception e) {
					logger.info(CommonUtil.getClassNameAndMethod(this), e);
				}finally {
					IS_RUNNING = false;
				}
			}
		}
		JSONObject result = JsonUtil.createInfRespJson(params);
		result.put("respCode", GWConstants.RET_CODE_SUCCESS);
		result.put("respDesc", "处理完成");
		return result;
	}
	
	private void scanQcTasks(String schema, String entId) {
		String chTaskRun = "";
		
		
		chTaskRun = CacheUtil.get(Constants.CH_TASK_RUN);
		if(StringUtils.equals("Y", chTaskRun)) {
			logger.warn(CommonUtil.getClassNameAndMethod(this) + "有抽取任务在执行中,请稍后再试");
			return ;
		}
		//添加标记到缓存
		CacheUtil.put(Constants.CH_TASK_RUN, "Y", 30 * 60);
		
		QcTaskServiceHandler.scanData(schema, entId);
		
		//移除标记
		CacheUtil.delete(Constants.CH_TASK_RUN);
	}
	
	
	/**
	 * 是否在可运行时段
	 * @return
	 */
	public static JSONArray isCanRunTime () {
		
		JSONArray runArray = new JSONArray();
		Calendar calendar = Calendar.getInstance();
		int currHour = calendar.get(Calendar.HOUR_OF_DAY);
		String paramC = CacheUtil.get("CC-QUALITY-PARAM");
		//没有缓存时
		if(StringUtils.isBlank(paramC)) {
			loadCfgToCache();
			paramC = CacheUtil.get("CC-QUALITY-PARAM");
		}
		JSONArray arr = new JSONArray();
		if(StringUtils.isNotBlank(paramC)) {
			arr = JSONArray.parseArray(paramC);
		}
		//遍历每个企业
		for(int i = 0;i < arr.size();i++) {
			JSONObject obj = arr.getJSONObject(i);
			//判断
			String startTimeStr = obj.getString("RUN_TASK_START_TIME");
			String endTimeStr = obj.getString("RUN_TASK_END_TIME");
			if(StringUtils.isNotBlank(startTimeStr)&&StringUtils.isNotBlank(endTimeStr)){
				int sTime,eTime = 0;
				//有多组运行时间
				if(startTimeStr.length()>8) {
					String[] startArr = startTimeStr.split(",");
					String[] endArr = endTimeStr.split(",");
					for(int j=0;j<startArr.length;j++) {
						sTime = getHourNumber(startArr[j]);
						eTime = getHourNumber(endArr[j]);
						if(currHour >= sTime && currHour <= eTime){
							runArray.add(obj);
							break;
						}
					}
				}else {//只有一组运行时间
					sTime = getHourNumber(startTimeStr);
					eTime = getHourNumber(endTimeStr);
					if(currHour >= sTime && currHour <= eTime){
						runArray.add(obj);
					}
				}
			}else {
				runArray.add(obj);
			}

		}

		if(ServerContext.isDebug()) {
			logger.info(CommonUtil.getClassNameAndMethod(SendQcObjService.class) + "质检运行时段 >> " + runArray.toJSONString());
		}
		
		return runArray;
	}
	
	private static void loadCfgToCache() {
		EasyQuery query = QueryFactory.getReadQuery();
		JSONArray jsonArray = SchemaService.findEntBusiSchema();
		JSONArray toCache = new JSONArray();
		if(jsonArray != null) {
			for(int i=0;i<jsonArray.size();i++) {
				JSONObject obj = jsonArray.getJSONObject(i);
				JSONObject jsonObject=new JSONObject();
				EasySQL sql = new EasySQL("SELECT PARAM_CONFIG FROM " + obj.getString("SCHEMA_NAME")+".C_CF_ENT_PARAM");
				sql.append("WHERE 1=1");
				sql.append(Constants.APP_NAME,"AND LABEL_CODE = ?");
				sql.append(obj.getString("ENT_ID"),"AND ENT_ID = ?",false);
				sql.append(obj.getString("BUSI_ORDER_ID"),"AND BUSI_ORDER_ID = ?",false);
				try {
					JSONObject data = query.queryForRow(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
					if(data!=null) {
						JSONObject getData = data.getJSONObject("PARAM_CONFIG");
						if(getData!=null) {
							String startTimeStr = getData.getString("RUN_TASK_START_TIME");
							String endTimeStr = getData.getString("RUN_TASK_END_TIME");
							//如果配置了运行时间
							if(StringUtils.isNotBlank(startTimeStr)&&StringUtils.isNotBlank(endTimeStr)) {
								//根据"["和"]"判断是否为数组
								if("[".equals(String.valueOf(startTimeStr.charAt(0)))&&"]".equals(String.valueOf(startTimeStr.charAt(startTimeStr.length()-1)))) {
									startTimeStr = startTimeStr.replace("[", "").replace("]","").replace("\"","");
									endTimeStr = endTimeStr.replace("[", "").replace("]","").replace("\"","");
								}
								jsonObject.put("RUN_TASK_START_TIME", startTimeStr);
								jsonObject.put("RUN_TASK_END_TIME", endTimeStr);
							}
						}
					}
					jsonObject.put("schema",obj.getString("SCHEMA_NAME"));
					jsonObject.put("entId", obj.getString("ENT_ID"));
					toCache.add(jsonObject);
				} catch (SQLException e) {
					//continue;
				}
			}
			CacheUtil.put("CC-QUALITY-PARAM",toCache.toJSONString());
			
		}
	}
	
	private static int getHourNumber(String str) {
		String[] s = str.split(":"); 
		int hour = NumberUtils.toInt(s[0]);
		return hour;
	}

	@Override
	public String getServiceId() {
		return ServiceID.JOB_SERVICEID_QC_EXTRACTION_DATA;
	}

	@Override
	public String getName() {
		return "每十分钟抽取质检数据到质检池";
	}
}
