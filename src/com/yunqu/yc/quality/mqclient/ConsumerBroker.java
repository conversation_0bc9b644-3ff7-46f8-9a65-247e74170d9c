package com.yunqu.yc.quality.mqclient;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

import javax.jms.Message;
import javax.jms.MessageConsumer;

import org.apache.log4j.Logger;
import org.easitline.common.core.activemq.Broker;
import org.easitline.common.core.activemq.BrokerMessageListener;

import com.yq.busi.common.util.mq.MQBrokerUtil;
import com.yunqu.yc.quality.base.CommonLogger;
import com.yunqu.yc.quality.job.ThreadMgr;




/**
 * 
 * <AUTHOR>
 *
 */
public class ConsumerBroker implements Runnable {
	
	private Broker broker = null;
	
	private boolean isRunning = false;
	
	private boolean close = false;
	
	private BlockingQueue<Message> messageQueue = new LinkedBlockingQueue<Message>();
	
	private BrokerMessageListener brokerMessageListener;
	
	private String brokerName ;
	
	private ThreadGroup threadGroup;
	
	
	private int timer = 0;
	
	private MessageConsumer consumer = null;
	
	private static Logger logger = CommonLogger.getLogger("mq");
	
	public ConsumerBroker(String brokerName,BrokerMessageListener brokerMessageListener){
		this.brokerName = brokerName;
		this.brokerMessageListener = brokerMessageListener;
	}
	
	public  void closeBroker(){
		MQBrokerUtil.closeBroker(broker, consumer);
		
	}
	
	
	private void initBroker(){
		//如果已经退出状态，则不做任何的处理。
		if(ThreadMgr.getInstance().isClosed()) return;
		try {
			if(broker==null){
				broker =  MQBrokerUtil.getConsumerBroker(this.brokerName);
			}
		    //broker.registMessageListener(new CommandMessage());
		    consumer = broker.getContext().getConsumer();
		    if(consumer == null){
		    	logger.error("initBroker error,cause: getConsumer() is null!");
		    }
		    isRunning = true;
		} catch (Exception ex) {
			logger.error("PetraBroker.addConsumer() error,cause:"+ex.getMessage(),ex);
			logger.error(ex,ex);
			isRunning = false;
			closeBroker();
		}finally{
			try {
				Thread.sleep(3*1000);
			} catch (Exception ex) {
				logger.error(ex,ex);
			}
		} 
	}
	
	
	public boolean isClose(){
		return this.close;
	}
	
	@Override
	public void run() {
		//启动的时候等待系统启动完成
		try {
			Thread.sleep(10*1000);
		} catch (InterruptedException ex) {
			logger.error(ex,ex);
		}
		this.initThread();
		logger.info("threadsState.isRunning() << "+ ThreadMgr.getInstance().isRunning());
		
		while(true){
			//如果不在运行状态，关闭，并退出
			if(ThreadMgr.getInstance().isClosed()){
				this.closeBroker();
				this.close = true;
				return ;
			}
			
			if(!isRunning){
				this.closeBroker();
				this.initBroker();
			}
			
			try {
				if(consumer == null) {
					logger.warn("ConsumerBroker run error  , cause:  consumer init fail!" );
					isRunning = false;
					continue;
				}
				Message message = consumer.receive(5*1000);
				if(message == null){
					timer++;
					if(timer>=30){
						timer = 0;
						logger.info("ConsumerBroker.recevie("+this.brokerName+") <<  no message ..." );
					}
					continue;
				}
				//设定消息保护。
				if(messageQueue.size()>10000){
					continue;
				}
				messageQueue.put(message);
			} catch (Exception ex) {
				logger.info(ex,ex);
				//如果接收消息出现异常，则进行重连
				isRunning = false;
			}
		}
	}
	
	private void initThread(){
		threadGroup = new ThreadGroup(this.brokerName);
		for (int i = 0; i < 10; i++) {
			Thread  client = new Thread(this.threadGroup,new MessageListenerThread(i));
			client.start();
		}
	}
	
	/**
	 * 回调处理线程
	 */
	private  class MessageListenerThread implements Runnable{
		/**
		 * 线程ID
		 */
		private int threadId;
		
		public MessageListenerThread(int threadId){
			this.threadId = threadId;
		}
		@Override
		public void run() {
			while(ThreadMgr.getInstance().isRunning()){
				try {
					//从队列中获取通知的数据
					Message message = messageQueue.take();
//					ObjectMessage objMessage = (ObjectMessage) message;
//					logger.info("ConsumerBroker.recevie(text) << "+ objMessage.getObject());
					brokerMessageListener.onMessage(message);
				} catch (Exception ex) {
					logger.error(ex,ex);
					//防止死循环
					try {
						Thread.sleep(3000);
					} catch (InterruptedException ex1) {
						logger.error(ex1,ex1);
					}
				}
			}
		}
	}

}


